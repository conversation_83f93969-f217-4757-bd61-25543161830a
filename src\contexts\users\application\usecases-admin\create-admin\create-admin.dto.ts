import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  IsBoolean,
  IsNumber,
  IsNotEmpty,
  ValidateNested,
  IsArray,
  IsEnum,
  IsUUID,
} from 'class-validator';
import { CreateAddressDto } from '../../usecases-address/create-address/create-address.dto';
import { Type } from 'class-transformer';
import { CreateUserDto } from '../../usecases-user/create-user/create-user.dto';
import { ApiProperty } from '@nestjs/swagger';
import { AdminDocumentTypeEnum } from 'src/contexts/users/domain/entities/admin-documents.entity';

export class CreateAdminDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  company_name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  alias: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  // @Matches(
  //   /^[A-Z&Ñ]{3,4}[0-9]{2}(0[1-9]|1[012])(0[1-9]|[12][0-9]|3[01])[A-Z0-9]{2}[0-9A]$/,
  //   {message: 'El RFC no cumple con el formato adecuado.'}
  // )
  rfc: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  num_asigned_cards: number = 0;

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  is_sucursal: boolean;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  membership_number: number = null;

  @IsOptional()
  @IsString()
  manager: string;

  @ApiProperty()
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => CreateAddressDto)
  address: CreateAddressDto;

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => CreateUserDto)
  user: CreateUserDto;

  @ApiProperty()
  @IsNumber({
    maxDecimalPlaces: 3,
  })
  spei_in: number;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  managerId?: string;

  @ApiProperty()
  @IsNumber({
    maxDecimalPlaces: 3,
  })
  spei_out: number;

  @ApiProperty()
  @IsNumber()
  target_refound: number;

  @ApiProperty()
  @IsString()
  ambassador: string;

  @IsOptional()
  @IsNumber()
  group_id: number = null;

  @ApiProperty()
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AdminIds)
  enterprises: AdminIds[] | null;

  @ApiProperty()
  @IsOptional()
  @IsArray()
  @Type(() => file)
  files: file[];
}

class AdminIds {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  adminId: string;
}

class file {
  @ApiProperty()
  @IsString()
  file: string;

  @ApiProperty()
  @IsEnum(AdminDocumentTypeEnum)
  type: AdminDocumentTypeEnum;
}
