import { Controller, Put, Get, Body, Param, UseGuards } from '@nestjs/common';
import { UpdateNotificationPreferencesUseCase } from '../../application/update-notification-preferences/update-notification-preferences.usecase';
import { GetNotificationPreferencesUseCase } from '../../application/get-notification-preferences/get-notification-preferences.usecase';
/* ** Utils ** */
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';
import { UpdateNotificationPreferencesDto } from '../../application/update-notification-preferences/update-notification-preferences.dto';
import { JwtAuthGuard } from 'src/contexts/auth/guards/auth.guard';

@UseGuards(JwtAuthGuard)
@Controller('user-notifications/preferences')
export class UserNotificationPreferencesController {
  constructor(
    private readonly updatePreferencesUseCase: UpdateNotificationPreferencesUseCase,
    private readonly getPreferencesUseCase: GetNotificationPreferencesUseCase,
  ) {}

  /**
   * Endpoint para guardar o actualizar las preferencias de notificaciones de un usuario.
   * @param userId ID del usuario
   * @param preferences Preferencias de notificaciones
   */
  @Put(':userId')
  async savePreferences(
    @Param('userId') userId: string,
    @Body() preferences: UpdateNotificationPreferencesDto,
  ): Promise<ResponseUtil> {
    try {
      const updateNotificationPreferences =
        await this.updatePreferencesUseCase.execute(userId, preferences);

      return ResponseUtil.success(
        'Preferences updated successfully',
        { updateNotificationPreferences },
        200,
      );
    } catch (error) {
      return ResponseUtil.error(
        'Error updating preferences',
        {
          data: {
            statusCode: 400,
            message: String(error),
            error: 'Error updating preferences',
          },
        },
        400,
      );
    }
  }

  /**
   * Endpoint para obtener las preferencias de notificaciones de un usuario.
   * @param userId ID del usuario
   * @returns Preferencias de notificaciones del usuario
   */
  @Get(':userId')
  async getPreferences(@Param('userId') userId: string): Promise<any> {
    try {
      const notificationPreferences =
        await this.getPreferencesUseCase.execute(userId);

      return ResponseUtil.success(
        'Preferences retrieved successfully',
        { notificationPreferences },
        200,
      );
    } catch (error) {
      console.log(error);
      return ResponseUtil.error(
        'Error retrieving preferences',
        {
          data: {
            statusCode: 400,
            message: String(error),
            error: 'Error retrieving preferences',
          },
        },
        400,
      );
    }
  }
}
