import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

/* ** Entities ** */
import { Commissions } from '../domain/entities/commission.entity';

/* ** Modules ** */
import { CustomRedisStoreModule } from 'src/contexts/shared/modules/redis.module';
import { DockModule } from 'src/contexts/dock/module/dock.module';
import { UsersModule } from 'src/contexts/users/module/users.module';

/* ** Controllers ** */
import { CommissionController } from '../infrastructure/http/commission.controller';

/* ** Repositories ** */
import { CommissionRepositoryImpl } from '../infrastructure/repositories/commission.repository.impl';

/* ** Use Cases ** */
import { GetBalanceAccountUseCase } from '../application/get-balance-account/get-balance-account.usecase';
import { CommissionFetchUseCase } from '../application/commission-fetch/commission-fetch.usecase';

/* ** Utils ** */
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';

@Module({
  imports: [
    TypeOrmModule.forFeature([Commissions]),
    CustomRedisStoreModule,
    DockModule,
    UsersModule,
  ],
  controllers: [CommissionController],
  providers: [
    GetBalanceAccountUseCase,
    CommissionFetchUseCase,
    CommissionRepositoryImpl,
    authTokenDock,
  ],
  exports: [CommissionRepositoryImpl],
})
export class CommissionModule {}
