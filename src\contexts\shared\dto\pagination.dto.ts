import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsInt, IsNotEmpty, IsPositive, Min } from "class-validator";

export class PaginationDto {
    @ApiProperty({
        type: Number,
        default: 1,
        minimum: 1
    })
    @Type(() => Number)
    @IsInt()
    @IsPositive()
    @Min(1)
    @IsNotEmpty()
    page: number;

    @ApiProperty({
        type: Number,
        default: 5,
        minimum: 1
    })
    @Type(() => Number)
    @IsInt()
    @IsPositive()
    @Min(1)
    @IsNotEmpty()
    limit: number;
}