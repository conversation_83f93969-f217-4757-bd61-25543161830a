export interface LegalPersonDetail {
    person_id:     string;
    country_code:  string;
    status_id:     number;
    legal_name:    string;
    creation_date: Date;
    documents:     Document[];
    phones:        Document[];
    addresses:     Address[];
    emails:        Email[];
}

interface Address {
    id:            string;
    type_id:       number;
    is_main:       boolean;
    postal_code:   string;
    suffix:        string;
    street:        string;
    number:        string;
    city:          string;
    country_code:  string;
    creation_date: Date;
}

interface Document {
    id:            string;
    country_code:  string;
    type_id:       number;
    number:        string;
    is_main:       boolean;
    creation_date: Date;
    dialing_code?: string;
    area_code?:    string;
}

interface Email {
    id:            string;
    type_id:       number;
    is_main:       boolean;
    email:         string;
    creation_date: Date;
}
