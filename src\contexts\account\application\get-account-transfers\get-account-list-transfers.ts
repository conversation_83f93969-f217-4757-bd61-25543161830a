import { Injectable, BadRequestException } from '@nestjs/common';
import axios from 'axios';
import * as https from 'https';
import { ConfigService } from '@nestjs/config';
import { authTokenDock } from '../../../shared/utils/authDock/authDock';
import { GetUserAccountService } from '../get-user-account/get-user-account.service';
import { DataSource } from 'typeorm';

@Injectable()
export class GetListTransfersUseCase {
  constructor(
    private readonly authTokenDock: authTokenDock,
    private readonly configService: ConfigService,
    private readonly getUserAccountService: GetUserAccountService,
    private readonly dataSource: DataSource,
  ) {}

  async execute(
    email: string,
    options: {
      initialDate?: string;
      endDate?: string;
      page?: number;
      limit?: number;
    },
  ): Promise<any[]> {
    const { account_id } =
      await this.getUserAccountService.getAccountAndPersonByEmail(email);

    const { bearer_token, key, certificate } =
      await this.authTokenDock.getAuthDock();

    const httpsAgent = new https.Agent({
      cert: certificate,
      key: key,
      rejectUnauthorized: false,
    });

    try {
      const url = `${this.configService.get('DOCK_URL_GLOBAL')}/account-services/management/v1/accounts/${account_id}/list-entries`;
      const urlGetDescription = `${this.configService.get(
        'DOCK_URL_GLOBAL',
      )}/dockpay/v1/transfers/`;

      const transfersResponse = await axios.post(
        url,
        {
          initial_date: options.initialDate,
          end_date: options.endDate,
          metadata: {
            pagination: {
              page: options.page,
              limit: options.limit,
            },
            sort: {
              field: 'id',
              order: 'desc',
            },
          },
        },
        {
          headers: {
            Authorization: `Bearer ${bearer_token}`,
            'Content-Type': 'application/json',
          },
          httpsAgent,
        },
      );

      const transferIds =
        transfersResponse.data?.content.map(
          (transfer) => transfer.operation_instance_id,
        ) || [];

      // Se hace peticiones para obtener mas detalle de las transferencias
      let movementsDetailsMap = {};
      if (transferIds.length > 0) {
        const movementsDetailsDock = await Promise.all(
          transferIds.map(async (id) => {
            try {
              const response = await axios.get(`${urlGetDescription}${id}`, {
                headers: {
                  Authorization: `Bearer ${bearer_token}`,
                  'Content-Type': 'application/json',
                },
                httpsAgent,
              });
              return {
                id,
                description: response.data?.description || null,
                debtor: response.data?.debtor || null,
                creditor: response.data?.creditor || null,
              };
            } catch (err) {
              return { id, description: null, debtor: null, creditor: null };
            }
          }),
        );

        movementsDetailsMap = movementsDetailsDock.reduce((acc, curr) => {
          acc[curr.id] = {
            description: curr.description || 'No disponible',
            debtor: {
              name: curr.debtor?.metadata.name || 'No disponible',
              key: curr.debtor?.key || 'No disponible',
            },
            creditor: {
              name: curr.creditor?.metadata.name || 'No disponible',
              key: curr.creditor?.key || 'No disponible',
            },
          };
          return acc;
        }, {});
      }

      let paymentTypesMap = {};
      if (transferIds.length > 0) {
        const userTransfers = await this.dataSource.query(
          `SELECT 
            reference_dock_id, 
            payment_type, 
            concept, 
            beneficiary_name, 
            beneficiary_account, 
            bank_name,
            status_dock,
            status_transfer
          FROM user_transfer 
          WHERE reference_dock_id = ANY($1)`,
          [transferIds],
        );

        paymentTypesMap = userTransfers.reduce((acc, curr) => {
          acc[curr.reference_dock_id] = {
            payment_type: curr.payment_type,
            concept: curr.concept,
            beneficiary_name: curr.beneficiary_name,
            beneficiary_account: curr.beneficiary_account,
            bank_name: curr.bank_name,
          };
          return acc;
        }, {});
      }

      const sentTransfers = await Promise.all(
        transfersResponse.data?.content.map(async (transfer) => {
          const transferData =
            paymentTypesMap[transfer.operation_instance_id] || {};
          const paymentTypeRaw = transferData.payment_type || null;
          const concept = transferData.concept || null;
          const beneficiary_name = transferData.beneficiary_name || null;
          let beneficiary_account = transferData.beneficiary_account || null;
          const bank_name = transferData.bank_name || null;
          paymentTypesMap[transfer.operation_instance_id] || null;
          let paymentTypeLabel = null;
          if (paymentTypeRaw === 'TRANSFER_ORDERS') {
            paymentTypeLabel = 'Spei enviado';
          } else if (paymentTypeRaw === 'DOCK_PAY') {
            paymentTypeLabel = 'Pago Convenia Wallet';
          } else if (!paymentTypeRaw) {
            // Si no hay paymentType, revisa el operation_config_name
            if (transfer.operation_config_name === 'P2P - P2P_IN') {
              paymentTypeLabel = 'Spei recibido';
            } else if (transfer.operation_config_name === 'P2P - P2P_OUT') {
              paymentTypeLabel = 'Spei enviado';
            } else {
              paymentTypeLabel = 'Compra en establecimiento';
            }
          }
          const movementsData =
            movementsDetailsMap[transfer.operation_instance_id];
          //Si no tiene beneficiary_account, intenta obtenerlo de la base de datos
          if (!beneficiary_account && movementsData?.creditor?.key) {
            const result = await this.dataSource.query(
              `SELECT at2.clabe
              FROM account a
              INNER JOIN person p ON p.id = a."personIDDockId"
              INNER JOIN account_transfer at2 ON at2.person_id = p.id
              WHERE a."accountExtID" = $1
              LIMIT 1`,
              [movementsData.creditor.key],
            );
            beneficiary_account = result[0]?.clabe || 'No disponible';
          }

          return {
            date: transfer.effective_due_date,
            amount: transfer.operation_components[0].amount,
            description:
              transfer.operation_config_name !== 'P2P - P2P_IN' &&
              transfer.operation_config_name !== 'P2P - P2P_OUT'
                ? transfer.beneficiary
                : movementsData.description,
            beneficiary: transfer.beneficiary,
            id: transfer.operation_instance_id,
            type:
              transfer.operation_config_name === 'P2P - P2P_IN'
                ? 'creditor'
                : 'debtor',
            key: transfer.operation_instance_id,
            movementType: transfer.operation_config_name
              ? transfer.operation_config_name.split(' - ').pop()
              : '',
            operationDate: transfer.contract_date,
            applicationDate: transfer.effective_due_date,
            payment_type: paymentTypeLabel,
            concept: concept || 'No disponible',
            beneficiary_name: beneficiary_name || movementsData?.creditor?.name,
            beneficiary_account: beneficiary_account || 'No disponible',
            bank_name: bank_name || 'Convenia',
            debtor: movementsData.debtor,
            creditor: movementsData.creditor,
          };
        }) || [],
      );
      const allTransfers = [...sentTransfers];

      return allTransfers;
    } catch (error) {
      console.error(
        'Error obteniendo las transferencias desde Dock:',
        error.response?.data || error.message,
      );
      throw new BadRequestException(
        error.response?.data ||
          'Error al obtener las transferencias desde Dock',
      );
    }
  }
}
