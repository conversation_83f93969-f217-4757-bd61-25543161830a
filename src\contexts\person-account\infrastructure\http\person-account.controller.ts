import { Controller, Body, Post, UseGuards } from '@nestjs/common';

/* ** Import useCases ** */
import { CreateNatPersAccUseCase } from '../../application/create-person-account-natural/create-pers-acc-nat.usecase';
import { CreateLegPersAccUseCase } from '../../application/create-person-account-legal/create-pers-acc-leg.usecase';

/* ** DTOs ** */
import { CreateNatPersAccDto } from '../../application/create-person-account-natural/create-pers-acc-nat.dto';
import { CreateLegPersAccDto } from '../../application/create-person-account-legal/create-pers-acc-leg.dto';

/* ** Utils ** */
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';
import { JwtAuthGuard } from 'src/contexts/auth/guards/auth.guard';

@UseGuards(JwtAuthGuard)
@Controller('person-account')
export class PersonAccountController {
  constructor(
    private readonly natural: CreateNatPersAccUseCase,
    private readonly legal: CreateLegPersAccUseCase,
  ) {}

  @Post('create-natural')
  async createNaturalPersonAccount(@Body() body: CreateNatPersAccDto) {
    try {
      return await this.natural.executeNatPersAcc(body);
    } catch (e) {
      console.log(e);
      return ResponseUtil.error(
        e.message,
        {
          data: {
            statusCode: e.status,
            message: String(e.response.data.error),
            error: e.name,
          },
        },
        e.status,
      );
    }
  }

  @Post('create-legal')
  async createLegalPersonAccount(@Body() body: CreateLegPersAccDto) {
    try {
      return await this.legal.executeLegPersAcc(body);
    } catch (e) {
      return ResponseUtil.error(
        e.message,
        {
          data: {
            statusCode: e.status,
            message: String(e),
            error: e.name,
          },
        },
        e.status,
      );
    }
  }
}
