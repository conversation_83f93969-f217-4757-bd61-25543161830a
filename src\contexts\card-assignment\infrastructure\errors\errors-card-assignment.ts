import { CustomError } from 'src/contexts/shared/errors/custom-error';

export class ERROR_USER_NOT_FOUND extends CustomError {
  constructor() {
    super(
      'The user could not be found. Please check the provided information.',
    );
  }
}

export class ERROR_CARD_NOT_FOUND extends CustomError {
  constructor() {
    super('The card was not found. Verify the details and try again.');
  }
}

export class ERROR_CARD_ALREADY_ASSIGNED extends CustomError {
  constructor() {
    super('This card is already assigned to another user.');
  }
}

export class ERROR_CARD_EXPIRED extends CustomError {
  constructor() {
    super('This card has expired and is no longer valid.');
  }
}

export class ERROR_ACCOUNT_NOT_FOUND extends CustomError {
  constructor() {
    super('The account could not be found. Please verify the information.');
  }
}

export class ERROR_ALIAS_CORE_PHYSICAL_NOT_CREATED extends CustomError {
  constructor() {
    super('Failed to create the Core physical alias. Please try again later.');
  }
}

export class ERROR_ALIAS_CORE_VIRTUAL_NOT_CREATED extends CustomError {
  constructor() {
    super('Failed to create the Core virtual alias. Please try again later.');
  }
}

export class ERROR_CHANGE_STATUS_NORMAL extends CustomError {
  constructor() {
    super(
      'Failed to change the status of the card to normal. Please try again later.',
    );
  }
}
