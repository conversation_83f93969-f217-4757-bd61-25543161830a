import { Injectable } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';
import * as Handlebars from 'handlebars';

/* ** DTOs ** */
import { SendWelcomeEmailDto } from './send-welcome-email.dto';

/* ** Repositories ** */
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';

/* ** Utils ** */
import { StorageS3Utils } from 'src/contexts/shared/utils/storageUtils/storageS3.utils';
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';

@Injectable()
export class SendWelcomeEmailUseCase {
  constructor(
    private readonly mailer: MailerService,
    private readonly s3Utils: StorageS3Utils,
  ) {}

  async sendWelcomeEmail(params: SendWelcomeEmailDto): Promise<ApiResponseDto> {
    try {
      const string_template: string = await this.s3Utils.getTemplateFromS3(
        'templates/welcome-created-user.html',
      );

      const template = Handlebars.compile(string_template);

      await this.mailer.sendMail({
        to: params.email,
        from: `🔔 Convenia Support`,
        subject: 'Bienvenido a Convenia – Tu cuenta ha sido creada',
        html: template({
          name_user: params.name_user,
          company_name: params.company_name,
          role_name: params.role_name,
          signup_date: params.signup_date,
        }),
      });

      return ResponseUtil.success('Welcome email sent successfully', {}, 200);
    } catch (error) {
      throw error;
    }
  }
}
