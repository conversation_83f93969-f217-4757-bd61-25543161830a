import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClarificationFileRepository } from '../../domain/repository/clarification-file.repository';
import { ClarificationFile } from '../../domain/entities/clarification-file.entity';

export class ClarificationFileRepositoryImpl
  implements ClarificationFileRepository
{
  constructor(
    @InjectRepository(ClarificationFile)
    private readonly repo: Repository<ClarificationFile>,
  ) {}

  create(file: Partial<ClarificationFile>): ClarificationFile {
    return this.repo.create(file);
  }

  save(file: ClarificationFile): Promise<ClarificationFile> {
    return this.repo.save(file);
  }

  findByClarification(trackingNumber: string): Promise<ClarificationFile[]> {
    return this.repo.find({ where: { clarification: { trackingNumber } } });
  }
}
