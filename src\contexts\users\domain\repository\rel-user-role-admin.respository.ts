import { ApiResponseDto } from "src/contexts/shared/interfaces/dtos/api-response.dto";
import { CreateRelUserRoleAdminDto } from "../../application/usecases-user/create-user/create-rel-user-role-admin.dto";

export interface RelUserRoleAdminRepository {
    save(dto: CreateRelUserRoleAdminDto) : Promise<ApiResponseDto>;
    remove(dto: CreateRelUserRoleAdminDto): Promise<void>;
    removeByUserId(userId: string) : Promise<void>;
}