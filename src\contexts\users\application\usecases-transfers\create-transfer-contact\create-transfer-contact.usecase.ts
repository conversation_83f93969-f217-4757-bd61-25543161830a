import { CreateTransferContactDto } from './create-transfer-contact.dto';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { ReadUserUseCase } from '../../usecases-user/read-user/read-user.usecase';
import { Injectable } from '@nestjs/common';
import { TransferContactRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/transfer-contact.repository.impl';

@Injectable()
export class CreateTransferContactUseCase {
  constructor(
    private readonly transferContactRepository: TransferContactRepositoryImpl,
    private readonly readUserUseCase: ReadUserUseCase
  ) {}

  async execute(data: CreateTransferContactDto): Promise<ApiResponseDto> {
    if (!data.userId && !data.email) {
      return {
        statusCode: 400,
        error: 'User ID or email is required',
        message: 'User ID or email is required',
      };
    }
    if (data.email && !data.userId) {
      const user = await this.readUserUseCase.executeEmail(data.email);
      data.userId = user.id;
    }
    return await this.transferContactRepository.save(data);
  }
}
