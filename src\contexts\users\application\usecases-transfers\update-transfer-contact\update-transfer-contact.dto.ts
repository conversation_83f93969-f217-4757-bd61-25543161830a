import { IsOptional, IsString } from 'class-validator';

export class UpdateTransferContactDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  num_clabe?: string;

  @IsString()
  @IsOptional()
  bank_institution?: string;

  @IsString()
  @IsOptional()
  rfc?: string;

  @IsString()
  @IsOptional()
  alias?: string;

  @IsString()
  @IsOptional()
  company_name?: string;

  @IsString()
  @IsOptional()
  email?: string;
}
