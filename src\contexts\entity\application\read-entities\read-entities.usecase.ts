import { Injectable } from '@nestjs/common';
import { EntityRepositoryImpl } from 'src/contexts/entity/infrastructure/repositories/entity.repository.impl';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';

@Injectable()
export class ReadEntitiesUseCase {
  constructor(private readonly entityRepositoryImpl: EntityRepositoryImpl) {}

  async execute(): Promise<ApiResponseDto> {
    return this.entityRepositoryImpl.findAll();
  }
}
