import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEmail,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';

export class ResUserWithAdminSettingsDto {
  @ApiProperty()
  @IsUUID()
  id: string;

  @ApiProperty()
  @IsBoolean()
  enabled: boolean;

  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  spei_in?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  spei_out?: number;

  @ApiProperty()
  @IsOptional()
  @IsUUID()
  managerId?: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  target_refound?: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  ambassador?: string;

  @ApiProperty()
  @IsOptional()
  @IsEmail()
  convenia_account?: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  membership_number: number;

  @ApiProperty()
  @IsNumber()
  phone: number;

  @ApiProperty()
  @IsBoolean()
  isManager: boolean;
}
