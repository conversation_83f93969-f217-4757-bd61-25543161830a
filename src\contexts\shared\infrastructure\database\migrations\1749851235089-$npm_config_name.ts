import { MigrationInterface, QueryRunner } from "typeorm";

export class  $npmConfigName1749851235089 implements MigrationInterface {
    name = ' $npmConfigName1749851235089'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "rol" ADD "isVisible" boolean NOT NULL DEFAULT true`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "rol" DROP COLUMN "isVisible"`);
    }

}
