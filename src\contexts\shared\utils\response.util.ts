import { Injectable } from '@nestjs/common';
import { ApiResponseDto } from '../interfaces/dtos/api-response.dto';

@Injectable()
export class ResponseUtil {
  static success(
    message: string,
    data: any,
    statusCode: number,
  ): ApiResponseDto {
    return {
      message,
      data,
      statusCode,
    };
  }

  static error(
    message: string,
    error: any,
    statusCode: number,
  ): ApiResponseDto {
    return {
      message,
      error,
      statusCode,
    };
  }
}
