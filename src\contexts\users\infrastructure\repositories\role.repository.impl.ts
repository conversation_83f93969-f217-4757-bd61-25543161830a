import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Rol, RoleTypeEnum } from '../../domain/entities/rol.entity';
import { RoleRepository } from '../../domain/repository/role.repository';
import { RoleEnum } from 'src/contexts/shared/enums/roles.enum';

export class RoleRepositoryImpl implements RoleRepository {
  constructor(
    @InjectRepository(Rol)
    private readonly repository: Repository<Rol>,
  ) {}

  async findByName(name: RoleEnum): Promise<Rol | null> {
    return this.repository.findOneBy({ name });
  }

  async findById(id: string): Promise<Rol | null> {
    return this.repository.findOneBy({ id });
  }

  async findByType(type?: RoleTypeEnum): Promise<Rol[]> {
    if (type) {
      return this.repository.find({
        where: {
          type,
          isVisible: true,
        },
      });
    }
    return this.repository.find();
  }
}
