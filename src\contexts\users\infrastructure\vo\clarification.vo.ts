import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsDate, IsArray } from 'class-validator';

export class ClarificationFileVO {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsString()
  file_name: string;

  @ApiProperty()
  @IsString()
  file_url: string;
}

export class ClarificationUserVO {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  email: string;

  @ApiProperty()
  @IsString()
  avatar: string;
}

export class ClarificationVO {
  @ApiProperty()
  @IsString()
  trackingNumber: string;

  @ApiProperty()
  @IsString()
  type: string;

  @ApiProperty()
  @IsString()
  description: string;

  @ApiProperty()
  @IsString()
  status: string;

  @ApiProperty()
  @IsDate()
  createdAt: Date;

  @ApiProperty({ type: ClarificationUserVO })
  user: ClarificationUserVO;

  @ApiProperty({ type: [ClarificationFileVO] })
  @IsArray()
  files: ClarificationFileVO[];
}
