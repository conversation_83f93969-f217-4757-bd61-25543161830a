import { Injectable } from '@nestjs/common';
import { ReadRolesFilterDtoo } from './read-roles.dto';
import { RoleRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/role.repository.impl';

@Injectable()
export class ReadRolesUseCase {
  constructor(
    private readonly roleRepository: RoleRepositoryImpl
  ) {}

  execute(filter: ReadRolesFilterDtoo){
    return this.roleRepository.findByType(filter.type);
  }

}
