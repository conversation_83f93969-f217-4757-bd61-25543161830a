export enum CardType {
  PHYSICAL = 'PHYSICAL',
  VIRTUAL = 'VIRTUAL',
}

export enum ActiveFunction {
  CREDIT = 'CREDIT',
  DEBIT = 'DEBIT',
  PREPAID = 'PREPAID',
  VOUCHER = 'VOUCHER',
  MULTIPLE = 'MULTIPLE',
}

export enum CardStatus {
  NORMAL = 'NORMAL',
  BLOCKED = 'BLOCKED',
  CANCELED = 'CANCELED',
}

export enum ReasonStatus {
  OWNER_REQUEST = 'OWNER_REQUEST',
  INITIAL_BLOCKED = 'INITIAL_BLOCKED',
  LOST = 'LOST',
  THEFT = 'THEFT',
  INVALID_PASSWORD = 'INVALID_PASSWORD',
  INACTIVITY = 'INACTIVITY',
  TEMPORARILY = 'TEMPORARILY',
  MISPLACED_CARD = 'MISPLACED_CARD',
  TERMINATED_CONTRACT = 'TERMINATED_CONTRACT',
  SUSPICION_OF_FRAUD = 'SUSPICION_OF_FRAUD',
  OTHER = 'OTHER',
}
