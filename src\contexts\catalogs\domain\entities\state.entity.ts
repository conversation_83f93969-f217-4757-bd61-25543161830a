import { Column, <PERSON><PERSON><PERSON>, OneToMany, PrimaryGeneratedColumn } from "typeorm";
import { City } from "./city.entity";
import { ApiProperty } from "@nestjs/swagger";
import { IsNumber, IsString } from "class-validator";

@Entity("states")
export class State {

  @ApiProperty({ description: 'The unique identifier for the state' })
  @IsNumber()
  @PrimaryGeneratedColumn("identity")
  id: number;

  @ApiProperty({ description: 'The name of the state' })
  @IsString()
  @Column({nullable: true})
  name: string;

  @OneToMany(() => City, (city) => city.state)
  cities: City[];

}