import { Injectable } from '@nestjs/common';
import { State } from 'src/contexts/catalogs/domain/entities/state.entity';
import { StateRepositoryImpl } from 'src/contexts/catalogs/infrastructure/repositories/state.repository';

@Injectable()
export class ReadStatesUseCase {
  constructor(private readonly stateRepository: StateRepositoryImpl) {}

  async execute(): Promise<State[]> {
    return this.stateRepository.findAll();
  }
}