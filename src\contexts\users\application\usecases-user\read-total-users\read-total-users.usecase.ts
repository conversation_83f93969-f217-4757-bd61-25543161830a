import { Injectable } from '@nestjs/common';
import { UsersRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/users.repository.impl';
import { TotalUsersVO } from 'src/contexts/users/infrastructure/vo/user.vo';
import { ReadTotalUsersDto } from './read-total-users.dto';
import { AccountTransferRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/account.transfer.repository.impl';

@Injectable()
export class ReadTotalUsersUseCase {
  constructor(
    private readonly usersRepositoryImpl: UsersRepositoryImpl,
    private readonly accountTransferRepositoryImpl: AccountTransferRepositoryImpl
  ) {}

  async execute(query: ReadTotalUsersDto): Promise<TotalUsersVO> {
    const count = await this.usersRepositoryImpl.count(query);
    return {count};
  }

  async executeTransferAccounts(query: ReadTotalUsersDto) : Promise<TotalUsersVO> {
     const count = await this.accountTransferRepositoryImpl.count(query);
     return {count}
  }
}
