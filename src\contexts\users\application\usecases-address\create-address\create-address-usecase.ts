import { Injectable } from '@nestjs/common';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { AddressRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/addressrepository.impl';
import { CreateAddressDto } from './create-address.dto';

@Injectable()
export class CreateAddressUseCase {
  constructor(private readonly addressRepositoryImpl: AddressRepositoryImpl) {}

  async execute(address: CreateAddressDto): Promise<ApiResponseDto> {
    return this.addressRepositoryImpl.save(address);
  }
}
