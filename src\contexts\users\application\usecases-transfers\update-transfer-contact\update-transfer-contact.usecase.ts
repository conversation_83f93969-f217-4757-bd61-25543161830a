import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { Injectable } from '@nestjs/common';
import { TransferContactRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/transfer-contact.repository.impl';
import { UpdateTransferContactDto } from './update-transfer-contact.dto';

@Injectable()
export class UpdateTransferContactUseCase {
  constructor(
    private readonly transferContactRepository: TransferContactRepositoryImpl,
  ) {}

  async execute(
    id: string,
    data: UpdateTransferContactDto,
  ): Promise<ApiResponseDto> {
    return await this.transferContactRepository.update(id, data);
  }
}
