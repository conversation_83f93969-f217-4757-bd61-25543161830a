import { IsString, <PERSON>NotEmpty, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

class PayloadDto {
  @IsString({ message: 'Debtor person id must be a string' })
  @IsNotEmpty({ message: 'Debtor person id is required' })
  debtor_person_id: string;

  @IsString({ message: 'Creditor person id must be a string' })
  @IsNotEmpty({ message: 'Creditor person id is required' })
  creditor_person_id: string;

  @IsString({
    message: 'operation_step_instance_event_reason must be a string',
  })
  @IsNotEmpty({ message: 'operation_step_instance_event_reason is required' })
  operation_step_instance_event_reason: string;

  @IsString({ message: 'operation_instance_amount must be a string' })
  @IsNotEmpty({ message: 'operation_instance_amount is required' })
  operation_instance_amount: string;

  @IsString({ message: 'operation_instance_asset must be a string' })
  @IsNotEmpty({ message: 'operation_instance_asset is required' })
  operation_instance_asset: string;

  @IsString({ message: 'debtor_name must be a string' })
  @IsNotEmpty({ message: 'debtor_name is required' })
  debtor_name: string;

  @IsString({ message: 'Status must be a string' })
  @IsNotEmpty({ message: 'Status is required' })
  status: string;
}

export class TriggersTestNotificationDto {
  @IsString({ message: 'Event name must be a string' })
  @IsNotEmpty({ message: 'Event name is required' })
  event_name: string;

  @ValidateNested()
  @Type(() => PayloadDto)
  payload: PayloadDto;
}
