import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as https from 'https';
import axios from 'axios';
import { DateTime } from 'luxon';

/* ** Utils ** */
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';
import { DecryptData } from 'src/contexts/shared/utils/sensitive-data/decryptData.util';
import { RedisService } from 'src/contexts/shared/utils/Redis/redis';
import { SecurePayloadUtil } from 'src/contexts/shared/utils/data-encrypt/data-encrypt.util';
import { EncryptData } from 'src/contexts/shared/utils/sensitive-data/encriptData.util';

/* ** Repositories ** */
import { AccountCardRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/account-card.repository.impl';

/* ** DTOs ** */
import {
  ParamsListCards,
  ParamsFindCardDockPin,
  ResListCards,
  DataAliasCore,
  ArrayDataCard,
  ParamsSensitiveData,
  ParamsListCard,
  ResFindCardDockPin,
  ResponseListCard,
  ResponseSearchCardPanDto,
} from './find-dock-card.dto';
import { FindPanCardPayloadDto } from 'src/contexts/card-assignment/application/single-card-assignment/single-card-assignment.dto';

/* ** Errors ** */
import {
  ERROR_FIND_ALIAS_CORE,
  ERROR_FIND_PIN_DOCK,
} from './../../infrastructure/errors/errors-dock-cards';

/* ** Enums ** */
import { CardStatus } from 'src/contexts/shared/interfaces/dtos/cards.enum.dto';

@Injectable()
export class FindDockCardUseCase {
  constructor(
    private readonly config: ConfigService,
    private readonly auth: authTokenDock,
    private readonly decryptData: DecryptData,
    private readonly accountCard: AccountCardRepositoryImpl,
    private readonly log: RedisService,
    private readonly secure: SecurePayloadUtil,
    private readonly encrypt: EncryptData,
  ) {}

  async findDockCard(card: ParamsListCards): Promise<ResListCards> {
    const alias_core = await this.getCardsAliasCore(card);

    if (!alias_core?.content?.length) throw new ERROR_FIND_ALIAS_CORE();

    const array_data = <ArrayDataCard[]>[];

    for (const card of alias_core.content) {
      const [detaills_card, card_data] = await Promise.all([
        this.sensitiveData({
          card_dock_id: card.metadata.card_id,
        }),
        this.getDataCard({
          card_dock_id: card.metadata.card_id,
        }),
      ]);

      const {
        id,
        card_type,
        format_expiration_date,
        card_last_4,
        pan,
        expiration_date,
      } = detaills_card;

      const response: ArrayDataCard = {
        id,
        card_type,
        format_expiration_date: this.secure.encryptData(format_expiration_date),
        card_last_4: this.secure.encryptData(card_last_4),
        pan: this.secure.encryptData(pan),
        expiration_date: this.secure.encryptData(expiration_date),
        status: card_data.status,
      };

      if (card_data && card_data.status !== CardStatus.CANCELED)
        array_data.push(response);
    }

    return {
      statusCode: 200,
      message: 'Success',
      data: array_data,
    };
  }

  async findDockCardPin(
    card: ParamsFindCardDockPin,
  ): Promise<ResFindCardDockPin> {
    const pin = await this.findPinByID(card);

    if (!pin) throw new ERROR_FIND_PIN_DOCK();

    return {
      statusCode: 200,
      message: 'Success',
      pin: this.secure.encryptData(pin),
    };
  }

  async searchPan(pan: string): Promise<ResponseSearchCardPanDto> {
    /* ** Encrypt data ** */
    const encrypt = await this.encrypt.encryptData({
      encrypted_data: pan,
    });

    const { bearer_token, certificate, key } = await this.auth.getAuthDock();

    const payload: FindPanCardPayloadDto = {
      pan: encrypt.encrypted_data,
      iv: encrypt.iv,
      aes: encrypt.aes,
      mode: encrypt.mode,
    };

    const httpsAgent = new https.Agent({
      cert: certificate,
      key,
      rejectUnauthorized: false,
    });

    const executeSearch = async (): Promise<ResponseSearchCardPanDto> => {
      try {
        const data = await axios.post(
          `${this.config.get('DOCK_URL_GLOBAL')}/cards/v1/cards/pan`,
          payload,
          {
            headers: {
              Authorization: `Bearer ${bearer_token}`,
            },
            httpsAgent,
          },
        );

        console.log('data', data.data);

        return data.data;
      } catch (error) {
        throw new BadRequestException(
          error.response?.data || 'Error al crear la orden de transferencia',
        );
      }
    };

    const response = await executeSearch();
    console.log('response', response);
    return response;
  }

  async sensitiveData(card: ParamsSensitiveData): Promise<ArrayDataCard> {
    const { bearer_token, certificate, key } = await this.auth.getAuthDock();

    /* ** Agent ** */
    const httpsAgent = new https.Agent({
      cert: certificate,
      key,
      rejectUnauthorized: false,
    });

    const execute = async (): Promise<ArrayDataCard> => {
      try {
        const promise_data_card = await axios.get(
          `${this.config.get('DOCK_URL_GLOBAL')}/cards/v1/cards/${card.card_dock_id}/data`,
          {
            headers: {
              Authorization: `Bearer ${bearer_token}`,
            },
            httpsAgent,
          },
        );

        const promise_type = await this.accountCard.findCardType(
          card.card_dock_id,
        );

        const [data_card, type] = await Promise.all([
          promise_data_card,
          promise_type,
        ]);

        const { aes, iv, pan, expiration_date } = data_card.data;

        const [decrypt_pan, decrypt_expiration_date] = await Promise.all([
          this.decryptData.decryptData({ encrypt_data: pan, iv, aes }),
          this.decryptData.decryptData({
            encrypt_data: expiration_date,
            iv,
            aes,
          }),
        ]);

        return {
          id: card.card_dock_id,
          pan: decrypt_pan,
          expiration_date: decrypt_expiration_date,
          card_type: type,
          card_last_4: decrypt_pan.slice(-4),
          format_expiration_date: DateTime.fromISO(
            decrypt_expiration_date,
          ).toFormat('MM/yy'),
          status: '',
        };
      } catch (error) {
        const code = error?.response?.status;
        this.log.logError({
          key: 'api:log',
          name: 'ERROR_SENSITIVE_DATA_DOCK',
          error: error?.response?.data || error?.message,
          nameController: 'FindDockCardUseCase',
          statusCode: code,
        });

        return null;
      }
    };
    const response = await execute();

    return response;
  }

  public async getCardsAliasCore(
    card: ParamsListCards,
  ): Promise<DataAliasCore> {
    /* ** Get Auth ** */
    const { bearer_token, certificate, key } = await this.auth.getAuthDock();
    /* ** Agent ** */
    const httpsAgent = new https.Agent({
      cert: certificate,
      key,
      rejectUnauthorized: false,
    });

    const executeListAliasCore = async (): Promise<DataAliasCore> => {
      try {
        const data = await axios.get(
          `${this.config.get('DOCK_URL_GLOBAL')}/account-services/alias-core/v1/alias/accounts/${card.account_dock_id}?limit=20`,
          {
            headers: {
              Authorization: `Bearer ${bearer_token}`,
              Accept: 'application/json',
            },
            httpsAgent,
          },
        );

        return data.data;
      } catch (error) {
        const code = error.response.status;

        this.log.logError({
          key: 'api:log',
          name: 'ERROR_GET_ALIAS_CORE_DOCK',
          error: error?.response?.data || error?.message,
          nameController: 'FindDockCardUseCase',
          statusCode: code,
        });

        return null;
      }
    };

    const response = await executeListAliasCore();
    return response;
  }

  public async getDataCard(card: ParamsListCard): Promise<ResponseListCard> {
    /* ** Get Auth ** */
    const { bearer_token, certificate, key } = await this.auth.getAuthDock();
    /* ** Agent ** */
    const httpsAgent = new https.Agent({
      cert: certificate,
      key,
      rejectUnauthorized: false,
    });

    const executeListCard = async (): Promise<ResponseListCard> => {
      try {
        const data = await axios.get(
          `${this.config.get('DOCK_URL_GLOBAL')}/cards/v1/cards/${card.card_dock_id}`,
          {
            headers: {
              Authorization: `Bearer ${bearer_token}`,
              Accept: 'application/json',
            },
            httpsAgent,
          },
        );

        return data.data;
      } catch (error) {
        const code = error.response.status;

        this.log.logError({
          key: 'api:log',
          name: 'ERROR_LIST_CARD',
          error: error?.response?.data || error?.message,
          nameController: 'FindDockCardUseCase',
          statusCode: code,
        });

        return null;
      }
    };

    const response = await executeListCard();
    return response;
  }

  private async findPinByID(card: ParamsFindCardDockPin): Promise<string> {
    const { bearer_token, certificate, key } = await this.auth.getAuthDock();

    /* ** Agent ** */
    const httpsAgent = new https.Agent({
      cert: certificate,
      key,
      rejectUnauthorized: false,
    });

    const execute = async (): Promise<string> => {
      try {
        const data_card = await axios.get(
          `${this.config.get('DOCK_URL_GLOBAL')}/cards/v1/cards/${card.card_dock_id}/pin`,
          {
            headers: {
              Authorization: `Bearer ${bearer_token}`,
            },
            httpsAgent,
          },
        );

        const { aes, iv, pin } = data_card.data;

        const decrypt_pin = await this.decryptData.decryptData({
          encrypt_data: pin,
          iv,
          aes,
        });

        return decrypt_pin;
      } catch (error) {
        const code = error?.response?.status;
        this.log.logError({
          key: 'api:log',
          name: 'ERROR_FIND_PIN_DOCK',
          error: error?.response?.data || error?.message,
          nameController: 'FindDockCardUseCase',
          statusCode: code,
        });

        return null;
      }
    };
    const response = await execute();

    return response;
  }
}
