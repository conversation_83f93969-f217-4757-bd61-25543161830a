import { Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { UpdateAdminDto } from './update-admin.dto';
import { AdminRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/adminrepository.impl';
import * as argon2 from 'argon2';
import { UsersRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/users.repository.impl';
import { CommonUtil } from 'src/contexts/shared/utils/common.util';
import { UpdateAddressUseCase } from '../../usecases-address/update-address/update-address.usecase';
import { UpdateLegPersAccUseCase } from 'src/contexts/person-account/application/update-person-account-legal/update-person-account-legal.usecase';
import { UpdatePersonAddressLegalDto } from 'src/contexts/person-account/application/update-person-account-legal/update-person-address-legal.dto';
import { UpdatePersonEmailLegalDto } from 'src/contexts/person-account/application/update-person-account-legal/update-person-email-legal.dto';
import { UpdateLegalPersonDto } from 'src/contexts/person-account/application/update-person-account-legal/update-legal-person.dto';
import { DockLegalPersonService } from 'src/contexts/dock/infraestructure/services/dock-legal-person.service';
import { RelUserRoleAdminRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/rel-user-role-admin.repository.impl';
import { RoleRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/role.repository.impl';
import { RoleEnum } from 'src/contexts/shared/enums/roles.enum';
import { S3Metadata } from 'src/contexts/users/domain/entities/s3-metadata.entity';
import { AdminDocuments } from 'src/contexts/users/domain/entities/admin-documents.entity';
import { StorageS3Utils } from 'src/contexts/shared/utils/storageUtils/storageS3.utils';

@Injectable()
export class UpdateAdminUseCase {
  constructor(
    private readonly adminRepositoryImpl: AdminRepositoryImpl,
    readonly updateAdressUseCase: UpdateAddressUseCase,
    private readonly usersRepositoryImpl: UsersRepositoryImpl,
    readonly updateLegalPersonUsecase: UpdateLegPersAccUseCase,
    private readonly dockLegalPersonService: DockLegalPersonService,
    private readonly relUserRoleAdminRepositoryImpl : RelUserRoleAdminRepositoryImpl,
    private readonly roleRepository: RoleRepositoryImpl,
    private readonly s3: StorageS3Utils
  ) {}

  async execute(id: string, admin: UpdateAdminDto): Promise<void> {

    const adminExist = await this.adminRepositoryImpl.findByIdDetails(id);

    if(!adminExist)
      throw new NotFoundException('Admin no encontrado.')

    const role = await this.roleRepository.findByName(RoleEnum.CLIENTE_ADMIN);

    if (!role)
      throw new InternalServerErrorException(
        'Error al obtener el rol del administrador',
      );

    const {manager} = adminExist;
    const {address} = manager;

    const addressId = address.id;

    const requiredUpdateAddress = CommonUtil.compareObjects(address, admin.address);

    const legalPerson = await this.dockLegalPersonService.getLegalPerson(manager.personIDDock.personExtID);

    const {person_id, addresses,emails } = legalPerson;

    const emailId = emails[0].id;
    const addressPersonId = addresses[0].id;


    if(requiredUpdateAddress){
      const addressDto : UpdatePersonAddressLegalDto = {
        addressLegalId: addressPersonId,
        legalPersonId: person_id,
        number: admin.address.num_ext.toString(),
        postal_code: admin.address.zip_code,
        street: admin.address.street,
        suffix: 'MX'
      };
      await this.updateAdressUseCase.execute(addressId, admin.address);
      await this.updateLegalPersonUsecase.updateLegalPersonAddress(addressDto)
    }

    manager.phone = admin.user.phone;
    manager.email = admin.user.email;
    manager.name = admin.user.name;
    
    if(admin.user.password)
      manager.password = await argon2.hash(admin.user.password);

    await this.usersRepositoryImpl.saveUser(manager);


    if(manager.email !== admin.user.email){

      const emailDto : UpdatePersonEmailLegalDto = {
        currentEmail: emailId,
        email: admin.user.email,
        legalPersonId: manager.personIDDock.personExtID
      }

      await this.updateLegalPersonUsecase.updateEmail(emailDto);
    }

    const updatePersonDto : UpdateLegalPersonDto = {
      legal_name: admin.company_name,
      legalPersonId: manager.personIDDock.personExtID
    }

    await this.updateLegalPersonUsecase.updateLegalPerson(updatePersonDto)

    delete admin.address
    delete admin.user


    if(!admin.enterprises){
      admin.enterprises = []
    }

    const adminExistInEnterprises = admin.enterprises.find(ent => ent.adminId === adminExist.id);

    if(!adminExistInEnterprises){
      admin.enterprises.push({ adminId: adminExist.id})
    }

    await this.relUserRoleAdminRepositoryImpl.removeByUserId(manager.id);
    
    for (const enterprise of admin.enterprises) {
      await this.relUserRoleAdminRepositoryImpl.save({
        admin_data: enterprise.adminId,
        user_id: manager.id,
        rol: role.id,
      });
    }

    delete admin.enterprises;

    adminExist.alias = admin.alias;
    adminExist.company_name = admin.company_name;
    adminExist.rfc = admin.rfc;
    adminExist.spei_in = admin.spei_in;
    adminExist.spei_out = admin.spei_out;
    adminExist.target_refound = admin.target_refound;
    adminExist.ambassador = admin.ambassador;
    adminExist.num_asigned_cards = admin.num_asigned_cards;

    await this.adminRepositoryImpl.saveAdmin(adminExist);

    const {files} = admin;
    
    if(files && files.length){

      for(const file of files){
        await this.s3.moveToPermamentLocation(file.file, adminExist.id);

        const path = "admin/".concat(adminExist.id).concat("/").concat(file.file);
        const s3Metada = new S3Metadata();
        s3Metada.key = path;

        const s3File = await s3Metada.save();

        const document = new AdminDocuments();
        document.file = s3File;
        document.admin = adminExist;
        document.documentType = file.type;

        await document.save();
      }
    }
  }
}
