import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEmail, IsOptional, IsBoolean, IsUUID, IsNumber, MinLength } from 'class-validator';

export class CreatedUserVO {

  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  lastName: string;

  @ApiProperty()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsNumber()
  phone: number;

  @ApiProperty()
  @IsString()
  @MinLength(6)
  password: string;

  @ApiProperty()
  @IsString()
  created_by: string;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  enabled?: boolean;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  personIDDock?: string;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  admin_data?: string;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  address?: string;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  rol?: string;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  relUserRoleAdmins?: string;
}
