import { Process, Processor } from "@nestjs/bull";
import { Job } from "bull";
import { Logger } from "@nestjs/common";
import { DeleteUserUseCase } from "../../application/usecases-user/delete-user/delete-user.usecase";

@Processor('delete-user')
export class DeleteAdminUsersProcessor {
  private readonly logger = new Logger(DeleteAdminUsersProcessor.name);

  constructor(
    private readonly deleteUserUseCase : DeleteUserUseCase
  ) {
    this.logger.log('🎯 DeleteAdminUsersProcessor initialized');
  }

  @Process('process')
  async handleTransfer(job: Job<{userId: string, adminId: string}>) {
    const { userId, adminId } = job.data;
    this.logger.log(`Processing deletion for user ID: ${userId} from admin ID: ${adminId}`);

    try {
      await this.deleteUserUseCase.execute(userId, adminId);
      this.logger.log(`Successfully processed deletion for user ID: ${userId}`);
    } catch (error) {
      this.logger.error(`Error processing deletion for user ID: ${userId}`, error.stack);
    }
  }
}