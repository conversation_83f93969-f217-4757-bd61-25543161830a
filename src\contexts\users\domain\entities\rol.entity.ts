import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { RelUserRoleAdmin } from './rel-user-role-admin.entity';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsString, IsUUID } from 'class-validator';
import { RoleEnum } from 'src/contexts/shared/enums/roles.enum';

export enum RoleTypeEnum {
  CONVENIA = 'CONVENIA',
  CLIENT = 'CLIENTE',
}

@Entity()
export class Rol {
  @ApiProperty()
  @IsUUID()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty()
  @IsEnum(RoleTypeEnum)
  @Column({ type: 'enum', enum: RoleTypeEnum })
  type: RoleTypeEnum;

  @ApiProperty()
  @IsString()
  @Column({ unique: true })
  name: RoleEnum;

  @ApiProperty()
  @IsString()
  @Column()
  description: string;

  @Column({ default: true })
  isVisible: boolean;

  @OneToMany(() => RelUserRoleAdmin, (rel) => rel.role)
  relUserRoleAdmins: RelUserRoleAdmin[];
}
