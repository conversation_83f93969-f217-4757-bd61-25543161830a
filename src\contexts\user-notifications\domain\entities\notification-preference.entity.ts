import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  OneToOne,
  <PERSON>inC<PERSON>umn,
} from 'typeorm';
import { Users } from 'src/contexts/users/domain/entities/users.entity';

@Entity('notification_preferences')
export class UserNotificationPreference {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @OneToOne(() => Users, { onDelete: 'CASCADE' })
  @JoinColumn()
  user: Users;

  @Column({ default: false })
  receive_transaction_notifications: boolean;

  @Column({ default: false })
  receive_reminder_notifications: boolean;
}
