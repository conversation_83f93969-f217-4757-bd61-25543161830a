import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';

@Injectable()
export class SecurePayloadUtil {
  constructor(private readonly config: ConfigService) {}

  encryptData(data: string): string {
    const GCM_NONCE_LENGTH = 12;
    const aes_key = this.config.get('FNBRR_KEY');

    const key = Buffer.from(aes_key, 'base64');
    const nonce = crypto.randomBytes(GCM_NONCE_LENGTH);

    const cipher = crypto.createCipheriv('aes-256-gcm', key, nonce);
    const ciphertext = Buffer.concat([
      cipher.update(data, 'utf8'),
      cipher.final(),
    ]);
    const tag = cipher.getAuthTag();

    const encryptedMessage = Buffer.concat([nonce, ciphertext, tag]).toString(
      'base64',
    );

    return encryptedMessage;
  }

  decryptMessage(data: string): string {
    const GCM_NONCE_LENGTH = 12;
    const GCM_TAG_LENGTH = 16;
    const aes_key = this.config.get('FNBRR_KEY');

    const key = Buffer.from(aes_key, 'base64');
    const messageBytes = Buffer.from(data, 'base64');
    const nonce = messageBytes.subarray(0, GCM_NONCE_LENGTH);
    const ciphertext = messageBytes.subarray(GCM_NONCE_LENGTH, -GCM_TAG_LENGTH);
    const tag = messageBytes.subarray(-GCM_TAG_LENGTH);

    const cipher = crypto.createDecipheriv('aes-256-gcm', key, nonce);
    cipher.setAuthTag(tag);
    const decrypted = Buffer.concat([
      cipher.update(ciphertext),
      cipher.final(),
    ]).toString('utf8');

    return decrypted.toString();
  }
}
