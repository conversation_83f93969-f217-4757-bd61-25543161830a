import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

/* ** Repositories ** */
import { CommissionRepository } from '../../domain/repository/commission.repository';

/* ** Entities ** */
import { Commissions } from '../../domain/entities/commission.entity';

/* ** DTOs ** */
import {
  ParamsCommissionUpdateDto,
  ParamsSaveCommissionDto,
} from '../../shared/commission.dto';

@Injectable()
export class CommissionRepositoryImpl implements CommissionRepository {
  constructor(
    @InjectRepository(Commissions)
    private readonly commission: Repository<Commissions>,
  ) {}

  async save(entity: ParamsSaveCommissionDto): Promise<Commissions> {
    return await this.commission.save(entity);
  }

  async findById(id: string): Promise<Commissions> {
    return await this.commission.findOne({ where: { id } });
  }

  async updateCommision(params: ParamsCommissionUpdateDto): Promise<boolean> {
    await this.commission.update(params.id, { status: params.status });

    return true;
  }
}
