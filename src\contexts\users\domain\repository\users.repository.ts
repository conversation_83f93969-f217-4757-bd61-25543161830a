import { ResponseFindUserByCardIDDto } from './../../../transfer-orders/application/transfer-orders-in/transfer-orders-in.dto';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { CreateUserDto } from '../../application/usecases-user/create-user/create-user.dto';
import { UpdateUserDto } from '../../application/usecases-user/update-user/update-user.dto';
import { ResUserWithAdminSettingsDto } from '../../application/usecases-user/read-user/read-user-with-admin-settings.dto';
import { ResponseCommissionByAccountDto } from 'src/contexts/notifications/application/notification-webhook/notification-webhook.dto';
import {
  ResponseFindOnlyUserAccountDto,
  ResponseFindAccountCreditorDto,
} from '../../application/usecases-user/get-user-account/get-user-account.dto';
import { Users } from '../entities/users.entity';

export interface UsersRepository {
  save(entity: CreateUserDto): Promise<ApiResponseDto>;
  findAll(): Promise<ApiResponseDto>;
  findById(id: string): Promise<ApiResponseDto>;
  update(id: string, entity: UpdateUserDto): Promise<ApiResponseDto>;
  remove(id: string): Promise<ApiResponseDto>;
  findByEmail(email: string): Promise<Users | null>;
  findUserWithCardID(
    card_dock_id: string,
  ): Promise<ResponseFindUserByCardIDDto | null>;
  findCommisions(
    account_dock_id: string,
  ): Promise<ResponseCommissionByAccountDto | null>;
  findCommisionsByEmail(
    email: string,
  ): Promise<ResponseCommissionByAccountDto | null>;
  findCommisionsOfSpei(
    email: string,
  ): Promise<ResponseCommissionByAccountDto | null>;
  findConveniaAccount(convenia_account: string): Promise<boolean>;
  updateConveniaAccount(id: string, account: string): Promise<void>;
  findAllUsers(): Promise<Users[]>;
  findUserWithAdminSettingsByEmail(
    email: string,
  ): Promise<ResUserWithAdminSettingsDto | null>;
  findOnlyUserAccount(
    id: string,
  ): Promise<ResponseFindOnlyUserAccountDto> | null;
  findAccountCreditorKey(
    accoun_dock_id: string,
  ): Promise<ResponseFindAccountCreditorDto | null>;
}
