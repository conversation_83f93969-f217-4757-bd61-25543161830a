# 🚀 FINBERRY BACKEND - NESTJS FINANCIAL PLATFORM

> NestJS-based financial services backend with clean architecture and comprehensive business domain management

## 🚨 MANDATORY SECURITY RULES 🚨

### CRITICAL SECURITY UPDATE PROTOCOL
**NEVER FORGET**: Every time you fix a security vulnerability in ANY context, you MUST:

1. **IMMEDIATELY** after fixing, run:
   ```bash
   npm run lint && npm run test
   ```

2. The system will:
   - 🔍 Detect code quality issues
   - 📝 Validate TypeScript compilation
   - 🧪 Run comprehensive test suite
   - 👥 Ensure no breaking changes

3. **🚫 NEVER commit without running quality checks first!**

This is **MANDATORY** for ALL contexts and must **NEVER** be forgotten!

## 🎯 FINBERRY DEVELOPMENT COMMANDS

### Daily Workflow Commands
```bash
# Start development session
npm run start:dev

# Build and validate code
npm run build

# Run quality checks
npm run lint
npm run format

# Run tests
npm run test
npm run test:cov

# Database operations
npm run migrations:show
npm run migrations:run
```

### Context-Specific Development
```bash
# Generate new migration
npm run migrations:generate -- --name=MigrationName

# Revert last migration
npm run migrations:revert

# TypeORM CLI access
npm run typeorm -- <command>

# Start with debugging
npm run start:debug
```

## 📊 VERIFIED PROJECT STRUCTURE

### Technology Stack (Confirmed from Code)
- **Framework**: NestJS 10.x with TypeScript 5.1+
- **Database**: PostgreSQL with TypeORM 0.3.20
- **Cache/Queue**: Redis with IORedis + Bull Queue
- **Authentication**: JWT + Passport + Argon2
- **Storage**: AWS S3 SDK v3
- **Testing**: Jest + Supertest
- **API Documentation**: Swagger/OpenAPI

### Verified Context Architecture
```
src/
├── contexts/                    # Domain-Driven Design Contexts
│   ├── account/                # Account management and details
│   │   ├── application/        # Use cases (get-account-details, transfers)
│   │   ├── infrastructure/http/ # Controllers and HTTP layer
│   │   └── module/             # NestJS module configuration
│   ├── auth/                   # Authentication & Authorization
│   │   ├── application/        # Signin, biometric, token renewal
│   │   ├── decorators/         # Custom auth decorators
│   │   ├── guards/             # Auth and role guards
│   │   ├── strategies/         # JWT strategy
│   │   └── infraestructure/http/ # Auth controllers
│   ├── users/                  # User management ecosystem
│   │   ├── application/usecases-*/ # Organized by user operations
│   │   │   ├── usecases-admin/    # Admin user operations
│   │   │   ├── usecases-user/     # Standard user operations
│   │   │   ├── usecases-account/  # User account operations
│   │   │   ├── usecases-address/  # Address management
│   │   │   └── usecases-transfers/ # Transfer contacts
│   │   ├── domain/entities/    # User, Account, Address entities
│   │   ├── domain/repository/  # Repository interfaces
│   │   └── infrastructure/     # Implementations and parsers
│   ├── transfers/              # Money transfer operations
│   │   ├── application/        # Transfer creation and management
│   │   ├── infrastructure/queue/ # Background processing
│   │   └── module/             # Transfer module
│   ├── transfer-orders/        # Transfer order management
│   │   ├── application/        # Order creation, checking, balances
│   │   ├── domain/entities/    # Transfer order entities
│   │   └── infrastructure/http/ # Controllers and webhooks
│   ├── dock/                   # Dock payment platform integration
│   │   ├── infraestructure/services/ # Core, legal person, transactions
│   │   └── interfaces/         # Dock API interfaces
│   ├── dock-cards/             # Card management via Dock
│   │   ├── apps/               # Card operations (create, control, CVV)
│   │   └── infrastructure/     # Controllers and error handling
│   ├── notifications/          # Notification system
│   │   ├── application/        # Webhooks and triggers
│   │   ├── domain/entities/    # Notification entities
│   │   └── infrastructure/http/ # Controllers and middleware
│   ├── otp/                    # One-time password management
│   │   ├── app/                # Generate and verify OTP
│   │   ├── domain/entities/    # OTP entities
│   │   └── infrastructure/     # Controllers and repositories
│   └── shared/                 # Shared infrastructure
│       ├── infrastructure/database/ # TypeORM config and migrations
│       ├── utils/              # Common utilities (encryption, Redis, S3)
│       ├── modules/            # Shared modules (Redis, S3, Mailer)
│       └── interfaces/dtos/    # Common DTOs and interfaces
└── main.ts                     # Application bootstrap
```

## Key Dependencies (Verified from package.json)
- **@nestjs/core**: 10.0.0 - NestJS framework core
- **@nestjs/typeorm**: 10.0.2 - TypeORM integration
- **@nestjs/jwt**: 11.0.0 - JWT authentication
- **@nestjs/swagger**: 8.1.1 - API documentation
- **@nestjs/bull**: 11.0.2 - Queue processing
- **@aws-sdk/client-s3**: 3.750.0 - AWS S3 integration
- **argon2**: 0.41.1 - Password hashing
- **ioredis**: 5.6.0 - Redis client
- **pg**: 8.13.1 - PostgreSQL client
- **class-validator**: 0.14.1 - Request validation
- **luxon**: 3.6.1 - Date/time handling

## 🛠️ VERIFIED BUILD COMMANDS

### Development Commands
```bash
# Start development server with hot reload
npm run start:dev

# Start with debugging enabled
npm run start:debug

# Build for production
npm run build

# Start production server
npm run start:prod
```

### Quality Assurance Commands
```bash
# Lint and fix code issues
npm run lint

# Format code with Prettier
npm run format

# Run unit tests
npm run test

# Run tests in watch mode
npm run test:watch

# Generate test coverage report
npm run test:cov

# Run end-to-end tests
npm run test:e2e
```

### Database Management Commands
```bash
# Generate new migration
npm run migrations:generate -- --name=YourMigrationName

# Run pending migrations
npm run migrations:run

# Show migration status
npm run migrations:show

# Revert last migration
npm run migrations:revert

# Direct TypeORM CLI access
npm run typeorm -- <command>
```

## 📋 DETAILED CONTEXT SPECIFICATIONS

### 🔐 Authentication Context (auth/)
**Purpose**: Complete authentication and authorization system

**Verified Components:**
```typescript
// Application Layer
├── biometric/              # Biometric authentication
├── signin/                 # User login
├── renew-token/           # JWT token renewal

// Infrastructure Layer  
├── decorators/            # Auth, roles, user decorators
├── guards/                # Authentication and role guards
├── strategies/            # JWT strategy implementation
└── infraestructure/http/  # Authentication controllers
```

**Key Features:**
- 🔑 JWT-based authentication with refresh tokens
- 🔒 Biometric authentication support
- 🛡️ Role-based access control (RBAC)
- ✅ Custom decorators for auth requirements

### 👥 Users Context (users/)
**Purpose**: Comprehensive user management ecosystem

**Verified Components:**
```typescript
// Use Case Organization
├── usecases-admin/        # Admin user operations
├── usecases-user/         # Standard user CRUD
├── usecases-account/      # User account management
├── usecases-address/      # Address management
├── usecases-person/       # Person entity operations
├── usecases-transfers/    # Transfer contact management
├── usecases-clarification/ # Transaction clarifications
└── usecases-roles/        # Role management

// Domain Layer
├── entities/              # User, Account, Address, Role entities
└── repository/            # Repository interfaces

// Infrastructure
├── repositories/          # Repository implementations
├── parsers/              # Data transformation utilities
└── vo/                   # Value objects
```

**Key Features:**
- 👤 Complete user lifecycle management
- 🏢 Admin and standard user roles
- 📍 Address management with state/city catalogs
- 💳 Account and transfer contact management
- 📋 Transaction clarification system

### 💰 Financial Operations Contexts

#### Transfers Context (transfers/)
**Purpose**: Core money transfer functionality
- 💸 Transfer creation and processing
- 🔄 Background job processing with Bull Queue
- ✅ Transaction confirmation workflows

#### Transfer Orders Context (transfer-orders/)
**Purpose**: Transfer order management and processing
- 📋 Order creation and validation
- 💰 Account balance checking
- 🔄 Incoming and outgoing transfer processing
- 🌐 Webhook handling for external integrations

#### Dock Integration (dock/, dock-cards/)
**Purpose**: Integration with Dock payment platform
- 🏦 Core banking operations
- 👤 Legal person management
- 💳 Card creation, control, and CVV operations
- 📊 Transaction processing

### 🔔 Communication Contexts

#### Notifications Context (notifications/)
**Purpose**: Comprehensive notification system
- 🔗 Webhook processing and triggers
- 📱 User notification preferences
- 📧 Email notifications integration

#### OTP Context (otp/, transfer-otp/, reset-password-otp/, users-otp/)
**Purpose**: One-time password management
- 🔢 OTP generation and verification
- 📱 Transfer confirmation OTPs
- 🔑 Password reset OTPs
- 👤 User verification OTPs

## 🔧 VERIFIED CONFIGURATION MANAGEMENT

### Database Configuration
```typescript
// TypeORM Configuration
src/contexts/shared/infrastructure/database/
├── data-source.ts         # Main data source configuration
├── database.providers.ts  # Database providers
└── migrations/           # Database migration files
```

### Environment Configuration
```typescript
// Configuration modules
├── ConfigModule.forRoot() # Global configuration
├── JwtModule.register()   # JWT configuration  
├── TypeOrmModule.forRoot() # Database configuration
└── BullModule.forRoot()   # Queue configuration
```

### Redis Configuration
```typescript
// Redis integration
├── IORedisModule         # Redis connection
├── BullModule           # Queue processing
└── Custom Redis utils   # Caching utilities
```

## ⚠️ DEVELOPMENT BEST PRACTICES

### Code Organization
```bash
# Follow the established pattern for new contexts
src/contexts/[context-name]/
├── application/[usecase-name]/
│   ├── [usecase-name].dto.ts
│   └── [usecase-name].usecase.ts
├── domain/
├── infrastructure/
└── module/
```

### Testing Strategy
- **Unit Tests**: Each use case and service should have corresponding `.spec.ts` files
- **Integration Tests**: Test repository implementations and database interactions
- **E2E Tests**: Test complete API workflows in `/test` directory

### Security Considerations
- **Never commit sensitive data**: Use environment variables
- **Validate all inputs**: Use class-validator decorators
- **Implement proper authentication**: Use custom guards and decorators
- **Hash passwords**: Use Argon2 for password hashing
- **Secure API endpoints**: Apply proper authentication and authorization

## 🤝 COLLABORATIVE DEVELOPMENT

### Context-Level Development
```bash
# Working on specific contexts
# Users context
cd src/contexts/users/

# Auth context  
cd src/contexts/auth/

# Transfers context
cd src/contexts/transfers/
```

### Code Quality Standards (Verified)
- **TypeScript**: Strict mode enabled with ES2021 target
- **ESLint**: Configured with Prettier integration
- **Jest**: Comprehensive test coverage requirements
- **Class Validator**: Input validation on all DTOs
- **UTF-8**: All source files and resources encoding

## 🎯 SUCCESS METRICS

The Finberry backend tracks:
- ✅ **Code quality**: ESLint and Prettier compliance
- ✅ **Test coverage**: Comprehensive unit and E2E tests  
- ✅ **Type safety**: Full TypeScript coverage
- ✅ **Security**: Authentication and authorization coverage
- ✅ **Performance**: Redis caching and queue processing
- ✅ **Documentation**: Swagger API documentation

---

## 🚨 FINAL REMINDER

**CRITICAL**: Always run `npm run lint && npm run test` before committing any changes. This ensures code quality, type safety, and prevents breaking existing functionality. This is not optional - it's a mandatory part of the development workflow!

*Powered by Finberry Financial Platform - NestJS Clean Architecture Implementation*