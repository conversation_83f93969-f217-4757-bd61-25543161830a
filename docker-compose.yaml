version: "3.8"
services:
  redis:
    image: "redis:latest"
    command: ["redis-server", "--requirepass", "${REDIS_PASSWORD}"]
    ports:
      - "6379:6379"
    environment:
      - REDIS_PASSWORD=4gxpuAC3yq6nT29Y4Ze
    volumes:
      - redis_data:/data
  postgres:
    image: "postgres:latest"
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=pass
      - POSTGRES_DB=dev_finberry
    volumes:
      - postgres_data:/var/lib/postgresql/data
volumes:
  redis_data:
  postgres_data: