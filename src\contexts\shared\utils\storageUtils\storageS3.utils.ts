import {
  S3Client,
  GetObjectCommand,
  PutObjectCommand,
  CopyObjectCommand,
  DeleteObjectCommand,
} from '@aws-sdk/client-s3';
import { Injectable } from '@nestjs/common';
import { Readable } from 'stream';
import { ConfigService } from '@nestjs/config';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

@Injectable()
export class StorageS3Utils {
  private s3Client: S3Client;
  private tempUploadDir: string = 'documentos/temp';

  constructor(private readonly configService: ConfigService) {
    this.s3Client = new S3Client({
      region: this.configService.get<string>('S3_REGION'),
      credentials: {
        accessKeyId: this.configService.get<string>('S3_ACCESS_KEY'),
        secretAccessKey: this.configService.get<string>('S3_SECRET_KEY'),
      },
    });
  }

  async getTemplateFromS3(fileName: string): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: this.configService.get('S3_BUCKET'),
      Key: fileName,
    });
    const data = await this.s3Client.send(command);
    const stream = data.Body as Readable;
    const chunks: Buffer[] = [];

    for await (const chunk of stream) {
      chunks.push(chunk);
    }

    return Buffer.concat(chunks).toString('utf-8');
  }

  async createTempFile(file: Express.Multer.File): Promise<string> {
    const fileId = crypto.randomUUID();
    const fileExtension = this.getFileExtension(file.originalname);
    const tempKey = `${this.tempUploadDir}/${fileId}${fileExtension}`;

    await this.s3Client.send(
      new PutObjectCommand({
        Bucket: this.configService.get('S3_BUCKET'),
        Key: tempKey,
        Body: file.buffer,
        ContentType: file.mimetype,
        Metadata: {
          originalname: file.originalname,
        },
      }),
    );

    return fileId.concat(fileExtension);
  }

  private getFileExtension(filename: string): string {
    const ext = filename.slice(((filename.lastIndexOf('.') - 1) >>> 0) + 1);
    return ext ? `${ext}` : '';
  }

  async moveToPermamentLocation(
    fileId: string,
    adminId: string,
  ): Promise<void> {
    const tempKey = `${this.tempUploadDir}/${fileId}`;
    const permanentKey = `documentos/admin/${adminId}/${fileId}`;

    try {
      await this.s3Client.send(
        new CopyObjectCommand({
          Bucket: this.configService.get('S3_BUCKET'),
          CopySource: `${this.configService.get('S3_BUCKET')}/${tempKey}`,
          Key: permanentKey,
        }),
      );

      await this.s3Client.send(
        new DeleteObjectCommand({
          Bucket: this.configService.get('S3_BUCKET'),
          Key: tempKey,
        }),
      );
    } catch (error) {
      throw new Error(`Error moving file: ${error.message}`);
    }
  }

  async moveFileToPermanentLocation(
    fileId: string,
    path: string,
  ): Promise<void> {
    const tempKey = `${this.tempUploadDir}/${fileId}`;
    const permanentKey = `documentos/${path}/${fileId}`;

    try {
      await this.s3Client.send(
        new CopyObjectCommand({
          Bucket: this.configService.get('S3_BUCKET'),
          CopySource: `${this.configService.get('S3_BUCKET')}/${tempKey}`,
          Key: permanentKey,
        }),
      );

      await this.s3Client.send(
        new DeleteObjectCommand({
          Bucket: this.configService.get('S3_BUCKET'),
          Key: tempKey,
        }),
      );
    } catch (error) {
      throw new Error(`Error moving file: ${error.message}`);
    }
  }

  async generatePresignedUrl(s3Key: string): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: this.configService.get('S3_BUCKET'),
      Key: s3Key,
    });

    return await getSignedUrl(this.s3Client, command, { expiresIn: 3600 });
  }

  async deleteDocument(s3Key: string): Promise<void> {
    const command = new DeleteObjectCommand({
      Bucket: this.configService.get('S3_BUCKET'),
      Key: this.tempUploadDir.concat('/').concat(s3Key),
    });
    await this.s3Client.send(command);
  }

  async uploadFileToPath(
    file: Express.Multer.File,
    s3Path: string,
  ): Promise<{ key: string; url: string }> {
    const fileId = crypto.randomUUID();
    const extension = this.getFileExtension(file.originalname);
    const key = `${s3Path}/${fileId}${extension}`;

    // 🔐 clean the originalname
    const cleanOriginalName = file.originalname.replace(/[^\w.\-]/g, '_');

    await this.s3Client.send(
      new PutObjectCommand({
        Bucket: this.configService.get('S3_BUCKET'),
        Key: key,
        Body: file.buffer,
        ContentType: file.mimetype,
        Metadata: {
          originalname: cleanOriginalName,
        },
      }),
    );

    const url = await this.generatePresignedUrl(key);
    return { key, url };
  }
}
