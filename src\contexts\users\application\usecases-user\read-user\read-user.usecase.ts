import { Injectable, NotFoundException } from '@nestjs/common';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { Users } from 'src/contexts/users/domain/entities/users.entity';
import { UsersRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/users.repository.impl';
import * as argon2 from 'argon2';
import { ConveniaUserVO } from 'src/contexts/users/infrastructure/vo/user.vo';
import { UserParser } from 'src/contexts/users/infrastructure/parsers/user.parser';
import { ResUserWithAdminSettingsDto } from './read-user-with-admin-settings.dto';

@Injectable()
export class ReadUserUseCase {
  constructor(private readonly usersRepositoryImpl: UsersRepositoryImpl) {}

  async execute(id: string): Promise<ApiResponseDto> {
    return this.usersRepositoryImpl.findById(id);
  }

  async executeEmail(email: string): Promise<Users | null> {
    const user = await this.usersRepositoryImpl.findByEmail(email);

    if (user) delete user.password;

    return user;
  }

  async executeEmailToCheckAllUserData(email: string): Promise<Users | null> {
    const user = await this.usersRepositoryImpl.findByEmail(email);

    return user;
  }

  async findUserWithAdminSettingsByEmail(
    email: string,
  ): Promise<ResUserWithAdminSettingsDto> {
    return await this.usersRepositoryImpl.findUserWithAdminSettingsByEmail(
      email,
    );
  }

  async executeValidatePassword(
    email: string,
    password: string,
  ): Promise<boolean> {
    const user = await this.executeEmail(email);
    if (!user) return false;
    const isValidPassword = await argon2.verify(user.password, password);
    if (!isValidPassword) return false;
    return false;
  }

  async executeConveniaUser(id: string): Promise<ConveniaUserVO> {
    const user = await this.usersRepositoryImpl.findConveniaUserById(id);

    if (!user) throw new NotFoundException('Usuario no encontrado');

    return UserParser.parseToConveniaUser(
      user,
      user.relUserRoleAdmins.map((r) => r.role),
    );
  }

  async executeIsEmailRegistered(email: string): Promise<boolean> {
    const user = await this.usersRepositoryImpl.findByEmail(email);
    return !!user;
  }

  async findUserAccountById(id: string): Promise<Users | null> {
    return this.usersRepositoryImpl.findUserAccountById(id);
  }
}
