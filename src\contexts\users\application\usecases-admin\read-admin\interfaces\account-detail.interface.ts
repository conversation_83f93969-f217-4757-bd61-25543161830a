export interface DockAccountDetail {
    content:  Content[];
    metadata: Metadata;
}

export interface Content {
    id:                     string;
    client_id:              string;
    book_id:                string;
    external_account_id:    string;
    legacy_account_id:      null;
    min_balance:            number;
    creation_date:          Date;
    account_group_instance: AccountGroupInstance;
    account_config:         AccountConfig;
    status:                 Status;
    sub_account_instances:  SubAccountInstance[];
}

export interface AccountConfig {
    id:                   string;
    name:                 string;
    is_mandatory_account: boolean;
    default_asset:        Asset;
    account_subtype:      AccountSubtype;
}

export interface AccountSubtype {
    id:               string;
    name:             string;
    internal_id:      string;
    high_performance: boolean;
    credit_limit:     CreditLimit;
    account_type:     Status;
}

export interface Status {
    id:          string;
    name:        string;
    internal_id: string;
}

export interface CreditLimit {
    id:   string;
    name: string;
}

export interface Asset {
    id:   string;
    name: string;
    code: string;
}

export interface AccountGroupInstance {
    id:                   string;
    person_id:            string;
    account_group_config: AccountGroupConfig;
}

export interface AccountGroupConfig {
    id:         string;
    name:       string;
    product_id: string;
}

export interface SubAccountInstance {
    id:                         string;
    sub_account_config:         SubAccountConfig;
    balance_category_instances: BalanceCategoryInstance[];
}

export interface BalanceCategoryInstance {
    id:                      string;
    balance_category_config: BalanceCategoryConfig;
    balance_type_instances:  BalanceTypeInstance[];
}

export interface BalanceCategoryConfig {
    id:                            string;
    is_mandatory_balance_category: boolean;
    name:                          string;
    balance_category:              CreditLimit;
}

export interface BalanceTypeInstance {
    id:                  string;
    current_balance:     number;
    credit_limit:        number;
    available_resource:  number;
    balance_type_config: BalanceTypeConfig;
}

export interface BalanceTypeConfig {
    id:                        string;
    is_mandatory_balance_type: boolean;
    name:                      string;
    is_internal:               boolean;
    balance_type:              BalanceType;
}

export interface BalanceType {
    id:                   string;
    name:                 string;
    is_balance_operation: boolean;
    allow_credit_limit:   boolean;
    is_internal:          boolean;
}

export interface SubAccountConfig {
    id:                       string;
    in_principal_asset:       boolean;
    is_mandatory_sub_account: boolean;
    is_categorized:           boolean;
    name:                     string;
    asset:                    Asset;
}

export interface Metadata {
    pagination: Pagination;
    sort:       Sort;
}

export interface Pagination {
    page:  number;
    limit: number;
}

export interface Sort {
    field: string;
    order: string;
}
