import { Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  ValidateNested,
  IsArray,
  IsOptional,
  IsNumber,
  IsEnum,
} from 'class-validator';
import { CreateAddressDto } from '../../usecases-address/create-address/create-address.dto';
import { UpdateUserDto } from '../../usecases-user/update-user/update-user.dto';
import { ApiProperty } from '@nestjs/swagger';
import { AdminDocumentTypeEnum } from 'src/contexts/users/domain/entities/admin-documents.entity';

export class UpdateAdminDto {
  @ApiProperty()
  @IsString()
  company_name: string;

  @ApiProperty()
  @IsString()
  alias: string;

  @ApiProperty()
  @IsString()
  rfc: string;

  @ApiProperty()
  @IsNumber()
  spei_in: number;

  @ApiProperty()
  @IsNumber()
  spei_out: number;

  @ApiProperty()
  @IsNumber()
  target_refound: number;

  @ApiProperty()
  @IsString()
  ambassador: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional() 
  num_asigned_cards: number = 0;

  @ApiProperty()
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => CreateAddressDto)
  address: CreateAddressDto;

  @ApiProperty()
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => UpdateUserDto)
  user: UpdateUserDto;

  @ApiProperty()
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AdminIds)
  enterprises: AdminIds[] | null;

  @ApiProperty()
  @IsOptional()
  @IsArray()
  @Type(() => file)
  files: file[];
}

class AdminIds {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  adminId: string;
}

class file {
  @ApiProperty()
  @IsString()
  file: string;

  @ApiProperty()
  @IsEnum(AdminDocumentTypeEnum)
  type: AdminDocumentTypeEnum;
}