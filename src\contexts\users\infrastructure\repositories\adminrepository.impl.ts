import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';
import { AdminRepository } from '../../domain/repository/admin.repository';
import { Admin } from '../../domain/entities/admin.entity';
import { UpdateAdminDto } from '../../application/usecases-admin/update-admin/update-admin.dto';
import { CreateAdminDto } from '../../application/usecases-admin/create-admin/create-admin.dto';
import { InternalServerErrorException } from '@nestjs/common';
import {
  AdminFilterDto,
  ReadUsersByAdminFilterDto,
} from '../../application/usecases-admin/read-admin/read-admins-filter.dto';
import { ReadAdminTotalAmountDto } from '../../application/usecases-admin/read-total-amount/read-total-amount.dto';
import { RoleEnum } from 'src/contexts/shared/enums/roles.enum';

export class AdminRepositoryImpl implements AdminRepository {
  constructor(
    @InjectRepository(Admin)
    private readonly adminRepository: Repository<Admin>,
  ) {}

  async save(admin: CreateAdminDto): Promise<Admin> {
    try {
      const adminEntity = this.adminRepository.create({
        ...admin,
        manager: { id: admin.manager },
      });
      const res = await this.adminRepository.save(adminEntity);
      return res;
    } catch (error) {
      console.log('Error occurred while saving admin:', error.message);
      throw new InternalServerErrorException(
        'No se pudo crear el usuario',
        error,
      );
    }
  }

  async findAll({
    page,
    limit,
    q,
    groupId,
    orderBy,
  }: AdminFilterDto): Promise<[Admin[], number]> {
    const qb = this.adminRepository
      .createQueryBuilder('admin')
      .leftJoinAndSelect('admin.manager', 'manager')
      .leftJoinAndSelect('manager.personIDDock', 'personIDDock')
      .leftJoinAndSelect('personIDDock.transfers', 'transfer')
      .leftJoinAndSelect('manager.relUserRoleAdmins', 'rel')
      .leftJoinAndSelect('rel.role', 'role')
      .leftJoinAndSelect('rel.admin', 'reladmin')
      .where('admin.isDeleted = :isDeleted', { isDeleted: false })
      .skip((page - 1) * limit)
      .take(limit);

    if (q) {
      qb.andWhere(
        'admin.company_name LIKE :q OR admin.rfc LIKE :q or manager.email LIKE :q',
        { q: `%${q}%` },
      );
    }

    if (groupId) {
      qb.andWhere('admin.group_id = :groupId', { groupId });
    }

    if (orderBy) {
      qb.orderBy(`admin.${orderBy}`, 'DESC');
    }

    return await qb.getManyAndCount();
  }

  async findAllIds({ groupId }: ReadAdminTotalAmountDto): Promise<Admin[]> {
    const qb = this.adminRepository
      .createQueryBuilder('admin')
      .leftJoin('admin.manager', 'manager')
      .leftJoin('manager.personIDDock', 'personIDDock')
      .select(['admin.id', 'manager.id', 'personIDDock.id'])
      .where('admin.isDeleted = :isDeleted', { isDeleted: false });

    if (groupId) qb.andWhere('admin.group_id = :groupId', { groupId });

    return await qb.getMany();
  }

  async findById(id: string): Promise<Admin | null> {
    const qb = this.adminRepository
      .createQueryBuilder('admin')
      .leftJoinAndSelect('admin.manager', 'manager')
      .leftJoinAndSelect('manager.personIDDock', 'personIDDock')
      .leftJoinAndSelect('personIDDock.transfers', 'transfers')
      .leftJoinAndSelect('manager.address', 'address')
      .leftJoinAndSelect('admin.documents', 'documents')
      .leftJoinAndSelect('documents.file', 'file')
      .leftJoinAndSelect('admin.relUserRoleAdmins', 'relUserRoleAdmin')
      .leftJoinAndSelect('relUserRoleAdmin.user', 'user')
      .leftJoinAndSelect('user.address', 'addressUser')
      .leftJoinAndSelect('relUserRoleAdmin.role', 'role')
      .leftJoinAndSelect('manager.relUserRoleAdmins', 'rel')
      .leftJoinAndSelect('rel.role', 'roleName')
      .leftJoinAndSelect('rel.admin', 'reladmin')
      .where('admin.id = :id', { id })
      .andWhere('admin.isDeleted = :isDeleted', { isDeleted: false });
    return await qb.getOne();
  }

  async findAllByUserId(userId: string, role: RoleEnum): Promise<Admin[]> {
    const query = this.adminRepository
      .createQueryBuilder('admin')
      .leftJoinAndSelect('admin.manager', 'manager')
      .leftJoinAndSelect('admin.relUserRoleAdmins', 'relUserRoleAdmin')
      .leftJoinAndSelect('relUserRoleAdmin.user', 'user')
      .leftJoinAndSelect('relUserRoleAdmin.role', 'role')
      .where('admin.isDeleted = :isDeleted', { isDeleted: false });

    if (role !== RoleEnum.CONVENIA_ADMIN) {
      query.andWhere('user.id = :userId', { userId });
    }

    query.andWhere('role.id IS NOT NULL').orderBy('admin.id', 'ASC');

    return await query.getMany();
  }

  async findByMembershipNumber(
    membership_number: number,
  ): Promise<ApiResponseDto> {
    try {
      const res = await this.adminRepository
        .createQueryBuilder('admin')
        .leftJoinAndSelect('admin.relUserRoleAdmins', 'relUserRoleAdmin') // tabla rel_user_role_admin
        .leftJoinAndSelect('relUserRoleAdmin.user', 'user') // userId
        .leftJoinAndSelect('user.address', 'address') // addressId
        .where('admin.membership_number = :membership_number', {
          membership_number,
        })
        .andWhere('admin.isDeleted = :isDeleted', { isDeleted: false })
        .getOne();
      if (!res) {
        return ResponseUtil.error(
          'Admin no encontrado',
          'Usuario Admin con numero de membresia proporcionado no existe',
          404,
        );
      }
      return ResponseUtil.success('Usuario obtenido exitosamente', res, 200);
    } catch (error) {
      return ResponseUtil.error('No se pudo obtener el usuario', error, 400);
    }
  }

  async findByIdDetails(id: string): Promise<Admin> {
    return this.adminRepository
      .createQueryBuilder('admin')
      .leftJoinAndSelect('admin.manager', 'manager')
      .leftJoinAndSelect('manager.personIDDock', 'personIDDock')
      .leftJoinAndSelect('manager.address', 'address')
      .where('admin.id = :id', { id })
      .andWhere('admin.isDeleted = :isDeleted', { isDeleted: false })
      .getOne();
  }

  async update(id: string, admin: Partial<UpdateAdminDto>): Promise<void> {
    try {
      await this.adminRepository.update(id, admin);
    } catch (error) {
      console.log(error);
      throw new InternalServerErrorException('INTERNAL SERVER ERROR');
    }
  }

  async remove(id: string): Promise<ApiResponseDto> {
    try {
      const res = await this.adminRepository.delete(id);
      return ResponseUtil.success('Usuario eliminado exitosamente', res, 200);
    } catch (error) {
      return ResponseUtil.error('No se pudo eliminar el usuario', error, 400);
    }
  }

  async findByRFC(rfc: string): Promise<Admin | null> {
    return this.adminRepository.findOneBy({ rfc , isDeleted: false });
  }

  async findByMembership(membership: number): Promise<Admin | null> {
    return this.adminRepository.findOneBy({ membership_number: membership, isDeleted: false });
  }

  async findAllBasic(): Promise<Admin[]> {
    return await this.adminRepository.find({ where: { isDeleted: false } });
  }

  async saveAdmin(admin: Admin): Promise<Admin> {
    return await this.adminRepository.save(admin);
  }

  async countByRFC(rfc: string): Promise<number> {
    return await this.adminRepository
      .createQueryBuilder('admin')
      .where('admin.rfc LIKE :rfc', { rfc: `${rfc}%` })
      .andWhere('admin.isDeleted = :isDeleted', { isDeleted: false })
      .getCount();
  }

  async getUsers(
    id: string,
    { page, limit, q }: ReadUsersByAdminFilterDto,
  ): Promise<Admin> {
    const qb = this.adminRepository
      .createQueryBuilder('admin')
      .where('admin.id = :id', { id })
      .andWhere('admin.isDeleted = :isDeleted', { isDeleted: false })
      .leftJoinAndSelect('admin.users', 'users')
      .innerJoinAndSelect('users.relUserRoleAdmins', 'relUserRoleAdmins')
      .innerJoinAndSelect('relUserRoleAdmins.role', 'role')
      .skip((page - 1) * limit)
      .take(limit);

    if (q) {
      qb.andWhere('users.name LIKE :q OR users.email LIKE :q', { q: `%${q}%` });
    }

    return await qb.getOne();
  }

  async findManagerAccount(dto: ReadAdminTotalAmountDto): Promise<Admin[]> {

    const { adminId, groupId } = dto;

    const qb = this.adminRepository
      .createQueryBuilder('admin')
      .leftJoin('admin.manager', 'manager')
      .innerJoin('manager.personIDDock', 'personIDDock')
      .innerJoin('personIDDock.accounts', 'accounts')
      .where('admin.isDeleted = :isDeleted', { isDeleted: false })
      
    .select(['admin.id', 'manager.id', 'personIDDock.personExtID', 'accounts.accountExtID']);

    if (adminId) {
      qb.andWhere('admin.id = :adminId', { adminId });
    }

    if (groupId) {
      qb.andWhere('admin.group_id = :groupId', { groupId });
    }

    return await qb.getMany();
  }

  async softDelete(id: string): Promise<void> {
        await this.adminRepository.update(id, { isDeleted: true, deletedAt: new Date() });
    }

  async getTotalCards(id: string): Promise<number> {
    const queryRaw = `
                 select DISTINCT
                  ac2.card_dock_id
                  from
                    "admin" as a
                  left join "users" as u on
                    u."adminDataId" = a.id
                  left join person p on
                    p.id = u."personIDDockId"
                  left join account ac on
                    ac."personIDDockId" = p.id
                  left join account_cards ac2 on
                    ac2.account_id = ac.id
                  where
                    a.id = $1
                    and ac2.enabled = true
                `;

    const cardIds = await this.adminRepository.query(queryRaw, [id]);

    return cardIds.length || 0;
  }
}
