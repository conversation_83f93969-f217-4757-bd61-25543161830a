import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { ResponseFindUserDto } from 'src/contexts/transfer-orders/application/transfer-orders-in/transfer-orders-in.dto';

export interface PersonRepository {
  save(id: string): Promise<ApiResponseDto>;
  findAll(): Promise<ApiResponseDto>;
  findById(id: string): Promise<ApiResponseDto>;
  update(id: string, enabled: boolean): Promise<ApiResponseDto>;
  remove(id: string): Promise<ApiResponseDto>;
  findUserWithClabe(clabe: string): Promise<ResponseFindUserDto>;
  findUserWithEmail(email: string): Promise<ResponseFindUserDto>;
  findUserWithClabeOnlyCompany(clabe: string): Promise<ResponseFindUserDto>;
  findUserWithConveniaAccount(account: string): Promise<ResponseFindUserDto>;
}
