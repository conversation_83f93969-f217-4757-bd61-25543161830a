import { Injectable } from '@nestjs/common';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { PersonRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/person.repository.impl';

@Injectable()
export class UpdatePersonUseCase {
  constructor(private readonly personRepositoryImpl: PersonRepositoryImpl) {}

  async execute(id: string, enabled: boolean): Promise<ApiResponseDto> {
    return this.personRepositoryImpl.update(id, enabled);
  }
}
