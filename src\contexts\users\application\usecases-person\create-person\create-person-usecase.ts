import { Injectable } from '@nestjs/common';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { PersonRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/person.repository.impl';
import { CreateAccountUseCase } from '../../usecases-account/create-account/create-account-usecase';
import { CreateAccountDto } from '../../usecases-account/create-account/create-account.dto';

@Injectable()
export class CreatePersonUseCase {
  constructor(
    private readonly personRepositoryImpl: PersonRepositoryImpl,
    readonly createAccountUseCase: CreateAccountUseCase,
  ) {}

  async execute(id: string, idAccount: string): Promise<ApiResponseDto> {
    const person = await this.personRepositoryImpl.save(id);
    if (person) {
      await this.createAccountUseCase.execute({
        accountExtID: idAccount,
        personIDDock: person.data.id,
      });
      return person;
    }
    return;
  }
}
