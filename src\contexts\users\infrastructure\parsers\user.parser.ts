import { Injectable } from "@nestjs/common";
import { Users } from '../../domain/entities/users.entity';
import { Rol } from "../../domain/entities/rol.entity";
import { AdminUserVO, ConveniaUserVO, UserAccountDeatilVO, UserAccountVO } from "../vo/user.vo";
import { AdminParser } from "./admin.parser";
import { Admin } from "../../domain/entities/admin.entity";

@Injectable()
export class UserParser {

    static parseToConveniaUser(user: Users, roles: Rol[], admin?: Admin | null) : ConveniaUserVO {
        return {
            id: user.id,
            name: user.name,
            createdAt: user.created_at,
            email: user.email.toLocaleLowerCase(),
            phone: user.phone,
            roles,
            admin: admin ? AdminParser.parseBasicAdmin(admin) : null
        }
    }

    static parseToUserAccount(user: Users, amount: number) : UserAccountVO {

        let isDeleteable = true;

        if( user.relUserRoleAdmins && user.relUserRoleAdmins.some(rel => rel.role !== null) )
            isDeleteable = false;

        return {
            id: user.id,
            name: user.name,
            email: user.email,
            clabe: user.personIDDock?.transfers[0]?.clabe,
            adminAlias: user.admin_data.alias,
            amount,
            speiIn: user.isSpeiInEnabled,
            speiOut: user.isSpeiOutEnabled,
            enabled: user.enabled,
            isDeleteable,
            roles: user.relUserRoleAdmins && user.relUserRoleAdmins.some(rel => rel.role !== null) ? user.relUserRoleAdmins.map(rel => rel.role) : []
        }
    }

    static parseToUserAccountDeatail(user:Users) : UserAccountDeatilVO {
        return {
            id: user.id,
            name: user.name,
            email: user.email,
            phone: user.phone,
            admin: user.admin_data ? AdminParser.parseBasicAdmin(user.admin_data) : null
        }
    }

    static parseToAdminUser(user: Users) : AdminUserVO {
        return {
            id: user.id,
            email: user.email.toLocaleLowerCase(),
            name: user.name,
            createdAt: user.created_at,
            role: user.relUserRoleAdmins ? {
                id: user.relUserRoleAdmins[0].role.id, 
                name: user.relUserRoleAdmins[0].role.name
            } : null
        }
    }
}
