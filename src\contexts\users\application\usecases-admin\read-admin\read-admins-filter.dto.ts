import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
} from 'class-validator';
import { PaginationDto } from 'src/contexts/shared/dto/pagination.dto';

enum AdminFilterOrderByEnum {
  createdAt = 'createdAt',
}
export class AdminFilterDto extends PaginationDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  q?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  groupId?: number;

  @ApiProperty()
  @IsEnum(AdminFilterOrderByEnum)
  @IsOptional()
  orderBy?: AdminFilterOrderByEnum;
}

export class AdminFilterByUserIdDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  userId?: string;
}

export class ReadUsersByAdminFilterDto extends PaginationDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  q?: string;
}
