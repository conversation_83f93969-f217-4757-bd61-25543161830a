import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CatalogsController } from '../infrastructure/http/catalogs.controller';
import { State } from 'src/contexts/catalogs/domain/entities/state.entity';
import { City } from 'src/contexts/catalogs/domain/entities/city.entity';
import { ReadStatesUseCase } from '../application/usecases-states/read-states/read-states.usecase';
import { ReadCitiesUseCase } from '../application/usecases-states/read-cities/read-cities.usecase';
import { StateRepositoryImpl } from 'src/contexts/catalogs/infrastructure/repositories/state.repository';
import { CityRepositoryImpl } from 'src/contexts/catalogs/infrastructure/repositories/city.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([State, City]),
  ],
  controllers: [CatalogsController],
  providers: [ReadStatesUseCase, ReadCitiesUseCase, StateRepositoryImpl, CityRepositoryImpl],
  exports: [TypeOrmModule],
})
export class CatalogsModule {}