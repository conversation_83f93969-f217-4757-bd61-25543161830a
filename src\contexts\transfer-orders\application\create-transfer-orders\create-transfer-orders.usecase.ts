import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { DateTime } from 'luxon';

/* ** DTOs ** */
import {
  ParamsCreateOrderDto,
  PayloadCreateOrderDto,
  PayloadCreateSignDto,
  ResponseCreateOrderDto,
  ResponseSpeiOutRequestDto,
  SpeiOutRequestDto,
} from './create-transfer-orders.dto';

/* ** Utils ** */
import { TransferEncryptSign } from 'src/contexts/shared/utils/transfer-encrypt-sign/transfer-encrypt-sign.utils';

/* ** Repositories ** */
import { PersonRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/person.repository.impl';
import { UserTransferRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/user-transfer.repository.impl';

/* ** Errors ** */
// import { ERROR_CREATE_ORDERS } from '../../infrastructure/error/transfer-orders.error';

@Injectable()
export class CreateOrdersTransfer {
  /* ** Zone Horaria ** */
  private zonaHoraria: string = 'America/Mexico_City';

  constructor(
    private readonly config: ConfigService,
    private readonly sign: TransferEncryptSign,
    private readonly person: PersonRepositoryImpl,
    private readonly userTransfer: UserTransferRepositoryImpl,
  ) {}

  async createTransferOrder(
    order: ParamsCreateOrderDto,
  ): Promise<ResponseCreateOrderDto> {
    /* ** We format the order ** */
    const payload: PayloadCreateOrderDto = {
      beneficiaryName: order?.beneficiaryName?.trim() || '',
      beneficiaryAccount: order.beneficiaryAccount,
      beneficiaryAccountType: order.beneficiaryAccountType,
      beneficiaryBank: order.beneficiaryBank,
      beneficiaryUid: order.beneficiaryUid,
      amount: order.amount,
      concept: order?.concept?.trim() || '',
      payerName: order.payerName,
      payerAccount: order.payerAccount,
      payerBank: order.payerBank,
      payerUid: order.payerUid,
      numericalReference: this.generateReference(),
      payerAccountType: order.payerAccountType,
      paymentType: order.paymentType,
      paymentDay: DateTime.now().toUTC().toMillis(),
      sign: '',
    };

    /* ** We create the sign ** */
    const sign = this.createSing({
      ...payload,
      schedule_order: null,
    });

    /* ** encrypt the sign ** */
    const encrypt_sign = await this.sign.encryptSign({ sign });
    payload.sign = encrypt_sign;

    const data_order = await this.requestTransferOrderV2(payload);

    return data_order;
  }

  async execueteSpeiOutCore(
    body: SpeiOutRequestDto,
  ): Promise<ResponseSpeiOutRequestDto> {
    const user = await this.person.findUserWithEmail(body.email);

    if (!user) {
      return {
        order_canceled: false,
        order_created: false,
        order_sent: false,
      };
    }

    if (!user.spei_out) {
      return {
        order_canceled: false,
        order_created: false,
        order_sent: false,
      };
    }

    const payload: PayloadCreateOrderDto = {
      beneficiaryName: body?.beneficiaryName?.trim() || '',
      beneficiaryAccount: body.num_clabe,
      beneficiaryAccountType: 40,
      beneficiaryBank: body.beneficiaryBank,
      beneficiaryUid: '',
      amount: body.amount,
      concept: body?.description?.trim() || '',
      payerName: user.name,
      payerAccount: user.clabe,
      payerBank: '684',
      numericalReference: this.generateReference(),
      payerAccountType: 40,
      paymentType: 1,
      paymentDay: DateTime.now().toUTC().toMillis(),
      sign: '',
    };

    /* ** We create the sign ** */
    const sign = this.createSing({
      ...payload,
      schedule_order: null,
    });

    /* ** encrypt the sign ** */
    const encrypt_sign = await this.sign.encryptSign({ sign });
    payload.sign = encrypt_sign;

    const data_order = await this.requestTransferOrder(payload);

    await this.userTransfer.updateOrderSign({
      id: body.external_id,
      sign_transfer: sign,
      error_transfer: data_order?.error || null,
    });

    if (data_order && data_order.code === 200) {
      await this.userTransfer.updateOrderID({
        id: body.external_id,
        order_id: data_order?.data?.id,
      });
    } else if (data_order && data_order.code !== 200) {
      return {
        order_canceled: true,
        order_created: true,
        order_sent: true,
      };
    } else if (!data_order) {
      return {
        order_canceled: true,
        order_created: true,
        order_sent: true,
      };
    }

    /* ** Order Canceled ** */
    const order_canceled = data_order?.data?.cancel ?? false;
    /* ** Order Sent ** */
    const order_sent = data_order?.data?.sent ?? false;

    return {
      order_canceled,
      order_created: data_order?.code === 200 && true,
      order_sent,
    };
  }

  async createScheduleTransfer(
    order: ParamsCreateOrderDto,
  ): Promise<ResponseCreateOrderDto> {
    /* ** Genrate Payment day ** */
    const nowPush = DateTime.now()
      .setZone(this.zonaHoraria)
      .plus({ days: order.schedule_order });
    const paymentDay = nowPush.toMillis();

    /* ** We format the order ** */
    const payload: PayloadCreateOrderDto = {
      beneficiaryName: order.beneficiaryName,
      beneficiaryAccount: order.beneficiaryAccount,
      beneficiaryAccountType: order.beneficiaryAccountType,
      beneficiaryBank: order.beneficiaryBank,
      beneficiaryUid: order.beneficiaryUid,
      amount: order.amount,
      concept: order.concept,
      payerName: order.payerName,
      payerAccount: order.payerAccount,
      payerBank: order.payerBank,
      payerUid: order.payerUid,
      numericalReference: this.generateReference(),
      payerAccountType: order.payerAccountType,
      paymentType: order.paymentType,
      paymentDay,
      sign: '',
    };

    /* ** We create the sign ** */
    const sign = this.createSing({
      ...payload,
      schedule_order: nowPush.toFormat('yyyy-MM-dd'),
    });

    /* ** encrypt the sign ** */
    const encrypt_sign = await this.sign.encryptSign({ sign });
    payload.sign = encrypt_sign;

    const data_order = await this.requestTransferOrder(payload);

    return data_order;
  }

  private async requestTransferOrder(
    order: PayloadCreateOrderDto,
  ): Promise<ResponseCreateOrderDto> {
    try {
      console.log('order', order);
      const url = `${this.config.get('TRANSFER_URL')}/api/1.0/orders/`;
      const response = await axios.post(url, order, {
        headers: {
          'X-Custom-Auth': this.config.get('TRANSFER_TOKEN'),
          Accept: 'application/json',
        },
      });

      return response?.data ?? null;
    } catch (error) {
      console.log('error', error);
      return {
        code: error.status,
        error:
          JSON.stringify(error.response?.data) ||
          'Error al crear la orden de transferencia',
      };
    }
  }

  private async requestTransferOrderV2(
    order: PayloadCreateOrderDto,
  ): Promise<any> {
    try {
      const url = `${this.config.get('TRANSFER_URL')}/api/1.0/orders/`;
      const response = await axios.post(url, order, {
        headers: {
          'X-Custom-Auth': this.config.get('TRANSFER_TOKEN'),
          Accept: 'application/json',
        },
      });

      return response?.data ?? response;
    } catch (error) {
      console.log('error', error);
      throw new BadRequestException(
        error.response?.data || 'Error al crear la orden de transferencia',
      );
    }
  }

  private generateReference(): number {
    return Number(
      Array(7)
        .fill(0)
        .map(() => Math.floor(Math.random() * 10))
        .join(''),
    );
  }

  private createSing = (body: PayloadCreateSignDto): string => {
    /* ** Dates are not used in the signature ** */
    const now_order = body.schedule_order
      ? body.schedule_order
      : DateTime.fromMillis(body.paymentDay).toFormat('yyyy-MM-dd');

    /* ** We create the sign ** */
    const {
      beneficiaryName,
      beneficiaryUid,
      beneficiaryBank,
      beneficiaryAccount,
      beneficiaryAccountType,
      payerAccount,
      numericalReference,
      paymentType,
      concept,
      amount,
    } = body;

    const sing: string = `||${beneficiaryName}|${beneficiaryUid}|${beneficiaryBank}|${beneficiaryAccount}|${beneficiaryAccountType}|${payerAccount}|${numericalReference}|${now_order}|${paymentType}|${concept}|${amount}||`;

    return sing;
  };
}
