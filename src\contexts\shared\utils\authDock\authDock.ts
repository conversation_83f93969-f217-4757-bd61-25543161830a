import * as https from 'https';
import axios from 'axios';
import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import { Injectable } from '@nestjs/common';
import { Readable } from 'stream';
import { RedisService } from '../Redis/redis';
import { AuthTokenDto, NewTokenDto } from '../../interfaces/dtos/authDock.dto';
import { ConfigService } from '@nestjs/config';
import { ExpiresIn } from '../../interfaces/dtos/redis.dto';

@Injectable()
export class authTokenDock {
  /* ** S3 Client ** */
  private s3Client: S3Client;

  /* ** Constructor ** */
  constructor(
    private readonly redis: RedisService,
    private readonly configService: ConfigService,
  ) {
    /* ** Inicializamos el cliente de S3 ** */
    this.s3Client = new S3Client({
      region: this.configService.get<string>('S3_REGION'),
      credentials: {
        accessKeyId: this.configService.get<string>('S3_ACCESS_KEY'),
        secretAccessKey: this.configService.get<string>('S3_SECRET_KEY'),
      },
    });
  }

  async getAuthDock(): Promise<AuthTokenDto> {
    /* ** Get certificate ** */
    const [key, certificate, key_cards] = await Promise.all([
      this.getCertificatesS3('certificates/certificate.key'),
      this.getCertificatesS3('certificates/certificate.pem'),
      this.getCertificatesS3('certificates/private_key_data_sensible.pem'),
    ]);

    /* ** Get token ** */
    let token = await this.redis.getCache('authDock');

    if (!token) {
      /* ** Generate new token ** */
      token = await this.newToken({ certificate, key });

      if (!token) throw new Error('Error al obtener el token de AuthDock');

      this.redis.setCache({
        key: 'authDock',
        value: token,
        expiresIn: ExpiresIn.FIFTY_MINUTES,
      });
    }

    return { key, certificate, bearer_token: token, key_cards };
  }

  private async newToken(params: NewTokenDto): Promise<string> {
    /* ** Obtener credenciales ** */
    const username: string = this.configService.get('DOCK_USER');
    const password: string = this.configService.get('DOCK_PASS');

    /* ** cifrar credenciales en base64 ** */
    const auth: string = Buffer.from(`${username}:${password}`).toString(
      'base64',
    );

    /* ** Generamos el agente https ** */
    const httpsAgent: https.Agent = new https.Agent({
      cert: params.certificate,
      key: params.key,
      rejectUnauthorized: false,
    });

    const executeToken = async (): Promise<string> => {
      try {
        /* ** Generamos la petición ** */
        const response = await axios.post(
          `${this.configService.get('DOCK_URL_TOKEN')}/oauth2/token?grant_type=client_credentials`,
          {},
          {
            headers: {
              Authorization: `Basic ${auth}`,
            },
            httpsAgent,
          },
        );

        return response.data.access_token;
      } catch (error) {
        const code = error?.response?.status;
        this.redis.logError({
          nameController: 'AuthTokenDock',
          name: 'Error al obtener el token de AuthDock',
          error: JSON.stringify(error.response.data.error),
          key: 'api:logs:authDock',
          statusCode: code,
        });
        return null;
      }
    };

    const token = await executeToken();

    return token;
  }

  private async getCertificatesS3(fileName: string): Promise<Buffer> {
    const command = new GetObjectCommand({
      Bucket: this.configService.get('S3_BUCKET'),
      Key: fileName,
    });
    const data = await this.s3Client.send(command);
    const stream = data.Body as Readable;
    const chunks: Buffer[] = [];

    for await (const chunk of stream) {
      chunks.push(chunk);
    }

    return Buffer.concat(chunks);
  }
}
