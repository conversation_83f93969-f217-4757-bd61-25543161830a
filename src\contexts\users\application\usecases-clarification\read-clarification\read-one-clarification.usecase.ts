import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Clarification } from '../../../domain/entities/clarification.entity';
import { Repository } from 'typeorm';
import { StorageS3Utils } from 'src/contexts/shared/utils/storageUtils/storageS3.utils';
import { ClarificationVO } from 'src/contexts/users/infrastructure/vo/clarification.vo';

@Injectable()
export class ReadOneClarificationUseCase {
  constructor(
    @InjectRepository(Clarification)
    private readonly clarificationRepo: Repository<Clarification>,
    private readonly s3: StorageS3Utils,
  ) {}

  async execute(trackingNumber: string): Promise<ClarificationVO> {
    const clarification = await this.clarificationRepo.findOne({
      where: { trackingNumber },
      relations: ['createdBy', 'files'],
    });

    if (!clarification) {
      throw new NotFoundException('Clarification not found');
    }

    const fileData = await Promise.all(
      clarification.files.map(async (file) => {
        const url = await this.s3.generatePresignedUrl(file.file_url);
        return {
          id: file.id,
          file_name: file.file_name,
          file_url: url,
        };
      }),
    );

    return {
      trackingNumber: clarification.trackingNumber,
      type: clarification.type,
      description: clarification.description,
      status: clarification.status,
      createdAt: clarification.createdAt,
      user: {
        name: clarification.createdBy.name,
        email: clarification.createdBy.email,
        avatar: clarification.createdBy.email,
      },
      files: fileData,
    };
  }
}
