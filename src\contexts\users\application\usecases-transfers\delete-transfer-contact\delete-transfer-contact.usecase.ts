import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { Injectable } from '@nestjs/common';
import { TransferContactRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/transfer-contact.repository.impl';

@Injectable()
export class DeleteTransferContactUseCase {
  constructor(
    private readonly transferContactRepository: TransferContactRepositoryImpl,
  ) {}

  async execute(id: string): Promise<ApiResponseDto> {
    return await this.transferContactRepository.remove(id);
  }
}
