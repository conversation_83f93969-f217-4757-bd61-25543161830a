import { Injectable, NotFoundException } from '@nestjs/common';
import { ControlDeleteDockCardUseCase } from 'src/contexts/dock-cards/apps/delete-dock-card/delete-dock-card.usecase';
import { UsersRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/users.repository.impl';
import { Account } from '../../../domain/entities/account.entity';
import { RelUserRoleAdminRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/rel-user-role-admin.repository.impl';
import { Users } from 'src/contexts/users/domain/entities/users.entity';
import { DockLegalPersonService } from 'src/contexts/dock/infraestructure/services/dock-legal-person.service';
import { ParamsDockPayTransfertDto } from 'src/contexts/transfers/application/create-transaction/dockpay-transfer.dto';
import { DockpayTransferUseCase } from 'src/contexts/transfers/application/create-transaction/dockpay-transfer.usecase';

@Injectable()
export class DeleteUserUseCase {
  constructor(
    private readonly usersRepositoryImpl: UsersRepositoryImpl,
    private readonly controlDeleteDockCardUseCase: ControlDeleteDockCardUseCase,
    private readonly relUserRoleAdminRepositoryImpl: RelUserRoleAdminRepositoryImpl,
    private readonly dockLegalPersonService: DockLegalPersonService,
    private readonly dockpayTransferUseCase: DockpayTransferUseCase
  ) {}

  async execute(id: string, adminId: string): Promise<void> {
    const userDetail = await this.usersRepositoryImpl.findUserDetail(id);

    if(!userDetail)
      throw new NotFoundException('User not found');

    if(adminId && userDetail.relUserRoleAdmins && userDetail.relUserRoleAdmins.length > 1) {
      const rel = userDetail.relUserRoleAdmins.find(rel => rel.admin.id === adminId);
      if(!rel) {
        throw new NotFoundException('User does not belong to the specified admin');
      }
      await this.relUserRoleAdminRepositoryImpl.deleteById(rel.id);
    }else{

      await this.usersRepositoryImpl.enabledSPEI(id);
      
      if(userDetail.personIDDock && userDetail.personIDDock.accounts) {

        await this.trasnferAmountToConvenia(userDetail);

        await this.deleteDockCards(userDetail.personIDDock.accounts);

      }

      await this.usersRepositoryImpl.softDelete(id);

    }
  }

  public async deleteDockCards(accounts: Account[]): Promise<void> {
    for (const account of accounts) {
      for(const card of account.cards) {
        await this.controlDeleteDockCardUseCase.deleteCard(card.card_dock_id).catch(console.error);
      }
    }
  }

  public async trasnferAmountToConvenia(user: Users) {

    let amount = '0';

    const account = user.personIDDock.accounts[0];
    const dockAccountDetail = await this.dockLegalPersonService.getAccountDetail(account.accountExtID, user.personIDDock.personExtID).catch(() => null);

    if(!dockAccountDetail || !dockAccountDetail.content.length)
      amount = '0';

    else {
      amount = dockAccountDetail.content[0].sub_account_instances[0]
      .balance_category_instances[0].balance_type_instances[0]
      .available_resource;
    }

    if (amount) {
      const conveniaAdmin = await this.usersRepositoryImpl.findConveniaAdmin();

      const data: ParamsDockPayTransfertDto = {
        email: user.email,
        amount: parseFloat(amount),
        description: 'Cuenta eliminada, transferencia de saldo a Convenia',
        num_clabe: conveniaAdmin.convenia_account
      };

      await this.dockpayTransferUseCase.executePay(data).catch(console.error);

    }
  }
}
