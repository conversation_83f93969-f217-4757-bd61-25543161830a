import {
  IsOptional,
  IsString,
  IsDateString,
  IsE<PERSON>,
  IsNotEmpty,
} from 'class-validator';

export enum StatusEmbosing {
  OPEN = 'OPEN',
  CLOSED = 'CLOSED',
  REVIEWING = 'REVIEWING',
}

export class ReadEmbossingParamsDto {
  @IsOptional()
  @IsDateString()
  @IsNotEmpty()
  creation_date?: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  embosser_id?: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  embossing_setup_id?: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  limit?: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  page?: string;

  @IsOptional()
  @IsEnum(StatusEmbosing)
  @IsNotEmpty()
  status?: StatusEmbosing;
}
