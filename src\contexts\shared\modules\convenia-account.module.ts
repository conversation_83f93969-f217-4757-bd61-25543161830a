import { Module, forwardRef } from '@nestjs/common';

/* ** Modules ** */
import { UsersModule } from 'src/contexts/users/module/users.module';

/* ** Utils ** */
import { ConveniaAccount } from '../utils/convenia-account/convenia-account.utils';

@Module({
  imports: [forwardRef(() => UsersModule)],
  providers: [ConveniaAccount],
  exports: [ConveniaAccount],
})
export class ConveniaAccountModule {}
