import { Injectable, NotFoundException } from "@nestjs/common";
import { UsersRepositoryImpl } from "src/contexts/users/infrastructure/repositories/users.repository.impl";
import { DeleteUserUseCase } from "../../usecases-user/delete-user/delete-user.usecase";


@Injectable()
export class DeleteUserAccountUseCase {
  constructor(
    private readonly userRepository: UsersRepositoryImpl,
    private readonly deleteUserUseCase: DeleteUserUseCase
  ) {}

  async execute(id: string): Promise<void> {
    const user = await this.userRepository.findUserDetail(id);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    await this.userRepository.enabledSPEI(id);

    if(user.personIDDock && user.personIDDock.accounts) {
        await this.deleteUserUseCase.trasnferAmountToConvenia(user);
        await this.deleteUserUseCase.deleteDockCards(user.personIDDock.accounts);
    }

    await this.userRepository.softDelete(id);


  }

}