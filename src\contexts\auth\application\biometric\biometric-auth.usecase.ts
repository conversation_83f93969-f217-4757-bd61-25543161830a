import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Users } from 'src/contexts/users/domain/entities/users.entity';
import { BiometricAuthDto } from './biometric-auth.dto';
import { randomUUID } from 'crypto';

@Injectable()
export class BiometricAuthUseCase {
  constructor(
    @InjectRepository(Users)
    private readonly userRepo: Repository<Users>,
  ) {}

  async execute(data: BiometricAuthDto) {
    const user = await this.userRepo.findOne({ where: { id: data.userId } });

    if (!user) {
      throw new NotFoundException('Usuario no encontrado');
    }

    if (data.enableBiometric) {
      user.biometricToken = randomUUID(); // Generar nuevo token
    } else {
      user.biometricToken = null; // Eliminar token
    }

    await this.userRepo.save(user);

    return { biometricToken: user.biometricToken };
  }

  async findByBiometricToken(token: string): Promise<Users | null> {
    return this.userRepo.findOne({
      where: { biometricToken: token },
    });
  }
}
