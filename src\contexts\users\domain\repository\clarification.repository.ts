import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { CreateClarificationDto } from '../../application/usecases-clarification/create-clarification/create-clarification.dto';
import { UpdateClarificationDto } from '../../application/usecases-clarification/update-clarification/update-clarification.dto';

export interface ClarificationRepository {
  save(entity: CreateClarificationDto): Promise<ApiResponseDto>;
  findAll(): Promise<ApiResponseDto>;
  findByTrackingNumber(trackingNumber: string): Promise<ApiResponseDto>;
  update(trackingNumber: string, entity: UpdateClarificationDto): Promise<ApiResponseDto>;
  remove(trackingNumber: string): Promise<ApiResponseDto>;
}
