import { IsNotE<PERSON>y, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString } from 'class-validator';

export class ParamsCommissionsDto {
  @IsString()
  debtor_key: string;

  @IsString()
  creditor_key: string;

  @IsNumber()
  commission: number;

  @IsString()
  type: 'SPEI_IN' | 'SPEI_OUT' | 'P2P_IN' | 'P2P_OUT';

  @IsString()
  @IsOptional()
  operation_instance_id?: string;

  @IsString()
  @IsNotEmpty()
  external_transaction_id: string;
}

export class ResponseCommissionsDto {
  @IsNumber()
  status_code: number;

  @IsString()
  operation_id: string;
}
