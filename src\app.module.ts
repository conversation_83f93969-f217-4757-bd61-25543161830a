import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { CreateEntityUseCase } from './contexts/entity/application/create-entity/create-entity.usecase';
import { ReadEntityUseCase } from './contexts/entity/application/read-entities/read-entity.usecase';
import { UpdateEntityUseCase } from './contexts/entity/application/update-entity/update-entity.usecase';
import { Entity } from './contexts/entity/domain/entities/entity.entity';
import { DatabaseProviders } from './contexts/shared/infrastructure/database/database.providers';
import { EntityRepositoryImpl } from './contexts/entity/infrastructure/repositories/entity.repository.impl';
import { EntityController } from './contexts/entity/infrastructure/http/entity.controller';
import { ReadEntitiesUseCase } from './contexts/entity/application/read-entities/read-entities.usecase';
import { DeleteEntityUseCase } from './contexts/entity/application/delete-entity/delete-entity.usecase';

/* ** Modules ** */
import { CustomMailerModule } from './contexts/shared/modules/mailer.module';
import { CustomRedisStoreModule } from './contexts/shared/modules/redis.module';

/* ** OTP MODULE ** */
import { OtpModule } from './contexts/otp/module/otp.module';
/* ** Embossing ** */
import { CardEmbossingModule } from './contexts/card-embossing/module/card-embossing.module';
/* ** Card Dock ** */
import { CardDockModule } from './contexts/dock-cards/module/dock-cards.module';
/* ** Person Account ** */
import { PersonAccountModule } from './contexts/person-account/module/person-account.module';
/* ** Notifications ** */
import { NotificationModule } from './contexts/notifications/module/notification.module';
/* ** Carsd Assignment ** */
import { CardAssignmentModule } from './contexts/card-assignment/module/card_assignment.module';

/* ** Transfer Orders ** */
import { TransferOrdersModule } from './contexts/transfer-orders/module/transfer-orders.module';

/* ** OTP Reset Password ** */
import { OtpResetPasswordModule } from './contexts/reset-password-otp/module/otp-reset-password.module';

/* ** Convenia Account ** */
import { ConveniaAccountModule } from './contexts/shared/modules/convenia-account.module';

/* ** Commission Module ** */
import { CommissionModule } from './contexts/commission/module/commission.module';

/* ** Utils ** */
import { authTokenDock } from './contexts/shared/utils/authDock/authDock';
import { EncryptData } from './contexts/shared/utils/sensitive-data/encriptData.util';
import { DecryptData } from './contexts/shared/utils/sensitive-data/decryptData.util';
import { StorageS3Utils } from './contexts/shared/utils/storageUtils/storageS3.utils';
import { CommissionsUtils } from './contexts/shared/utils/commissions/commissions.utils';

/* ** Modules ** */
import { UsersModule } from './contexts/users/module/users.module';
import { AuthModule } from './contexts/auth/module/auth.module';
import { AccountModule } from './contexts/account/module/account.module';
import { TransactionsModule } from './contexts/transfers/module/transactions.module';
import { DockModule } from './contexts/dock/module/dock.module';
/** User notifications */
import { UserNotificationsModule } from './contexts/user-notifications/module/user-notifications.module';
/** Biometric preferences */
import { BiometricPreferencesModule } from './contexts/biometrics/module/biometric-preferences.module';
import { CatalogsModule } from './contexts/catalogs/module/catalogs.module';
import { UploadModule } from './contexts/upload/module/upload.module';
import { TransferOtpModule } from './contexts/transfer-otp/module/transfer-otp.module';
import { ClarificationModule } from './contexts/users/module/clarification.module';
import { UsersOtpModule } from './contexts/users-otp/module/users-otp.module';
import { WelcomeEmailModule } from './contexts/welcome-email-user/module/welcome-email.module';
import { LogsCardsMovementsModule } from './contexts/logs-cards-movements/module/logs_cards_movements.module';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    TypeOrmModule.forFeature([Entity]),
    ...DatabaseProviders,
    OtpModule,
    CardEmbossingModule,
    CardDockModule,
    CustomMailerModule,
    CustomRedisStoreModule,
    PersonAccountModule,
    NotificationModule,
    UsersModule,
    AuthModule,
    CardAssignmentModule,
    AccountModule,
    TransferOrdersModule,
    TransactionsModule,
    DockModule,
    UserNotificationsModule,
    BiometricPreferencesModule,
    CatalogsModule,
    OtpResetPasswordModule,
    UploadModule,
    TransferOtpModule,
    UsersOtpModule,
    ClarificationModule,
    ConveniaAccountModule,
    WelcomeEmailModule,
    CommissionModule,
    LogsCardsMovementsModule,
  ],
  controllers: [AppController, EntityController],
  providers: [
    AppService,
    EntityRepositoryImpl,
    CreateEntityUseCase,
    ReadEntityUseCase,
    UpdateEntityUseCase,
    ReadEntitiesUseCase,
    DeleteEntityUseCase,
    authTokenDock,
    EncryptData,
    DecryptData,
    StorageS3Utils,
    CommissionsUtils,
  ],
})
export class AppModule {}
