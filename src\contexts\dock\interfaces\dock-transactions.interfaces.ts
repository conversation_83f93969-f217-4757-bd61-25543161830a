export interface DockTransactions {
  previous_page: number;
  current_page: number;
  next_page: number;
  last: boolean;
  total_pages: number;
  total_items: number;
  max_items_per_page: number;
  total_items_page: number;
  items: Item[];
}

export interface Item {
  operation_instance_id: string;
  debtor_key: string;
  debtor_key_type: TorKeyType;
  creditor_key: string;
  creditor_key_type: TorKeyType;
  amount: number;
  total_amount: number;
  description: string;
  operation_type: OperationType;
  external_transaction_id: null | string;
  transaction_date: Date;
  scheduling_date: Date;
  status: string;
  reason: string;
  transfer_way: TransferWay;
}

export enum TorKeyType {
  AccountID = 'ACCOUNT_ID',
}

export enum OperationType {
  P2PP2PIn = 'P2P - P2P_IN',
  P2PP2POut = 'P2P - P2P_OUT',
}

export enum TransferWay {
  In = 'IN',
  Out = 'OUT',
}
