import { Injectable, BadRequestException } from '@nestjs/common';
import axios from 'axios';
import * as https from 'https';
import { ConfigService } from '@nestjs/config';
import { authTokenDock } from '../../../shared/utils/authDock/authDock';
import { GetUserAccountService } from '../get-user-account/get-user-account.service';

@Injectable()
export class GetAccountTransfersUseCase {
  constructor(
    private readonly authTokenDock: authTokenDock,
    private readonly configService: ConfigService,
    private readonly getUserAccountService: GetUserAccountService,
  ) {}

  async execute(email: string): Promise<any[]> {
    const { account_id } =
      await this.getUserAccountService.getAccountAndPersonByEmail(email);

    const { bearer_token, key, certificate } =
      await this.authTokenDock.getAuthDock();

    const httpsAgent = new https.Agent({
      cert: certificate,
      key: key,
      rejectUnauthorized: false,
    });

    try {
      const url = `${this.configService.get('DOCK_URL_GLOBAL')}/dockpay/v1/transfers`;
      // Solicitud para transferencias enviadas (debtor_key)
      const sentTransfersResponse = await axios.get(url, {
        params: {
          status: 'APPROVED',
          operation_type: 'P2P - P2P_IN',
          debtor_key: account_id,
        },
        headers: {
          Authorization: `Bearer ${bearer_token}`,
          'Content-Type': 'application/json',
        },
        httpsAgent,
      });

      // Solicitud para transferencias recibidas (creditor_key)
      const receivedTransfersResponse = await axios.get(url, {
        params: {
          status: 'APPROVED',
          operation_type: 'P2P - P2P_IN',
          creditor_key: account_id,
        },
        headers: {
          Authorization: `Bearer ${bearer_token}`,
          'Content-Type': 'application/json',
        },
        httpsAgent,
      });

      // Combinar ambas listas de transferencias
      const sentTransfers =
        sentTransfersResponse.data?.items.map((transfer) => ({
          date: transfer.transaction_date, // Fecha de la transacción
          amount: transfer.amount, // Monto de la transacción
          description: transfer.description, // Descripción de la transacción
          id: transfer.operation_instance_id, // ID de la operación
          type: 'debtor', // Identificar como transferencia enviada
          key: transfer.operation_instance_id,
        })) || [];

      const receivedTransfers =
        receivedTransfersResponse.data?.items.map((transfer) => ({
          date: transfer.transaction_date, // Fecha de la transacción
          amount: transfer.amount, // Monto de la transacción
          description: transfer.description, // Descripción de la transacción
          id: transfer.operation_instance_id, // ID de la operación
          type: 'creditor', // Identificar como transferencia recibida
          key: transfer.operation_instance_id,
        })) || [];

      const allTransfers = [...sentTransfers, ...receivedTransfers];

      // Ordenar por fecha
      allTransfers.sort(
        (a, b) =>  new Date(b.date).getTime() - new Date(a.date).getTime(),
      );

      return allTransfers;
    } catch (error) {
      console.error(
        'Error obteniendo las transferencias desde Dock:',
        error.response?.data || error.message,
      );
      throw new BadRequestException(
        error.response?.data ||
          'Error al obtener las transferencias desde Dock',
      );
    }
  }
}
