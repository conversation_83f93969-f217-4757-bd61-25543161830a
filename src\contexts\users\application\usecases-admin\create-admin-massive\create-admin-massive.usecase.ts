import { Injectable } from '@nestjs/common';
import { CreateAdminUseCase } from '../create-admin/create-admin.usecase';
import { Admin } from 'src/contexts/users/domain/entities/admin.entity';
import { CreateAdminDto } from '../create-admin/create-admin.dto';
import { MailerService } from '@nestjs-modules/mailer';
import { StorageS3Utils } from 'src/contexts/shared/utils/storageUtils/storageS3.utils';
import * as Handlebars from 'handlebars';

@Injectable()
export class CreateAdminMassiveUseCase {
  constructor(
    private readonly createAdminUseCase: CreateAdminUseCase,
    private readonly mailerService: MailerService,
    private readonly s3Utils: StorageS3Utils,
  ) {}

  async execute(
    dtos: CreateAdminDto[],
    creatorEmail: string,
    name: string,
  ): Promise<{
    successCount: number;
    failCount: number;
    successes: Admin[];
    errors: { index: number; error: any; dto: CreateAdminDto }[];
  }> {
    // const results = await Promise.allSettled(
    //   dtos.map((dto) => this.createAdminUseCase.execute(dto)),
    // );

    // const successes: Admin[] = [];
    // const errors: { index: number; error: any; dto: CreateAdminDto }[] = [];

    // results.forEach((result, idx) => {
    //   if (result.status === 'fulfilled') {
    //     successes.push(result.value);
    //   } else {
    //     errors.push({ index: idx, error: result.reason, dto: dtos[idx] });
    //   }
    // });

    const successes: Admin[] = [];
    const errors: { index: number; error: any; dto: CreateAdminDto }[] = [];

    for (let idx = 0; idx < dtos.length; idx++) {
      try {
        const admin = await this.createAdminUseCase.execute(dtos[idx]);
        successes.push(admin);

        // Espera 300ms entre cada creación para evitar rate limiting
        await new Promise((res) => setTimeout(res, 300));
      } catch (error) {
        errors.push({ index: idx, error, dto: dtos[idx] });
      }
    }

    const successfulCompanies = successes.map((admin) => ({
      name: admin.company_name,
      affiliationNumber: admin.membership_number,
    }));

    const failedCompanies = errors.map((err) => err.dto.company_name);

    const data = {
      userName: name,
      successfulCompanies,
      failedCompanies,
    };

    const templateSource: string = await this.s3Utils.getTemplateFromS3(
      'templates/masivos.html',
    );

    const template = Handlebars.compile(templateSource);

    const html = template(data);

    await this.mailerService.sendMail({
      to: creatorEmail,
      subject: 'Carga masiva de clientes',
      html,
    });

    return {
      successCount: successes.length,
      failCount: errors.length,
      successes,
      errors,
    };
  }
}
