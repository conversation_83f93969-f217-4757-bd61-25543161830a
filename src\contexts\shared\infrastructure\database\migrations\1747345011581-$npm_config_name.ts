import { MigrationInterface, QueryRunner } from "typeorm";

export class  $npmConfigName1747345011581 implements MigrationInterface {
    name = ' $npmConfigName1747345011581'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "transfer_contact" ADD "alias" character varying`);
        await queryRunner.query(`ALTER TABLE "transfer_contact" ADD "company_name" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "transfer_contact" DROP COLUMN "company_name"`);
        await queryRunner.query(`ALTER TABLE "transfer_contact" DROP COLUMN "alias"`);
    }

}
