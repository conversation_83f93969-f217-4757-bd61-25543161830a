// src/contexts/clarification/application/usecases/create-clarification.usecase.ts

import { Injectable } from '@nestjs/common';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { ClarificationRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/clarification.repository.impl';
import { CreateClarificationDto } from './create-clarification.dto';
import { ReadUserUseCase } from '../../usecases-user/read-user/read-user.usecase';

@Injectable()
export class CreateClarificationUseCase {
  constructor(
    private readonly clarificationRepositoryImpl: ClarificationRepositoryImpl,
    private readonly readUserUseCase: ReadUserUseCase,
  ) {}

  async execute(dto: CreateClarificationDto): Promise<ApiResponseDto> {
    const trackingNumber = await this.generateUniqueTrackingNumber();

    if (!dto.createdBy && !dto.email) {
      return {
        statusCode: 400,
        message: 'El campo createdBy o email es obligatorio',
        data: null,
      };
    }

    if (!dto.createdBy && dto.email) {
      const user = await this.readUserUseCase.executeEmail(dto.email);
      if (!user) {
        return {
          statusCode: 404,
          message: 'Usuario no encontrado',
          data: null,
        };
      }

      const payload = {
        ...dto,
        trackingNumber,
        createdBy: user.id,
      };

      return this.clarificationRepositoryImpl.save(payload);
    }

    const payload = {
      ...dto,
      trackingNumber,
    };

    return this.clarificationRepositoryImpl.save(payload);
  }

  private async generateUniqueTrackingNumber(): Promise<string> {
    let unique = false;
    let trackingNumber: string;

    while (!unique) {
      trackingNumber = this.generateRandomNumber();
      const existing =
        await this.clarificationRepositoryImpl.findByTrackingNumber(
          trackingNumber,
        );
      if (existing.statusCode === 404) {
        unique = true;
      }
    }

    return trackingNumber;
  }

  private generateRandomNumber(): string {
    const min = 100000;
    const max = 999999;
    return Math.floor(Math.random() * (max - min + 1) + min).toString();
  }
}
