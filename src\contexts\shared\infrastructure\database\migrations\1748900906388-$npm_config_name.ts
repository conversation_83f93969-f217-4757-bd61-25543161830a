import { MigrationInterface, QueryRunner } from "typeorm";

export class  $npmConfigName1748900906388 implements MigrationInterface {
    name = ' $npmConfigName1748900906388'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_transfer" ADD "cep" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_transfer" DROP COLUMN "cep"`);
    }

}
