import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import { Injectable } from '@nestjs/common';
import { Readable } from 'stream';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';

/* ** DTOs ** */
import {
  ParamsEncryptSignDto,
  ParamsVerifySignDto,
} from '../../interfaces/dtos/transfer-encrypt-sign.dto';

@Injectable()
export class TransferEncryptSign {
  /* ** S3 Client ** */
  private s3Client: S3Client;

  /* ** Constructor ** */
  constructor(private readonly configService: ConfigService) {
    /* ** Inicializamos el cliente de S3 ** */
    this.s3Client = new S3Client({
      region: this.configService.get<string>('S3_REGION'),
      credentials: {
        accessKeyId: this.configService.get<string>('S3_ACCESS_KEY'),
        secretAccessKey: this.configService.get<string>('S3_SECRET_KEY'),
      },
    });
  }

  async encryptSign(params: ParamsEncryptSignDto): Promise<string> {
    /* ** Get Private Key ** */
    const private_key = await this.getPrivateKeyS3(
      'certificates/private_key_transfer',
    );

    crypto.generateKeyPair(
      'rsa',
      {
        modulusLength: 4096,
        publicKeyEncoding: {
          type: 'spki',
          format: 'pem',
        },
        privateKeyEncoding: {
          type: 'pkcs8',
          format: 'pem',
        },
      },
      () => {},
    );

    const privateKey = crypto.createPrivateKey({
      key: private_key.toString(),
      format: 'pem',
      type: 'pkcs8',
    });

    const signer = crypto.createSign('RSA-SHA256');
    signer.update(params.sign);

    return signer.sign(privateKey).toString('base64');
  }

  async verifySign(params: ParamsVerifySignDto): Promise<boolean> {
    const public_key = await this.getPrivateKeyS3(
      'certificates/public_key_transfer',
    );

    const publicKey = crypto.createPublicKey({
      key: public_key.toString(),
      format: 'pem',
      type: 'spki',
    });

    const verifier = crypto.createVerify('RSA-SHA256');
    verifier.update(params.signature);
    const isValid = verifier.verify(
      publicKey,
      Buffer.from(params.sign, 'base64'),
    );

    return isValid;
  }

  private async getPrivateKeyS3(fileName: string): Promise<Buffer> {
    const command = new GetObjectCommand({
      Bucket: this.configService.get('S3_BUCKET'),
      Key: fileName,
    });
    const data = await this.s3Client.send(command);
    const stream = data.Body as Readable;
    const chunks: Buffer[] = [];

    for await (const chunk of stream) {
      chunks.push(chunk);
    }

    return Buffer.concat(chunks);
  }
}
