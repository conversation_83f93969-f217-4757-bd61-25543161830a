import { MigrationInterface, QueryRunner } from "typeorm";

export class  $npmConfigName1747427289715 implements MigrationInterface {
    name = ' $npmConfigName1747427289715'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" ADD "rfc" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "rfc"`);
    }

}
