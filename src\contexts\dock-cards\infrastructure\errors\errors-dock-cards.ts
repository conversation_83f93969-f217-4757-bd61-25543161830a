import { CustomError } from 'src/contexts/shared/errors/custom-error';

export class ERROR_FIND_ALIAS_CORE extends CustomError {
  constructor() {
    super('Failed to find the alias core. Please try again later.');
  }
}

export class ERROR_PHYSICAL_CVV extends CustomError {
  constructor() {
    super('Failed to find the physical card cvv. Please try again later.');
  }
}

export class ERROR_VIRTUAL_CVV extends CustomError {
  constructor() {
    super('Failed to find the virtual card cvv. Please try again later.');
  }
}

export class ERROR_CHANGE_STATUS_DOCK extends CustomError {
  constructor() {
    super(
      'Failed to change the status of the dock card. Please try again later.',
    );
  }
}

export class ERROR_CHANGE_STATUS_DB extends CustomError {
  constructor() {
    super(
      'Failed to change the status of the dock card in the database. Please try again later.',
    );
  }
}

export class ERROR_CHANGE_PIN_DOCK extends CustomError {
  constructor() {
    super('Failed to change the pin of the dock card. Please try again later.');
  }
}

export class ERROR_FIND_PIN_DOCK extends CustomError {
  constructor() {
    super('Failed to find the pin of the dock card. Please try again later.');
  }
}

export class ERROR_LIST_CARD extends CustomError {
  constructor() {
    super('Failed to list the dock cards. Please try again later.');
  }
}

export class ERROR_DELETE_CARD extends CustomError {
  constructor() {
    super('Failed to delete the dock card. Please try again later.');
  }
}
