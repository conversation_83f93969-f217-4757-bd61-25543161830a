import { Injectable, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import * as https from 'https';
import axios from 'axios';

/* ** DTOs ** */
import {
  CreateNotificationWebhookDto,
  ParamsApplyCommissionDto,
} from './notification-webhook.dto';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';

/* ** Repositories ** */
import { NotificationsRepositoryImpl } from '../../infrastructure/repository/notifications.repository.impl';
import { UserTransferRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/user-transfer.repository.impl';
import { UsersRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/users.repository.impl';
import { CommissionRepositoryImpl } from 'src/contexts/commission/infrastructure/repositories/commission.repository.impl';

/* ** usecases ** */
import { CreateOrdersTransfer } from 'src/contexts/transfer-orders/application/create-transfer-orders/create-transfer-orders.usecase';
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';

/* ** Utils ** */
import { CommissionsUtils } from 'src/contexts/shared/utils/commissions/commissions.utils';

/* ** Enums ** */
import {
  PaymentEnum,
  PaymentStatusEnum,
  PaymentEventEnum,
} from 'src/contexts/shared/enums/typePayment.enum';
import { RoleTypeEnum } from 'src/contexts/users/domain/entities/rol.entity';
import { TypeCommissionEnum } from 'src/contexts/shared/enums/commission.enum';

@Injectable()
export class NotificationWebhookUseCase {
  /* ** Constants ** */
  private defaul_commission: number = 0.01;
  constructor(
    private readonly notificationsImpl: NotificationsRepositoryImpl,
    private readonly configService: ConfigService,
    private readonly userTransfer: UserTransferRepositoryImpl,
    private readonly order: CreateOrdersTransfer,
    private readonly auth: authTokenDock,
    private readonly users: UsersRepositoryImpl,
    private readonly commissions: CommissionsUtils,
    private readonly commissionsImpl: CommissionRepositoryImpl,
  ) {}

  async NotificationWebhook(req: any, res: any) {
    try {
      const decryptedMessage = this.decryptMessage(req.body);

      const data = JSON.parse(decryptedMessage);

      const payload: CreateNotificationWebhookDto = {
        log: data ? data : 'Empty body',
        eventName: data?.event_name || 'Unknown event',
        status: 'Success',
      };

      await this.notificationsImpl.save(payload);

      this.createOrderTransfer(data);

      return res
        .status(HttpStatus.OK)
        .send('Webhook recibido y procesado correctamente');
    } catch (e) {
      const payload: CreateNotificationWebhookDto = {
        log: 'Error al procesar el webhook',
        error: e.message,
        eventName: 'webhooks/push-notifications',
        status: 'Error',
      };
      await this.createErrorNotificationWebhook(payload);
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .send('Error al procesar el webhook');
    }
  }

  private async createErrorNotificationWebhook(
    params: CreateNotificationWebhookDto,
  ): Promise<ApiResponseDto> {
    return await this.notificationsImpl.save(params);
  }

  private async encryptedAESNotification() {
    const publicKey = `....`;

    const aesKey = crypto.randomBytes(32);
    const encryptedAesKey = crypto.publicEncrypt(
      {
        key: publicKey,
        padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
        oaepHash: 'sha256',
      },
      aesKey,
    );

    return {
      aesKey: aesKey.toString('base64'),
      encryptedAesKey: encryptedAesKey.toString('base64'),
    };
  }

  private decryptMessage(encryptedMessage: string): string {
    const GCM_NONCE_LENGTH = 12;
    const GCM_TAG_LENGTH = 16;
    const aes_key = this.configService.get('WEBHOOK_KEY_AES');

    const key = Buffer.from(aes_key, 'base64');
    const messageBytes = Buffer.from(encryptedMessage, 'base64');
    const nonce = messageBytes.subarray(0, GCM_NONCE_LENGTH);
    const ciphertext = messageBytes.subarray(GCM_NONCE_LENGTH, -GCM_TAG_LENGTH);
    const tag = messageBytes.subarray(-GCM_TAG_LENGTH);

    const cipher = crypto.createDecipheriv('aes-256-gcm', key, nonce);
    cipher.setAuthTag(tag);
    const decrypted = Buffer.concat([
      cipher.update(ciphertext),
      cipher.final(),
    ]).toString('utf8');

    return decrypted.toString();
  }

  private async createOrderTransfer(data: any): Promise<boolean> {
    /* ** Event Name */
    const eventName = data?.event_name ?? 'Unknown event';
    /* ** Status */
    const event_status =
      data?.payload?.operation_step_instance_event_status ?? 'Unknown status';
    /* ** External ID */
    const external_id = data?.payload?.external_id ?? null;
    /* ** Amount */
    const amount = data?.payload?.operation_instance_amount ?? null;
    /* ** Concept ** */
    const concept = data?.payload?.description ?? null;
    /* ** debtor_account_id ** */
    const debtor_account_id = data?.payload?.debtor_account_id ?? null;
    /* ** creditor_account_id ** */
    const creditor_account_id = data?.payload?.creditor_account_id ?? null;
    /* ** Reference Dock ID ** */
    const reference_dock_id = data?.payload?.operation_instance_id ?? null;
    /* ** Commission ** */
    let commission = '0.00';
    /* ** Status Transfer ** */
    let status_transfer = 'pending';

    if (
      eventName === PaymentEventEnum.GLOBAL_DOCKPAY_P2P &&
      event_status === PaymentStatusEnum.APPROVED
    ) {
      if (external_id) {
        /* ** Get Transfer Order ** */
        const new_order = await this.userTransfer.findByIDPayment(external_id);

        if (new_order && new_order.payment_type === PaymentEnum.TRANSFER) {
          const order = await this.order.execueteSpeiOutCore({
            amount: amount.toFixed(2),
            num_clabe: new_order.beneficiary_account,
            beneficiaryBank: new_order.bank,
            beneficiaryName: new_order?.beneficiary_name || 'Beneficiario',
            description: concept || 'Transferencia',
            email: new_order.email,
            external_id,
          });

          if (!order || order.order_canceled) {
            this.canceledTransferOrder(new_order.player_account_id, amount);
            status_transfer = 'canceled';
          }
        } else if (
          new_order &&
          new_order.payment_type === PaymentEnum.DOCK &&
          creditor_account_id &&
          debtor_account_id
        ) {
          /* ** Apply Commissions OUT ** */
          const pay_commission = await this.applyCommissions({
            debtor_dock_id: debtor_account_id,
            creditor_dock_id: creditor_account_id,
            amount: amount,
            type: 'SPEI_OUT',
            event_status: event_status,
          });

          commission = pay_commission.toFixed(2);
        }

        if (new_order) {
          /* ** Save data transfer ** */
          await this.userTransfer.changeDataPaymentDock({
            id: external_id,
            commission: commission,
            reference_dock_id: reference_dock_id,
            status: event_status,
            status_transfer,
          });
        }

        const new_commission = new_order
          ? null
          : await this.commissionsImpl.findById(external_id);

        if (new_commission && new_commission?.id) {
          /* ** Update Commission Status ** */
          await this.commissionsImpl.updateCommision({
            id: new_commission?.id,
            status: event_status,
          });
        }
      }
    }

    return true;
  }

  private async canceledTransferOrder(
    creditorId: string,
    amount: number,
  ): Promise<boolean> {
    try {
      const auth = await this.auth.getAuthDock();
      const httpsAgent = new https.Agent({
        cert: auth.certificate,
        key: auth.key,
        rejectUnauthorized: false,
      });
      const transferObject = {
        debtor: {
          key: this.configService.get('DOCK_OPERATING_ACCOUNT_ID'),
          key_type: 'ACCOUNT_ID',
          balance_category: 'GENERAL',
        },
        creditor: {
          key: creditorId,
          key_type: 'ACCOUNT_ID',
          balance_category: 'GENERAL',
        },
        amount: Number(amount),
        operation_type: 'P2P - P2P_OUT',
        description: 'Transferencia revertida',
      };
      /* ** Realizamos la transferencia ** */
      const response = await axios.post(
        `${this.configService.get('DOCK_URL_GLOBAL')}/dockpay/v1/transfers`,
        transferObject,
        {
          headers: {
            Authorization: `Bearer ${auth.bearer_token}`,
            'Content-Type': 'application/json',
          },
          httpsAgent,
        },
      );

      return response.status === 200 || (response.status === 201 && true);
    } catch (error) {
      console.error('Error al cancelar la transferencia:', error.message);
      return false;
    }
  }

  private async applyCommissions(
    params: ParamsApplyCommissionDto,
  ): Promise<number> {
    const user_commission = await this.users.findCommisions(
      params.debtor_dock_id,
    );

    const creditor_commission = await this.users.findCommisions(
      params.creditor_dock_id,
    );

    let commissionAmount = 0;

    if (
      user_commission &&
      user_commission.role_type &&
      user_commission.role_type === RoleTypeEnum.CLIENT &&
      user_commission.role_type !== creditor_commission?.role_type
    ) {
      const concentrator_account = this.configService.get<string>(
        'DOCK_OPERATING_ACCOUNT_ID',
      );

      const account_commission = this.configService.get<string>(
        'DOCK_COMMISSIONS_ACCOUNT_ID',
      );

      if (
        params.type === 'SPEI_OUT' &&
        params.debtor_dock_id !== concentrator_account &&
        params.debtor_dock_id !== account_commission
      ) {
        const pct = user_commission.target_refound ?? 0;
        const operation = (Number(params.amount) * pct) / 100;
        const new_commision = operation.toFixed(2);
        const commission_aplied =
          Number(new_commision) < this.defaul_commission
            ? this.defaul_commission
            : new_commision;
        commissionAmount = Number(commission_aplied);
      }

      if (commissionAmount > 0) {
        /* ** Commission Payment ** */
        const comm = await this.commissionsImpl.save({
          status: params.event_status,
          type_commission: TypeCommissionEnum.FUNDING,
          player_account: user_commission?.account || 'N/A',
          embajador_account: user_commission?.ambassador || 'N/A',
          amount: Number(params.amount).toFixed(2),
        });

        await this.commissions.payCommissions({
          debtor_key: params.debtor_dock_id,
          creditor_key: account_commission,
          commission: commissionAmount,
          type: params.type,
          external_transaction_id: comm.id,
        });
      }
    }

    return commissionAmount;
  }
}
