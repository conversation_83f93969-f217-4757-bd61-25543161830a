import {
  Column,
  Entity as OrmNotification,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@OrmNotification()
export class NotificationWebhook {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  eventName?: string;

  @Column({ nullable: true })
  status?: string;

  @Column()
  log: string;

  @Column({ nullable: true })
  error?: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
