import { Injectable, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';

/* ** DTOs ** */
import {
  ErrorNotificationAuthorizationDto,
  CreateNotificationAuthorizationDto,
} from './notification-authorization.dto';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';

/* ** Repositories ** */
import { NotificationsRepositoryImpl } from '../../infrastructure/repository/notifications.repository.impl';

@Injectable()
export class NotificationAuthorizationUseCase {
  constructor(
    private configService: ConfigService,
    private notificationsImpl: NotificationsRepositoryImpl,
  ) {}

  async execute(req: any, res: any) {
    try {
      const decryptedMessage = this.decryptMessage(req.body);
      const data = JSON.parse(decryptedMessage);

      const payload: CreateNotificationAuthorizationDto = {
        log: data ? data : 'Empty body',
        eventName: data?.event_name || 'Unknown event',
        status: 'Success',
      };

      await this.notificationsImpl.save(payload);

      return res
        .status(HttpStatus.OK)
        .send('Webhook recibido y procesado correctamente');
    } catch (e) {
      const payload: ErrorNotificationAuthorizationDto = {
        log: 'Error al procesar el webhook',
        error: e.message,
        eventName: 'webhooks/push-notifications',
        status: 'Error',
      };
      await this.createErrorNotificationWebhook(payload);
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .send('Error al procesar el webhook');
    }
  }

  private async createErrorNotificationWebhook(
    params: ErrorNotificationAuthorizationDto,
  ): Promise<ApiResponseDto> {
    return await this.notificationsImpl.save(params);
  }

  private decryptMessage(encryptedMessage: string): string {
    const GCM_NONCE_LENGTH = 12;
    const GCM_TAG_LENGTH = 16;
    const aes_key = this.configService.get('WEBHOOK_KEY_AES');

    const key = Buffer.from(aes_key, 'base64');
    const messageBytes = Buffer.from(encryptedMessage, 'base64');
    const nonce = messageBytes.subarray(0, GCM_NONCE_LENGTH);
    const ciphertext = messageBytes.subarray(GCM_NONCE_LENGTH, -GCM_TAG_LENGTH);
    const tag = messageBytes.subarray(-GCM_TAG_LENGTH);

    const cipher = crypto.createDecipheriv('aes-256-gcm', key, nonce);
    cipher.setAuthTag(tag);
    const decrypted = Buffer.concat([
      cipher.update(ciphertext),
      cipher.final(),
    ]).toString('utf8');

    return decrypted.toString();
  }
}
