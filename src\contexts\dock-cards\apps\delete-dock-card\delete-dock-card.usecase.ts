import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as https from 'https';
import axios from 'axios';

/* ** Utils ** */
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';
import { RedisService } from 'src/contexts/shared/utils/Redis/redis';

/* ** Repositories ** */
import { AccountCardRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/account-card.repository.impl';

/* ** DTOs ** */
import {
  ResponseDeleteDockCard,
  PayloadDeleteCard,
} from './delete-dock-card.dto';

/* ** Enums ** */
import {
  ReasonStatus,
  CardStatus,
} from 'src/contexts/shared/interfaces/dtos/cards.enum.dto';

@Injectable()
export class ControlDeleteDockCardUseCase {
  constructor(
    private readonly config: ConfigService,
    private readonly auth: authTokenDock,
    private readonly accountCardImpl: AccountCardRepositoryImpl,
    private readonly log: RedisService,
  ) {}

  async deleteCard(card_id: string): Promise<ResponseDeleteDockCard> {
    const status = await this.changeDockCardStatus(card_id);

    if (status) this.changeDBCardStatus(card_id);

    return {
      statusCode: status ? 200 : 400,
      message: status ? 'Success' : 'Error deleting card',
    };
  }

  private async changeDockCardStatus(card_id: string): Promise<string> {
    const { bearer_token, certificate, key } = await this.auth.getAuthDock();

    /* ** Agent ** */
    const httpsAgent = new https.Agent({
      cert: certificate,
      key,
      rejectUnauthorized: false,
    });

    const payload: PayloadDeleteCard = {
      status: CardStatus.CANCELED,
      status_reason: ReasonStatus.OWNER_REQUEST,
    };

    const execute = async (): Promise<string> => {
      try {
        const data_card = await axios.put(
          `${this.config.get('DOCK_URL_GLOBAL')}/cards/v1/cards/${card_id}/status`,
          payload,
          {
            headers: {
              Authorization: `Bearer ${bearer_token}`,
            },
            httpsAgent,
          },
        );

        return data_card?.data?.status;
      } catch (error) {
        const code = error.response.status;
        this.log.logError({
          key: 'api:log',
          name: 'ERROR_DELETE_CARD',
          error: error?.response?.data || error?.message,
          nameController: 'ControlDeleteDockCardUseCase',
          statusCode: code,
        });

        return null;
      }
    };
    const response = await execute();

    return response;
  }

  private async changeDBCardStatus(card_dock_id: string): Promise<boolean> {
    const response = await this.accountCardImpl.updateCardStatus({
      card_dock_id,
      status: false,
    });

    return response;
  }
}
