import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { AdminDocuments } from "../../domain/entities/admin-documents.entity";
import { S3Metadata } from "../../domain/entities/s3-metadata.entity";
import { S3MetadataRepository } from "../../domain/repository/s3-metadata.repository";

export class S3MetadataRepositoryImpl implements S3MetadataRepository {

    constructor(
        @InjectRepository(S3Metadata)
        private readonly repository: Repository<S3Metadata>,
    ) { }
    
    async getById(id: number){
        return this.repository.findOneBy({ id });
    }

    async deleteById(id: number): Promise<void> {
        await this.repository.delete({ id });
    }
}