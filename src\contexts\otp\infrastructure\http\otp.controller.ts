import { Body, Controller, Post, HttpStatus } from '@nestjs/common';
import { GenerateEntityDto } from '../../app/generate-otp/generate-otp.dto';
import { GenerateOtpUseCase } from '../../app/generate-otp/generate-otp.usecase';

/* ** Verificar OTP ** */
import { VerifyEntityDto } from '../../app/verify-otp/verify-otp.dto';
import { VerifyOtpUseCase } from '../../app/verify-otp/verify-otp-usecase';
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';

@Controller('otp')
export class OtpController {
  constructor(
    private readonly generateOtpUseCase: GenerateOtpUseCase,
    private readonly verifyOtpUseCase: VerifyOtpUseCase,
  ) {}

  @Post('generate')
  async createCodeOtp(@Body() params: GenerateEntityDto) {
    try {
      return await this.generateOtpUseCase.createOtpEntry(params);
    } catch (e) {
      return ResponseUtil.error(
        e.message,
        {
          data: {
            statusCode: e.status,
            message: e.message,
            error: e.name,
          },
        },
        e.status,
      );
    }
  }

  @Post('verify')
  async controllerVerifyCodeOtp(@Body() params: VerifyEntityDto) {
    try {
      return await this.verifyOtpUseCase.executeVerifyOTP(params);
    } catch (e) {
      await this.verifyOtpUseCase.deleteOtpEntry(params);

      return ResponseUtil.error(
        e.message,
        {
          data: {
            statusCode: HttpStatus.UNAUTHORIZED,
            message: e.message,
            error: e.name,
          },
        },
        HttpStatus.UNAUTHORIZED,
      );
    }
  }
}
