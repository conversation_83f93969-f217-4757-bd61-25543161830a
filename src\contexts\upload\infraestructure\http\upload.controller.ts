import {
  Controller,
  Post,
  UseInterceptors,
  UploadedFile,
  UseGuards,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from 'src/contexts/auth/guards/auth.guard';
import { StorageS3Utils } from 'src/contexts/shared/utils/storageUtils/storageS3.utils';

@UseGuards(JwtAuthGuard)
@Controller('upload')
export class UploadController {
  constructor(private readonly s3: StorageS3Utils) {}

  @Post('')
  @UseInterceptors(FileInterceptor('file'))
  async uploadTemporary(@UploadedFile() file: Express.Multer.File) {
    const fileId = await this.s3.createTempFile(file);
    return { fileId };
  }

  // @Get(":id")
  // async getFile(@Param('id') id: string) {
  //     return await this.s3.generatePresignedUrl(id);
  // }
}
