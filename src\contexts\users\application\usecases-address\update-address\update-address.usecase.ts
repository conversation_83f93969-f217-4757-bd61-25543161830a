import { Injectable } from '@nestjs/common';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { AddressRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/addressrepository.impl';
import { UpdateAddressDto } from './update-address.dto';

@Injectable()
export class UpdateAddressUseCase {
  constructor(private readonly addressRepositoryImpl: AddressRepositoryImpl) {}

  async execute(id: string, address: UpdateAddressDto): Promise<ApiResponseDto> {
    return this.addressRepositoryImpl.update(id, address);
  }
}
