import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsArray,
  ValidateNested,
  IsBoolean,
} from 'class-validator';

import { Type } from 'class-transformer';

class BanksDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  code: string;

  @IsString()
  @IsNotEmpty()
  legalCode: string;

  @IsBoolean()
  active: boolean;
}

export class ParamsCheckOrdersDto {
  @IsString()
  @IsNotEmpty()
  custome_auth: string;

  @IsString()
  @IsNotEmpty()
  url: string;
}

export class ParamsCheckBalanceAccountDto {
  @IsString()
  @IsNotEmpty()
  account: string;
}

export class ParamsNotifyTransferDto {
  @IsString()
  @IsNotEmpty()
  productId: string;
}

export class ResponseAllBanksDto {
  @IsNumber()
  statusCode: number;

  @IsString()
  @IsNotEmpty()
  message: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BanksDto)
  banks?: BanksDto[];
}

export class ResponseServicesBanksDto {
  @IsNumber()
  code: number;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BanksDto)
  data?: BanksDto[];
}

export class ParamsFilterBanksDto {
  @IsString()
  @IsNotEmpty()
  code: string;
}

export class ResponseFilterBanksDto {
  @IsNumber()
  statusCode: number;

  @IsString()
  @IsNotEmpty()
  message: string;

  @IsString()
  bank_name: string;
}
