import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum } from 'class-validator';
import { ClarificationStatus } from '../create-clarification/create-clarification.dto';

export class UpdateClarificationDto {
  @ApiPropertyOptional({ description: 'Tipo de aclaración' })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiPropertyOptional({ description: 'Descripción detallada del caso' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'ID del administrador asignado',
    type: String,
  })
  @IsOptional()
  @IsString()
  assignedAdmin?: string;

  @ApiPropertyOptional({
    description: 'Estado actual de la aclaración',
    enum: ClarificationStatus,
    enumName: 'ClarificationStatus',
  })
  @IsOptional()
  @IsEnum(ClarificationStatus)
  status?: ClarificationStatus;
}
