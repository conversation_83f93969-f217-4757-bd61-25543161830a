import { Body, Controller, Get, Param, Put, UseGuards } from '@nestjs/common';
import { GetBiometricPreferencesUseCase } from '../../application/get-biometric-preferences/get-biometric-preferences.usecase';
import { BiometricPreference } from '../../domain/entities/biometric-preference.entity';
import { UpdateBiometricPreferencesUseCase } from '../../application/update-biometric-preferences/update-biometric-preferences.usecase';
import { JwtAuthGuard } from 'src/contexts/auth/guards/auth.guard';

@UseGuards(JwtAuthGuard)
@Controller('biometric-preferences')
export class BiometricPreferencesController {
  constructor(
    private readonly getPreferencesUseCase: GetBiometricPreferencesUseCase,
    private readonly updatePreferencesUseCase: UpdateBiometricPreferencesUseCase,
  ) {}

  /**
   * Endpoint para obtener las preferencias biométricas de un usuario.
   * @param userId ID del usuario
   * @returns Preferencias biométricas del usuario
   */
  @Get(':userId')
  async getPreferences(
    @Param('userId') userId: string,
  ): Promise<{ fingerprint_enabled: boolean; face_id_enabled: boolean }> {
    return await this.getPreferencesUseCase.execute(userId);
  }

  /**
   * Endpoint para actualizar o crear las preferencias biométricas de un usuario.
   * @param userId ID del usuario
   * @param preferences Preferencias biométricas
   * @returns Preferencias biométricas actualizadas o creadas
   */
  @Put(':userId')
  async updatePreferences(
    @Param('userId') userId: string,
    @Body() preferences: Partial<BiometricPreference>,
  ): Promise<BiometricPreference> {
    return await this.updatePreferencesUseCase.execute(userId, preferences);
  }
}
