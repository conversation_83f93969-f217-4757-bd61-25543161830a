import { MigrationInterface, QueryRunner } from "typeorm";

export class  $npmConfigName1748661906962 implements MigrationInterface {
    name = ' $npmConfigName1748661906962'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "isSpeiInEnabled" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "isSpeiOutEnabled" SET NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "isSpeiOutEnabled" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "isSpeiInEnabled" DROP NOT NULL`);
    }

}
