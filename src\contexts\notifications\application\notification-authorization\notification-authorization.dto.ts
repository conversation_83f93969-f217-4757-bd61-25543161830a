import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class ErrorNotificationAuthorizationDto {
  @IsOptional()
  @IsString()
  eventName?: string;

  @IsOptional()
  @IsString()
  status?: string;

  @IsNotEmpty()
  @IsString()
  log: string;

  @IsOptional()
  @IsString()
  error?: string;
}

export class CreateNotificationAuthorizationDto {
  @IsOptional()
  @IsString()
  eventName?: string;

  @IsOptional()
  @IsString()
  status?: string;

  @IsNotEmpty()
  @IsString()
  log: string;

  @IsOptional()
  @IsString()
  error?: string;
}
