import {
  Controller,
  Post,
  Get,
  Delete,
  Query,
  HttpCode,
  Body,
  Param,
  UseGuards,
} from '@nestjs/common';

/* ** Import useCases ** */
import { CheckOrders } from '../../application/check-orders/check-orders.usecase';
import { CreateOrdersTransfer } from '../../application/create-transfer-orders/create-transfer-orders.usecase';
import { AccountBalances } from '../../application/account-balances/account-balances.usecase';
import { ReadTransferOrdersUseCase } from '../../application/read-transder-orders/read-transfer-orders.usecase';

/* ** Utils ** */
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';
import { TransferAccount } from 'src/contexts/shared/utils/transfer-account/transfer-account.utils';

/* ** DTOs ** */
import {
  ParamsCheckOrdersDto,
  ParamsCheckBalanceAccountDto,
  ParamsNotifyTransferDto,
  ParamsFilterBanksDto,
  ResponseAllBanksDto,
  ResponseFilterBanksDto,
} from '../../application/check-orders/check-orders.dto';
import { ParamsCreateOrderDto } from '../../application/create-transfer-orders/create-transfer-orders.dto';
import { ParamsCreateClabeDto } from 'src/contexts/shared/interfaces/dtos/transfer-account.dto';

import { JwtAuthGuard } from 'src/contexts/auth/guards/auth.guard';

@UseGuards(JwtAuthGuard)
@Controller('transfer-orders')
export class TransferOrdersController {
  constructor(
    private readonly checkOrders: CheckOrders,
    private readonly createOrders: CreateOrdersTransfer,
    private readonly transferAccount: TransferAccount,
    private readonly accountBalances: AccountBalances,
    private readonly readTransferOrdersUseCase: ReadTransferOrdersUseCase,
  ) {}

  @Get('check-dinamic-transfer')
  @HttpCode(200)
  async checkDinamicTransfer(
    @Query() body: ParamsCheckOrdersDto,
  ): Promise<ResponseUtil> {
    try {
      return await this.checkOrders.checkDinamicTransfer(body);
    } catch (error) {
      ResponseUtil.error(error.name, { error }, 500);
    }
  }

  @Get('get-banks')
  @HttpCode(200)
  async getAllBanks(): Promise<ResponseAllBanksDto> {
    try {
      return await this.checkOrders.getAllBanks();
    } catch (error) {
      return {
        statusCode: error.status || 500,
        message: error.message,
      };
    }
  }

  @Get('get-filter-banks')
  @HttpCode(200)
  async getFilterBanks(
    @Query() params: ParamsFilterBanksDto,
  ): Promise<ResponseFilterBanksDto> {
    try {
      return await this.checkOrders.getFilterBanksByCode(params);
    } catch (error) {
      return {
        statusCode: error.status || 500,
        message: error.message,
        bank_name: '',
      };
    }
  }

  @Get('get-transfer-by-id/:id')
  @HttpCode(200)
  async getTransferById(@Param('id') id: string): Promise<ResponseUtil> {
    try {
      return await this.readTransferOrdersUseCase.execute(id);
    } catch (error) {
      return ResponseUtil.error(error.message, { error }, 500);
    }
  }

  @Post('create-order')
  @HttpCode(200)
  async createOrder(@Body() body: ParamsCreateOrderDto): Promise<ResponseUtil> {
    try {
      const order = await this.createOrders.createTransferOrder(body);

      return ResponseUtil.success('Success', { order }, 200);
    } catch (error) {
      const timestamp = new Date().getTime();
      return ResponseUtil.error(
        error.name,
        { error, Body: { ...body, timestamp } },
        500,
      );
    }
  }

  @Post('scheduled-transfer')
  @HttpCode(200)
  async scheduleOrder(
    @Body() body: ParamsCreateOrderDto,
  ): Promise<ResponseUtil> {
    try {
      const order = await this.createOrders.createScheduleTransfer(body);

      return ResponseUtil.success('Success', { order }, 200);
    } catch (error) {
      const timestamp = new Date().getTime();
      return ResponseUtil.error(
        error.name,
        { error, Body: { ...body, timestamp } },
        500,
      );
    }
  }

  @Post('check-balance-account')
  @HttpCode(200)
  async checkBalanceAccount(
    @Body() body: ParamsCheckBalanceAccountDto,
  ): Promise<ResponseUtil> {
    try {
      return await this.checkOrders.checkBalanceAccount(body);
    } catch (error) {
      return ResponseUtil.error(error.name, { error }, error.status || 500);
    }
  }

  @Delete('cancel-order/:id')
  @HttpCode(200)
  async cancelOrderTransfer(@Param('id') id: string): Promise<ResponseUtil> {
    try {
      return await this.checkOrders.cancelOrderTransfer(id);
    } catch (error) {
      return ResponseUtil.error(error.name, { error }, error.status || 500);
    }
  }

  @Post('notify-transfer')
  @HttpCode(200)
  async notifyTransfer(
    @Body() body: ParamsNotifyTransferDto,
  ): Promise<ResponseUtil> {
    try {
      return await this.checkOrders.notifyTransfer(body);
    } catch (error) {
      return ResponseUtil.error(error.name, { error }, error.status || 500);
    }
  }

  @Post('create-account-transfer')
  @HttpCode(200)
  async createNewAccount(
    @Body() body: ParamsCreateClabeDto,
  ): Promise<ResponseUtil> {
    try {
      const response = await this.transferAccount.createAccount(body);

      return ResponseUtil.success('Success', { response }, 200);
    } catch (error) {
      return ResponseUtil.error(error.name, { error }, error.status || 500);
    }
  }

  @Post('balance-account')
  @HttpCode(200)
  async checkAccountBalances(): Promise<ResponseUtil> {
    try {
      const response = await this.accountBalances.getAccountBalances();

      return ResponseUtil.success('Success', { response }, 200);
    } catch (error) {
      return ResponseUtil.error(error.name, { error }, error.status || 500);
    }
  }
}
