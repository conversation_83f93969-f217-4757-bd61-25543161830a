import { Module, forwardRef } from '@nestjs/common';

/* ** Controllers ** */
import { CardsDockController } from '../infrastructure/http/dock-cards.controller';

/* ** Use Cases ** */
import { CreateDockCardUseCase } from '../apps/create-dock-card/create-dock-card.usecase';
import { FindDockCardUseCase } from './../apps/find-dock-card/find-dock-card.usecase';
import { QueryDockCardCvvUseCase } from '../apps/cvv-dock-card/cvv-dock-card.usecase';
import { ControlDockCardUseCase } from '../apps/control-dock-card/control-dock-card.usecase';
import { ControlDeleteDockCardUseCase } from '../apps/delete-dock-card/delete-dock-card.usecase';

/* ** Providers ** */
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';
import { DecryptData } from 'src/contexts/shared/utils/sensitive-data/decryptData.util';
import { EncryptData } from 'src/contexts/shared/utils/sensitive-data/encriptData.util';
import { RedisService } from 'src/contexts/shared/utils/Redis/redis';
import { SecurePayloadUtil } from 'src/contexts/shared/utils/data-encrypt/data-encrypt.util';

/* ** Module ** */
import { CustomRedisStoreModule } from 'src/contexts/shared/modules/redis.module';
import { UsersModule } from 'src/contexts/users/module/users.module';

@Module({
  imports: [CustomRedisStoreModule, forwardRef(() => UsersModule)],
  controllers: [CardsDockController],
  providers: [
    CreateDockCardUseCase,
    FindDockCardUseCase,
    QueryDockCardCvvUseCase,
    ControlDockCardUseCase,
    ControlDeleteDockCardUseCase,
    authTokenDock,
    DecryptData,
    EncryptData,
    RedisService,
    SecurePayloadUtil,
  ],
  exports: [FindDockCardUseCase],
})
export class CardDockModule {}
