import { InjectQueue } from '@nestjs/bull';
import { Injectable, NotFoundException } from '@nestjs/common';
import { Queue } from 'bull';
import { StorageS3Utils } from 'src/contexts/shared/utils/storageUtils/storageS3.utils';
import { Users } from 'src/contexts/users/domain/entities/users.entity';
import { AdminDocumentsRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/admin-docuemnts.repository.impl';
import { AdminRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/adminrepository.impl';
import { RelUserRoleAdminRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/rel-user-role-admin.repository.impl';
import { S3MetadataRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/s3-metadata.repository.impl';

@Injectable()
export class DeleteAdminUseCase {
  constructor(
    private readonly adminRepositoryImpl: AdminRepositoryImpl,
    private readonly s3MetadatRepository: S3MetadataRepositoryImpl,
    private readonly adminDocumentsRepository: AdminDocumentsRepositoryImpl,
    private readonly s3 : StorageS3Utils,
    @InjectQueue('delete-user')
    private readonly deleteAdminUsersQueue: Queue,
    private readonly relUserRoleAdminRepository: RelUserRoleAdminRepositoryImpl
  ) {}

  async execute(id: string): Promise<void> {
    const admin = await this.adminRepositoryImpl.findById(id);

    if (!admin) {
        throw new NotFoundException('Admin not found');
    }

    await this.adminRepositoryImpl.softDelete(id);

    const relUserRoleAdmin = await this.relUserRoleAdminRepository.getByAdminId(id);

    for(const rel of relUserRoleAdmin) {

      try {
        const job = await this.deleteAdminUsersQueue.add(
          'process',
          {
            userId: rel.user.id,
            adminId: id
          },
          {
            priority: 1,
            delay: 1000,
            attempts: 3,
            backoff: {
              type: 'exponential',
              delay: 500,
            },
            removeOnComplete: 5,
            removeOnFail: 3,
          },
        );
  
        console.log(
          'Admin added to queue successfully:',
          rel.user.id,
          'Job ID:',
          job.id,
        );
      } catch (error) {
        console.error('Error adding job to queue:', error);
        throw error;
      }

    }
  }

  async deleteDocument(documentId: number) : Promise<void>{
    const document = await this.adminDocumentsRepository.getById(documentId);

    if(!document)
      throw new NotFoundException('Document not found');

    const {file} = document;

    await this.adminDocumentsRepository.deleteById(documentId);

    await this.s3MetadatRepository.deleteById(file.id);

    await this.s3.deleteDocument(file.key);

  }
}
