import { MigrationInterface, QueryRunner } from 'typeorm';

export class $npmConfigName1749083875264 implements MigrationInterface {
  name = ' $npmConfigName1749083875264';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_transfer" ADD "reference_dock_id" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_transfer" ADD "bank_name" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_transfer" ADD "commission" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_transfer" ADD "concept" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "address" ALTER COLUMN "num_ext" SET NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "address" ALTER COLUMN "num_ext" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_transfer" DROP COLUMN "concept"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_transfer" DROP COLUMN "commission"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_transfer" DROP COLUMN "bank_name"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_transfer" DROP COLUMN "reference_dock_id"`,
    );
  }
}
