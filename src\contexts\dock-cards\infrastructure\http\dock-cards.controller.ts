import {
  Controller,
  Post,
  Put,
  Body,
  HttpStatus,
  Get,
  Delete,
  Param,
  UseGuards,
} from '@nestjs/common';

/* ** useCases ** */
import { CreateDockCardUseCase } from '../../apps/create-dock-card/create-dock-card.usecase';
import { FindDockCardUseCase } from '../../apps/find-dock-card/find-dock-card.usecase';
import { QueryDockCardCvvUseCase } from '../../apps/cvv-dock-card/cvv-dock-card.usecase';
import { ControlDockCardUseCase } from '../../apps/control-dock-card/control-dock-card.usecase';
import { ControlDeleteDockCardUseCase } from '../../apps/delete-dock-card/delete-dock-card.usecase';

/* ** DTOs ** */
import {
  CreateDockCardPhysicalDto,
  CreateDockCardVirtualDto,
  CreateDockCardBatchDto,
} from '../../apps/create-dock-card/create-dock-card.dto';

import {
  ParamsListCards,
  ResListCards,
  ParamsFindCardDockPin,
  ResFindCardDockPin,
  ParamsFindCardDockPan,
  ResponseSearchCardPanDto,
  ParamsSensitiveData,
  ArrayDataCard,
} from '../../apps/find-dock-card/find-dock-card.dto';

import {
  ParamsCheckCvv,
  ResCheckCvv,
} from '../../apps/cvv-dock-card/cvv-dock-card.dto';

import {
  ParamsChangeCardStatus,
  ResChangeCardStatus,
  ParamsChangeCardPin,
  ResChangeCardPin,
} from '../../apps/control-dock-card/control-dock-card-dto';

import { ResponseDeleteDockCard } from '../../apps/delete-dock-card/delete-dock-card.dto';

/* ** Utils ** */
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';
import { JwtAuthGuard } from 'src/contexts/auth/guards/auth.guard';

@UseGuards(JwtAuthGuard)
@Controller('dock-cards')
export class CardsDockController {
  constructor(
    private readonly dockerUseCase: CreateDockCardUseCase,
    private readonly findCards: FindDockCardUseCase,
    private readonly queryCvv: QueryDockCardCvvUseCase,
    private readonly controlCard: ControlDockCardUseCase,
    private readonly controlDeleteCard: ControlDeleteDockCardUseCase,
  ) {}

  @Post('create-physical-card')
  async createPhysicalCard(@Body() params: CreateDockCardPhysicalDto) {
    try {
      return await this.dockerUseCase.createDockCardPhysical(params);
    } catch (e) {
      return ResponseUtil.error(
        e.message,
        {
          data: {
            statusCode: e.status,
            message: String(e.response.data.error),
            error: e.name,
          },
        },
        e.status,
      );
    }
  }

  @Post('create-virtual-card')
  async createVirtualCard(@Body() params: CreateDockCardVirtualDto) {
    try {
      return await this.dockerUseCase.createDockCardVirtual(params);
    } catch (e) {
      return ResponseUtil.error(
        e.message,
        {
          data: {
            statusCode: e.status,
            message: JSON.stringify(e.response.data.error),
            error: e.name,
          },
        },
        e.status,
      );
    }
  }

  @Post('create-batch-card')
  async createBatchCard(@Body() params: CreateDockCardBatchDto) {
    try {
      return await this.dockerUseCase.createDockCardBatch(params);
    } catch (e) {
      return ResponseUtil.error(
        e.message,
        {
          data: {
            statusCode: e.status,
            message: JSON.stringify(e.response.data.error),
            error: e.name,
          },
        },
        e.status,
      );
    }
  }

  @Post('list-cards')
  async listCards(@Body() params: ParamsListCards): Promise<ResListCards> {
    try {
      return await this.findCards.findDockCard(params);
    } catch (error) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: error.message,
        error: error.name,
      };
    }
  }

  @Get('consult-card-pin/:card_dock_id')
  async consultCardPin(
    @Param() params: ParamsFindCardDockPin,
  ): Promise<ResFindCardDockPin> {
    try {
      return await this.findCards.findDockCardPin(params);
    } catch (error) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: error.message,
        error: error.name,
      };
    }
  }

  @Post('query-cvv')
  async queryCardsCVV(@Body() params: ParamsCheckCvv): Promise<ResCheckCvv> {
    try {
      return await this.queryCvv.queryCVV(params);
    } catch (error) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: error.message,
        error: error.name,
      };
    }
  }

  @Put('control-card-status')
  async controlCardStatus(
    @Body() params: ParamsChangeCardStatus,
  ): Promise<ResChangeCardStatus> {
    try {
      return await this.controlCard.changeCardStatus(params);
    } catch (error) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: error.message,
        error: error.name,
      };
    }
  }

  @Put('change-card-pin')
  async controlCarPin(
    @Body() params: ParamsChangeCardPin,
  ): Promise<ResChangeCardPin> {
    try {
      return await this.controlCard.changeCardPin(params);
    } catch (error) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: error.message,
        error: error.name,
      };
    }
  }

  @Delete('delete-card/:id')
  async deleteCard(@Param('id') id: string): Promise<ResponseDeleteDockCard> {
    try {
      return await this.controlDeleteCard.deleteCard(id);
    } catch (error) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: error.message,
      };
    }
  }

  @Get('consult-card-pan/:pan')
  async consultCardPan(
    @Param() params: ParamsFindCardDockPan,
  ): Promise<ResponseSearchCardPanDto> {
    try {
      return await this.findCards.searchPan(params.pan);
    } catch (error) {
      return error;
    }
  }

  @Get(':card_dock_id/sensitive-data')
  async consultCardSensitiveData(
    @Param() params: ParamsSensitiveData,
  ): Promise<ArrayDataCard> {
    try {
      return await this.findCards.sensitiveData(params);
    } catch (error) {
      return error;
    }
  }
}
