import {
  Entity as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateC<PERSON>umn,
  UpdateDateColumn,
} from 'typeorm';

import { PaymentEnum } from 'src/contexts/shared/enums/typePayment.enum';

@ORMUserTransfer()
export class UserTransfer {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  reference_dock_id?: string;

  @Column({
    type: 'enum',
    enum: PaymentEnum,
  })
  payment_type: PaymentEnum;

  @Column({ nullable: true })
  bank?: string;

  @Column({ nullable: true })
  bank_name?: string;

  @Column({ nullable: true })
  beneficiary_name?: string;

  @Column({ nullable: true })
  beneficiary_account?: string;

  @Column({ nullable: true })
  player_account?: string;

  @Column()
  reference: number;

  @Column({ nullable: true })
  status_dock: string;

  @Column({ nullable: true })
  status_transfer: string;

  @Column()
  email: string;

  @Column()
  player_account_id: string;

  @Column({ nullable: true })
  order_id?: string;

  @Column({ nullable: true, type: 'decimal' })
  amount?: number;

  @Column({ nullable: true })
  cep?: string;

  @Column({ nullable: true })
  commission?: string;

  @Column({ nullable: true })
  concept?: string;

  @Column({ nullable: true })
  sign_transfer?: string;

  @Column({ nullable: true })
  error_transfer?: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
