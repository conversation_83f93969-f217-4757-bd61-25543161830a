import { Injectable } from '@nestjs/common';
import { BiometricPreferencesRepository } from '../../infrastructure/repository/biometric-preferences.repository';

@Injectable()
export class GetBiometricPreferencesUseCase {
  constructor(
    private readonly biometricPreferencesRepository: BiometricPreferencesRepository,
  ) {}

  async execute(
    userId: string,
  ): Promise<{ fingerprint_enabled: boolean; face_id_enabled: boolean }> {
    const preferences =
      await this.biometricPreferencesRepository.findByUserId(userId);

    if (!preferences) {
      // Si no hay preferencias registradas, devuelve valores predeterminados
      return {
        fingerprint_enabled: false,
        face_id_enabled: false,
      };
    }

    // Devuelve las preferencias encontradas
    return {
      fingerprint_enabled: preferences.fingerprint_enabled,
      face_id_enabled: preferences.face_id_enabled,
    };
  }
}
