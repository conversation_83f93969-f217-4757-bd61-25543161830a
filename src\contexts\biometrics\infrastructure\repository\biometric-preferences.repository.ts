import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BiometricPreference } from '../../domain/entities/biometric-preference.entity';

@Injectable()
export class BiometricPreferencesRepository {
  constructor(
    @InjectRepository(BiometricPreference)
    private readonly repository: Repository<BiometricPreference>,
  ) {}

  /**
   * Busca las preferencias biométricas de un usuario por su ID.
   * @param userId ID del usuario
   * @returns Preferencias biométricas o null si no existen
   */
  async findByUserId(userId: string): Promise<BiometricPreference | null> {
    return await this.repository.findOne({
      where: { user: { id: userId } },
    });
  }

  /**
   * Crea o actualiza las preferencias biométricas de un usuario.
   * @param userId ID del usuario
   * @param preferences Preferencias biométricas
   * @returns Preferencias biométricas actualizadas o creadas
   */
  async createOrUpdate(
    userId: string,
    preferences: Partial<BiometricPreference>,
  ): Promise<BiometricPreference> {
    let existingPreference = await this.repository.findOne({
      where: { user: { id: userId } },
    });

    if (existingPreference) {
      Object.assign(existingPreference, preferences);
      return await this.repository.save(existingPreference);
    }

    const newPreference = this.repository.create({
      user: { id: userId },
      ...preferences,
    });
    return await this.repository.save(newPreference);
  }
}