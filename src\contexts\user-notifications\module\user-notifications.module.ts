import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserNotificationPreferencesController } from '../infrastructure/http/user-notifications.controller';
import { UserNotificationPreference } from '../domain/entities/notification-preference.entity';
import { NotificationPreferencesRepositoryImpl } from '../infrastructure/repository/notification-preferences.repository.impl';
import { UpdateNotificationPreferencesUseCase } from '../application/update-notification-preferences/update-notification-preferences.usecase';
import { GetNotificationPreferencesUseCase } from '../application/get-notification-preferences/get-notification-preferences.usecase';

@Module({
  imports: [TypeOrmModule.forFeature([UserNotificationPreference])],
  controllers: [UserNotificationPreferencesController],
  providers: [
    {
      provide: 'NotificationPreferencesRepository',
      useClass: NotificationPreferencesRepositoryImpl,
    },
    UpdateNotificationPreferencesUseCase,
    GetNotificationPreferencesUseCase,
  ],
})
export class UserNotificationsModule {}
