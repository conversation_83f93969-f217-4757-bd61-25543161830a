// bull-queue.module.ts
import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BulkTransfers } from 'src/contexts/users/domain/entities/bulk-transfers.entity';
import { BulkTransfersError } from 'src/contexts/users/domain/entities/bulk-transfers-errors.entity';
import { Users } from '../../users/domain/entities/users.entity';
import { Admin } from 'src/contexts/users/domain/entities/admin.entity';

@Module({
  imports: [
    BullModule.forRootAsync({
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        console.log('Configuring Bull with Redis...');
        
        const redisConfig = {
          host: configService.get<string>('REDIS_HOST', 'localhost'),
          port: configService.get<number>('REDIS_PORT', 6379),
          password: configService.get<string>('REDIS_PASSWORD', ''),
          db: configService.get<number>('REDIS_DB', 0),
        };
        
        return {
          redis: redisConfig,
          defaultJobOptions: {
            removeOnComplete: 5,
            removeOnFail: 3,
          },
        };
      },
    }),
    BullModule.registerQueue({
      name: 'bulk-transfers',
    }),
    BullModule.registerQueue({
      name: 'delete-user'
    }),
    TypeOrmModule.forFeature([BulkTransfers, BulkTransfersError, Users, Admin])
  ],
  exports: [BullModule],
})
export class BullQueueModule {}