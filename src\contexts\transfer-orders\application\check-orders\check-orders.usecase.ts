import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

/* ** DTOs ** */
import {
  ParamsCheckOrdersDto,
  ParamsCheckBalanceAccountDto,
  ParamsNotifyTransferDto,
  ParamsFilterBanksDto,
  ResponseAllBanksDto,
  ResponseServicesBanksDto,
  ResponseFilterBanksDto,
} from './check-orders.dto';

/* ** Errors ** */
import { ERROR_CHECK_ORDERS } from '../../infrastructure/error/transfer-orders.error';

/* ** Utils ** */
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';

@Injectable()
export class CheckOrders {
  constructor(private readonly config: ConfigService) {}

  async checkDinamicTransfer(
    ckeck: ParamsCheckOrdersDto,
  ): Promise<ResponseUtil> {
    const data = await this.executeAxiosRequest(ckeck);

    return ResponseUtil.success('Successfully checked', { data }, 200);
  }

  async getAllBanks(): Promise<ResponseAllBanksDto> {
    const executeServicesBanks =
      async (): Promise<ResponseServicesBanksDto> => {
        try {
          const url: string = `${this.config.get<string>('TRANSFER_URL')}/api/1.0/banks/`;
          const response = await axios.get(url, {
            headers: {
              'X-Custom-Auth': this.config.get<string>('TRANSFER_TOKEN'),
              Accept: 'application/json',
            },
          });
          return response.data;
        } catch (error) {
          throw new ERROR_CHECK_ORDERS(error.message);
        }
      };

    const { code, data } = await executeServicesBanks();

    return {
      statusCode: code,
      message:
        code === 200
          ? 'Successfully checked all banks'
          : 'Error checking all banks',
      banks: data ? data : [],
    };
  }

  async getFilterBanksByCode(
    params: ParamsFilterBanksDto,
  ): Promise<ResponseFilterBanksDto> {
    const executeServicesBanks =
      async (): Promise<ResponseServicesBanksDto> => {
        try {
          const url: string = `${this.config.get<string>('TRANSFER_URL')}/api/1.0/banks/`;
          const response = await axios.get(url, {
            headers: {
              'X-Custom-Auth': this.config.get<string>('TRANSFER_TOKEN'),
              Accept: 'application/json',
            },
          });
          return response.data;
        } catch (error) {
          throw new ERROR_CHECK_ORDERS(error.message);
        }
      };

    const { code, data } = await executeServicesBanks();

    if (code !== 200) {
      return {
        statusCode: code,
        message: 'Failed to fetch banks',
        bank_name: '',
      };
    }

    // Filtrar el banco por código
    const filterBank = data.find((bank) => bank.code === params.code);
    if (!filterBank) {
      return {
        statusCode: 404,
        message: 'Bank not found',
        bank_name: '',
      };
    }

    return {
      statusCode: 200,
      message: 'Successfully checked all banks',
      bank_name: filterBank.name,
    };
  }

  async checkBalanceAccount(
    params: ParamsCheckBalanceAccountDto,
  ): Promise<ResponseUtil> {
    const url = `${this.config.get<string>('TRANSFER_URL')}/api/1.0/balances/`;
    const custome_auth = this.config.get<string>('TRANSFER_TOKEN');

    const data = await axios.post(url, params, {
      headers: {
        'X-Custom-Auth': custome_auth,
        Accept: 'application/json',
      },
    });

    return ResponseUtil.success(
      'Successfully checked balance account',
      { data: data.data },
      200,
    );
  }

  async cancelOrderTransfer(id: string): Promise<ResponseUtil> {
    const url = `${this.config.get<string>('TRANSFER_URL')}/api/1.0/orders/cancel/${id}`;
    const custome_auth = this.config.get<string>('TRANSFER_TOKEN');

    const data = await axios.delete(url, {
      headers: {
        'X-Custom-Auth': custome_auth,
        Accept: 'application/json',
      },
    });

    return ResponseUtil.success(
      'Successfully checked balance account',
      { data: data.data },
      200,
    );
  }

  async notifyTransfer(params: ParamsNotifyTransferDto): Promise<ResponseUtil> {
    const url = `${this.config.get<string>('TRANSFER_URL')}/api/1.0/orders/webhookNotify/${params.productId}`;
    const custome_auth = this.config.get<string>('TRANSFER_TOKEN');

    const data = await axios.post(
      url,
      {},
      {
        headers: {
          'X-Custom-Auth': custome_auth,
          Accept: 'application/json',
        },
      },
    );

    return ResponseUtil.success(
      'Successfully checked balance account',
      { data: data.data },
      200,
    );
  }

  private async executeAxiosRequest(
    execute: ParamsCheckOrdersDto,
  ): Promise<any> {
    try {
      const response = await axios.get(execute.url, {
        headers: {
          'X-Custom-Auth': execute.custome_auth,
          Accept: 'application/json',
        },
      });
      return response.data;
    } catch (error) {
      throw new ERROR_CHECK_ORDERS(error.message);
    }
  }
}
