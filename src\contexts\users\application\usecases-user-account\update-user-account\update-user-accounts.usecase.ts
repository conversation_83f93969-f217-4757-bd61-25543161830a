import { ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { UsersRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/users.repository.impl';
import { UpdateSpeiDto, UpdateUserAccountDto } from './update-user-accounts.dto';
import { Users } from 'src/contexts/users/domain/entities/users.entity';
import { AdminRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/adminrepository.impl';
import { UserAccountDeatilVO } from 'src/contexts/users/infrastructure/vo/user.vo';
import { UserParser } from 'src/contexts/users/infrastructure/parsers/user.parser';
import { ConfigService } from '@nestjs/config';
import { MailerService } from '@nestjs-modules/mailer';
import { StorageS3Utils } from 'src/contexts/shared/utils/storageUtils/storageS3.utils';

import * as Handlebars from 'handlebars';

@Injectable()
export class UpdateUserAccountsUseCase {
  constructor(
    private readonly userRepository: UsersRepositoryImpl,
    private readonly adminRepository: AdminRepositoryImpl,
    private readonly config: ConfigService,
    private readonly mailer: MailerService,
    private readonly s3Utils: StorageS3Utils,
  ) {}

  async execute(id: string, dto: UpdateUserAccountDto) : Promise<UserAccountDeatilVO> {
    const res = await this.userRepository.findById(id);

    if (res.error) {
      throw new NotFoundException('Usuario no encontrado.');
    }

    const user = res.data as Users;

    if(user.email !== dto.email){
      const existsEmail = await this.userRepository.findByEmail(dto.email);
      
      if (existsEmail) 
        throw new ConflictException('El correo electrónico ya existe.');
      
    }

    const admin = await this.adminRepository.findById(dto.adminId);

    if(!admin)
      throw new NotFoundException("admin no encontrado");

    user.admin_data = admin;
    user.email = dto.email;
    user.name = dto.name;
    user.phone = dto.phone;

    await this.userRepository.saveUser(user);

    return UserParser.parseToUserAccountDeatail(user);
  }

  async executeSPEI(id: string, dto: UpdateSpeiDto): Promise<void> {
    const res = await this.userRepository.findById(id);

    if (res.error) {
      throw new NotFoundException('Usuario no encontrado.');
    }

    const user = res.data as Users;

    user.isSpeiInEnabled = dto.speiIn;
    user.isSpeiOutEnabled = dto.speiOut;

    await this.userRepository.saveUser(user);
  }

  async updateStatus(id: string): Promise<void> {
    const user = await this.userRepository.findUserById(id);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    user.enabled = !user.enabled;
    await this.userRepository.saveUser(user);

    const string_template: string = await this.s3Utils.getTemplateFromS3('templates/cuenta-activa.html');
    
    const template = Handlebars.compile(string_template);

    await this.mailer.sendMail({
      to: user.email,
      from: `<${this.config.get('EMAIL_FROM_NOTIFICATIONS')}>`,
      subject: 'Bienvenido a Convenia',
      html: template({
        name_user: user.name
      }),
    });

  }
}
