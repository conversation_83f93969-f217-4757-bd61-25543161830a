import {
  ConflictException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  ReadUserAccountsFilterDto,
  ParamsReadUserTransactionsDto,
} from './read-user-accounts.dto';
import { UsersRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/users.repository.impl';
import { DockLegalPersonService } from 'src/contexts/dock/infraestructure/services/dock-legal-person.service';
import { UserParser } from 'src/contexts/users/infrastructure/parsers/user.parser';
import {
  UserAccountDeatilVO,
  UserAccountListVO,
  UserAccountVO,
} from 'src/contexts/users/infrastructure/vo/user.vo';
import { FindDockCardUseCase } from 'src/contexts/dock-cards/apps/find-dock-card/find-dock-card.usecase';
import { AccountRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/account.repository.impl';
import { CardStatus } from 'src/contexts/shared/interfaces/dtos/cards.enum.dto';
import { DockTransactionsService } from 'src/contexts/dock/infraestructure/services/dock-transactions.service';
import { UserTransferRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/user-transfer.repository.impl';
import {
  PAYMENT_TYPE,
  TYPE_METHOD,
} from 'src/contexts/shared/constants/payment.constants';

@Injectable()
export class ReadUserAccountsUseCase {
  constructor(
    private readonly userRepository: UsersRepositoryImpl,
    private readonly dockLegalPersonService: DockLegalPersonService,
    @Inject(forwardRef(() => FindDockCardUseCase))
    private readonly dockCardsUseCase: FindDockCardUseCase,
    private readonly accountRepository: AccountRepositoryImpl,
    private readonly dockTransactionsService: DockTransactionsService,
    private readonly userTransferRepositoryImpl: UserTransferRepositoryImpl,
  ) {}

  async execute(filter: ReadUserAccountsFilterDto): Promise<UserAccountListVO> {
    const [users, count] = await this.userRepository.findUserAccounts(filter);

    const userAccounts: UserAccountVO[] = [];

    for (const user of users) {
      const personDock = user.personIDDock;
      const account = personDock.accounts[0];

      let amount = 0;

      if (account) {
        const accountDetail =
          await this.dockLegalPersonService.getAccountDetail(
            account.accountExtID,
            personDock.personExtID,
          );
        amount =
          accountDetail.content[0].sub_account_instances[0]
            .balance_category_instances[0].balance_type_instances[0]
            .available_resource;
      }

      userAccounts.push(UserParser.parseToUserAccount(user, amount));
    }

    return {
      count,
      userAccounts,
    };
  }

  async executeById(userId: string): Promise<UserAccountDeatilVO> {
    const user = await this.userRepository.findUserAccountById(userId);

    if (!user) {
      throw new NotFoundException('Usuario no encontrado');
    }

    return UserParser.parseToUserAccountDeatail(user);
  }

  async getUserCards(userId: string) {
    const user = await this.userRepository.findUserAccountById(userId);

    if (!user) throw new NotFoundException('User not found');

    if (!user.personIDDock)
      throw new ConflictException('User does not have a person ID in Dock');

    const account = await this.accountRepository.findByPersonId(
      user.personIDDock.id,
    );

    if (!account) throw new ConflictException('Account not found');

    const alias_core = await this.dockCardsUseCase.getCardsAliasCore({
      account_dock_id: account.accountExtID,
    });

    if (!alias_core?.content?.length)
      throw new ConflictException('No card aliases found in Dock');

    const data = [];

    for (const card of alias_core.content) {
      const [detaills_card, card_data] = await Promise.all([
        this.dockCardsUseCase.sensitiveData({
          card_dock_id: card.metadata.card_id,
        }),
        this.dockCardsUseCase.getDataCard({
          card_dock_id: card.metadata.card_id,
        }),
      ]);

      const { status } = card_data;

      if (status === CardStatus.CANCELED) continue;

      const {
        id,
        card_type,
        format_expiration_date,
        card_last_4,
        expiration_date,
      } = detaills_card;

      const response = {
        card: {
          id,
          card_type,
          format_expiration_date: format_expiration_date,
          card_last_4: card_last_4,
          expiration_date: expiration_date,
          status: card_data.status,
        },
        user: {
          name: user.name,
          email: user.email,
          enterprise: user.admin_data ? user.admin_data.company_name : null,
          clabe: user.personIDDock.transfers[0].clabe,
        },
      };

      data.push(response);
    }

    return data;
  }

  async getUserTransactions(params: ParamsReadUserTransactionsDto) {
    const { userId, page = 1 } = params;

    const user = await this.userRepository.findUserAccountById(userId);

    if (!user) {
      throw new NotFoundException('Usuario no encontrado');
    }

    const person = user.personIDDock;

    if (!person) {
      throw new ConflictException('Usuario sin person ID en Dock');
    }

    const account = await this.accountRepository.findByPersonId(person.id);
    if (!account) {
      throw new NotFoundException('Cuenta no encontrada');
    }

    const transactions = await this.dockTransactionsService.getTransactions(
      account.accountExtID,
      page,
      'OUT',
      'DESC',
    );

    const transactionsParsed = [];

    for (const transaction of transactions.items) {
      let ownTransaction = null;

      if (transaction.external_transaction_id) {
        ownTransaction = await this.userTransferRepositoryImpl.findById(
          transaction.external_transaction_id,
        );
      } else {
        ownTransaction = await this.userRepository.findAccountCreditorKey(
          transaction.creditor_key,
        );
      }

      const parsedTransaction = {
        account: ownTransaction?.beneficiary_account ?? null,
        amount: transaction.total_amount,
        createdAt: transaction.transaction_date,
        description: transaction.description,
        id: transaction.operation_instance_id,
        externalId: ownTransaction
          ? (transaction?.external_transaction_id ?? null)
          : null,
        accountType: ownTransaction?.payment_type
          ? PAYMENT_TYPE[ownTransaction?.payment_type]
          : 'TRANSFER',
        enterprise: 'CLIENTE',
        method: TYPE_METHOD[ownTransaction?.payment_type] ?? 'SPEI envío',
        cep: ownTransaction?.cep ?? null,
      };
      transactionsParsed.push(parsedTransaction);
    }

    return {
      total: transactions.total_items,
      transactions: transactionsParsed,
    };
  }
}
