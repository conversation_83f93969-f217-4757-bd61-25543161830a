import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';
import { PersonRepository } from '../../domain/repository/person.repository';
import { Person } from '../../domain/entities/person.entity';

import { ResponseFindUserDto } from 'src/contexts/transfer-orders/application/transfer-orders-in/transfer-orders-in.dto';
export class PersonRepositoryImpl implements PersonRepository {
  constructor(
    @InjectRepository(Person)
    private readonly personRepository: Repository<Person>,
  ) {}

  async save(id: string): Promise<ApiResponseDto> {
    try {
      const addressEntity = this.personRepository.create({ personExtID: id });
      const res = await this.personRepository.save(addressEntity);
      return ResponseUtil.success('Persona creada exitosamente', res, 201);
    } catch (error) {
      return ResponseUtil.error('No se pudo crear la persona', error, 400);
    }
  }

  async findAll(): Promise<ApiResponseDto> {
    try {
      const res = await this.personRepository.find();
      return ResponseUtil.success('Personas obtenidas exitosamente', res, 200);
    } catch (error) {
      return ResponseUtil.error(
        'No se pudieron obtener las personas',
        error,
        400,
      );
    }
  }

  async findById(id: string): Promise<ApiResponseDto> {
    try {
      const res = await this.personRepository.findOne({
        where: { personExtID: id },
      });
      if (!res) {
        return ResponseUtil.error(
          'Persona no encontrada',
          'Persona con el ID proporcionado no existe',
          404,
        );
      }
      return ResponseUtil.success('Persona obtenida exitosamente', res, 200);
    } catch (error) {
      return ResponseUtil.error('No se pudo obtener la persona', error, 400);
    }
  }

  async update(id: string, enabled: boolean): Promise<ApiResponseDto> {
    try {
      const res = await this.personRepository.update(
        { personExtID: id },
        { enabled },
      );
      return ResponseUtil.success('Persona actualizada exitosamente', res, 200);
    } catch (error) {
      return ResponseUtil.error('No se pudo actualizar la persona', error, 400);
    }
  }

  async remove(id: string): Promise<ApiResponseDto> {
    try {
      const res = await this.personRepository.delete(id);
      return ResponseUtil.success('Persona eliminada exitosamente', res, 200);
    } catch (error) {
      return ResponseUtil.error('No se pudo eliminar la persona', error, 400);
    }
  }

  async findUserWithClabe(person: string): Promise<ResponseFindUserDto> {
    const user = await this.personRepository
      .createQueryBuilder('p')
      .innerJoin('users', 'u', 'u.personIDDock = p.id')
      .innerJoin('account', 'a', 'a.personIDDock = p.id')
      .innerJoin('account_transfer', 'at', 'at.person_id = p.id')
      .innerJoin('deposit_limits', 'dl', 'dl.id = at.limit_id')
      .select([
        'p.personExtID AS person_dock_id',
        'p.enabled AS person_enabled',
        'u.email AS email',
        'u.enabled AS user_enabled',
        'u.isSpeiInEnabled AS spei_in',
        'u.isSpeiOutEnabled AS spei_out',
        'u.name AS name',
        'a.accountExtID AS accoun_dock_id',
        'at.clabe AS clabe',
        'at.enabled as transfer_enabled',
        'dl.amount AS amount',
      ])
      .where('at.clabe = :person', { person })
      .getRawOne();

    return user;
  }

  async findUserWithEmail(email: string): Promise<ResponseFindUserDto> {
    const user = await this.personRepository
      .createQueryBuilder('p')
      .innerJoin('users', 'u', 'u.personIDDock = p.id')
      .innerJoin('account', 'a', 'a.personIDDock = p.id')
      .innerJoin('account_transfer', 'at', 'at.person_id = p.id')
      .innerJoin('deposit_limits', 'dl', 'dl.id = at.limit_id')
      .select([
        'p.personExtID AS person_dock_id',
        'p.enabled AS person_enabled',
        'u.email AS email',
        'u.enabled AS user_enabled',
        'u.isSpeiInEnabled AS spei_in',
        'u.isSpeiOutEnabled AS spei_out',
        'u.name AS name',
        'a.accountExtID AS accoun_dock_id',
        'at.clabe AS clabe',
        'at.enabled as transfer_enabled',
        'dl.amount AS amount',
      ])
      .where('u.email = :email', { email })
      .getRawOne();

    return user;
  }

  async findUserWithClabeOnlyCompany(
    clabe: string,
  ): Promise<ResponseFindUserDto> {
    const user = await this.personRepository
      .createQueryBuilder('p')
      .innerJoin('users', 'u', 'u.personIDDock = p.id')
      .innerJoin('admin', 'a2', 'u.id = a2.managerId')
      .innerJoin('account', 'a', 'a.personIDDock = p.id')
      .innerJoin('account_transfer', 'at', 'at.person_id = p.id')
      .innerJoin('deposit_limits', 'dl', 'dl.id = at.limit_id')
      .select([
        'p.personExtID AS person_dock_id',
        'p.enabled AS person_enabled',
        'u.email AS email',
        'u.enabled AS user_enabled',
        'u.isSpeiInEnabled AS spei_in',
        'u.isSpeiOutEnabled AS spei_out',
        'u.name AS name',
        'a.accountExtID AS accoun_dock_id',
        'at.clabe AS clabe',
        'at.enabled as transfer_enabled',
        'dl.amount AS amount',
      ])
      .where('at.clabe = :clabe', { clabe })
      .getRawOne();

    return user;
  }

  async findUserWithConveniaAccount(
    account: string,
  ): Promise<ResponseFindUserDto> {
    const user = await this.personRepository
      .createQueryBuilder('p')
      .innerJoin('users', 'u', 'u.personIDDock = p.id')
      .innerJoin('account', 'a', 'a.personIDDock = p.id')
      .innerJoin('account_transfer', 'at', 'at.person_id = p.id')
      .innerJoin('deposit_limits', 'dl', 'dl.id = at.limit_id')
      .select([
        'p.personExtID AS person_dock_id',
        'p.enabled AS person_enabled',
        'u.email AS email',
        'u.enabled AS user_enabled',
        'u.isSpeiInEnabled AS spei_in',
        'u.isSpeiOutEnabled AS spei_out',
        'u.name AS name',
        'a.accountExtID AS accoun_dock_id',
        'at.clabe AS clabe',
        'at.enabled as transfer_enabled',
        'dl.amount AS amount',
      ])
      .where('u.convenia_account = :account', { account })
      .getRawOne();

    return user;
  }
}
