import { ConflictException, Injectable } from '@nestjs/common';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { UpdateConveniUserDto, UpdateUserDto } from './update-user.dto';
import { UsersRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/users.repository.impl';
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';
import { RelUserRoleAdminRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/rel-user-role-admin.repository.impl';
import { UpdateUserPassDto } from './update-user-pass.dto';
import * as argon2 from 'argon2';
import { ConveniaUserVO } from 'src/contexts/users/infrastructure/vo/user.vo';
import { Users } from 'src/contexts/users/domain/entities/users.entity';
import { RoleRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/role.repository.impl';
import { RoleTypeEnum } from 'src/contexts/users/domain/entities/rol.entity';
import { UserParser } from 'src/contexts/users/infrastructure/parsers/user.parser';
import { AdminRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/adminrepository.impl';

@Injectable()
export class UpdateUserUseCase {
  constructor(
    private readonly usersRepositoryImpl: UsersRepositoryImpl,
    private readonly relUserRoleAdminRepositoryImpl: RelUserRoleAdminRepositoryImpl,
    private readonly roleRepository:RoleRepositoryImpl,
    private readonly adminRespository: AdminRepositoryImpl
    
  ) {}

  async execute(id: string, user: UpdateUserDto): Promise<ApiResponseDto> {
    const { rol, admin_data } = user;

    if (rol && !admin_data) {
      return ResponseUtil.error(
        'Admin data is required when role is provided',
        null,
        400,
      );
    }

    if (admin_data && !rol) {
      return ResponseUtil.error(
        'Role is required when admin data is provided',
        null,
        400,
      );
    }

    if (admin_data && rol) {
      await this.relUserRoleAdminRepositoryImpl.save({
        admin_data,
        rol,
        user_id: id,
      });
    }

    return this.usersRepositoryImpl.update(id, user);
  }

  async executeResetPass(user: UpdateUserDto): Promise<ApiResponseDto> {
    const { email } = user;

    const userExist = await this.usersRepositoryImpl.findByEmail(email);
    if (!userExist) {
      return ResponseUtil.error('User does not exist', null, 404);
    }

    return this.usersRepositoryImpl.update(userExist.id, user);
  }

  async executeChangePass({
    email,
    oldPassword,
    newPassword,
  }: UpdateUserPassDto): Promise<ApiResponseDto> {
    const user = await this.usersRepositoryImpl.findByEmail(email);
    if (!user) {
      return ResponseUtil.error('User does not exist', null, 404);
    }

    if (await argon2.verify(user.password, oldPassword)) {
      return this.usersRepositoryImpl.update(user.id, {
        password: newPassword,
      });
    } else {
      return ResponseUtil.error('Invalid password', null, 400);
    }
  }

  async excecuteUpdateConveniaUser(userId: string, dto: UpdateConveniUserDto) : Promise<ConveniaUserVO> {
    const userData = await this.usersRepositoryImpl.findById(userId);

    if(userData.error)
      throw new ConflictException(userData.message)

    const userDB = userData.data as Users;

    if(userDB.email !== dto.email){
      const existsEmail = await this.usersRepositoryImpl.findByEmail(dto.email);

      if(existsEmail) {
        throw new ConflictException('El correo ya se encuentra registrado');
      }
    }

    const role = await this.roleRepository.findById(dto.rol);

    let admin = null;

    if(dto.admin){
      admin = await this.adminRespository.findById(dto.admin);
      if (!admin) {
        throw new ConflictException('Admin not found');
      }
    }

    try {
      const updatedUser = { ...userDB };

      Object.keys(dto).forEach(key => {
        if (dto[key] !== undefined) {
          updatedUser[key] = dto[key];
        }
      });

      await this.usersRepositoryImpl.updateConveniaUser(userDB.id, dto);

      if(admin)
        await this.relUserRoleAdminRepositoryImpl.removeByUserIdAndAdminId(userDB.id, admin.id);
      else
        await this.relUserRoleAdminRepositoryImpl.removeByUserId(userDB.id);

      await this.relUserRoleAdminRepositoryImpl.save({
        rol: role.id,
        user_id: userDB.id,
        admin_data: admin ? admin.id : null
      });

      return UserParser.parseToConveniaUser(updatedUser, [role], admin);
    } catch (error) {
      console.error('Error al actualizar usuario Convenia:', error);
      throw new ConflictException('Error al actualizar el usuario: ' + error.message);
    }
  }
}
