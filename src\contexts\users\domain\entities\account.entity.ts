import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Person } from './person.entity';
import { IsOptional } from 'class-validator';
import { AccountCards } from './account-cards.entity';

@Entity()
export class Account {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  accountExtID: string;

  @Column({ default: true })
  @IsOptional()
  enabled: boolean;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @ManyToOne(() => Person, person => person.accounts, { nullable: true })
  @IsOptional()
  @JoinColumn()
  personIDDock: Person;

  @OneToMany(() => AccountCards, (card) => card.account)
  cards?: AccountCards[];
}
