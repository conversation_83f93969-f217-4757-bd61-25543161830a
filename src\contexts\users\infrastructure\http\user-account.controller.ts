import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseUUI<PERSON>ip<PERSON>,
  Patch,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiResponse } from '@nestjs/swagger';
import { ReadUserAccountsFilterDto } from '../../application/usecases-user-account/read-user-accounts/read-user-accounts.dto';
import { ReadUserAccountsUseCase } from '../../application/usecases-user-account/read-user-accounts/read-user-accounts.usecase';
import { UserAccountListVO, UserAccountDeatilVO } from '../vo/user.vo';
import { UpdateUserAccountsUseCase } from '../../application/usecases-user-account/update-user-account/update-user-accounts.usecase';
import {
  UpdateSpeiDto,
  UpdateUserAccountDto,
} from '../../application/usecases-user-account/update-user-account/update-user-accounts.dto';
import { JwtAuthGuard } from 'src/contexts/auth/guards/auth.guard';
import { RoleProtected } from 'src/contexts/auth/decorators/role-protected.decorator';
import { RoleEnum } from 'src/contexts/shared/enums/roles.enum';
import { UserRoleGuard } from 'src/contexts/auth/guards/user-role.guard';
import { DeleteUserAccountUseCase } from '../../application/usecases-user-account/delete-user-account/delete-user-account.usecase';

@UseGuards(JwtAuthGuard)
@Controller('user-account')
export class UserAccountController {
  constructor(
    private readonly readUserAccountsUseCase: ReadUserAccountsUseCase,
    private readonly updateUserAccountsUseCase: UpdateUserAccountsUseCase,
    private readonly deleteUserAccountUseCase: DeleteUserAccountUseCase,
  ) {}

  @Get()
  @ApiResponse({
    status: 200,
    type: UserAccountListVO,
  })
  getAllUserAccounts(@Query() filter: ReadUserAccountsFilterDto) {
    return this.readUserAccountsUseCase.execute(filter);
  }

  @Get('/:id')
  @RoleProtected(
    RoleEnum.CONVENIA_ADMIN,
    RoleEnum.CONVENIA_ADMIN_CONSULTOR,
    RoleEnum.CLIENTE_ADMIN,
    RoleEnum.CLIENTE_TESORERO,
    RoleEnum.CLIENTE_GESTOR_TARJETAHABIENTES,
    RoleEnum.CLIENTE_LECTOR,
  )
  @UseGuards(UserRoleGuard)
  @ApiResponse({
    status: 200,
    type: UserAccountDeatilVO,
  })
  @ApiResponse({
    status: 404,
  })
  getUserAccountDetail(@Param('id') id: string) {
    return this.readUserAccountsUseCase.executeById(id);
  }

  @Patch('/:id')
  @ApiResponse({
    status: 200,
    type: UserAccountDeatilVO,
  })
  @RoleProtected(RoleEnum.CONVENIA_ADMIN)
  @UseGuards(UserRoleGuard)
  @ApiResponse({
    status: 404,
  })
  updateUserAccountDetail(
    @Param('id') id: string,
    @Body() dto: UpdateUserAccountDto,
  ) {
    return this.updateUserAccountsUseCase.execute(id, dto);
  }

  @Patch('/:id/spei')
  @RoleProtected(RoleEnum.CONVENIA_ADMIN)
  @UseGuards(UserRoleGuard)
  @ApiResponse({
    status: 201,
  })
  @ApiResponse({
    status: 404,
  })
  updateSPEI(@Param('id') id: string, @Body() dto: UpdateSpeiDto) {
    return this.updateUserAccountsUseCase.executeSPEI(id, dto);
  }

  @Patch('/:id/status')
  @RoleProtected(RoleEnum.CONVENIA_ADMIN, RoleEnum.CLIENTE_ADMIN)
  @UseGuards(UserRoleGuard)
  @ApiResponse({
    status: 201,
  })
  @ApiResponse({
    status: 404,
  })
  updateStatus(@Param('id') id: string) {
    return this.updateUserAccountsUseCase.updateStatus(id);
  }

  @ApiResponse({
    status: 200,
  })
  @ApiResponse({
    status: 409,
  })
  @Get('/:id/cards')
  listCards(@Param('id') id: string) {
    return this.readUserAccountsUseCase.getUserCards(id);
  }

  @Get('/:id/transactions')
  listTransactions(
    @Param('id') id: string,
    @Query('page') page: number = 0,
    @Query('limit') limit: number = 10,
  ) {
    return this.readUserAccountsUseCase.getUserTransactions({
      userId: id,
      page,
      limit,
    });
  }

  @Delete(':id')
  deleteUserAccount(@Param('id', ParseUUIDPipe) id: string) {
    return this.deleteUserAccountUseCase.execute(id);
  }
}
