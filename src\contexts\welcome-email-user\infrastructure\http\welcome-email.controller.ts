import { Body, Controller, Post } from '@nestjs/common';
import { ApiTags, ApiResponse, ApiOperation } from '@nestjs/swagger';
import { SendWelcomeEmailDto } from '../../app/send-welcome-email/send-welcome-email.dto';
import { SendWelcomeEmailUseCase } from '../../app/send-welcome-email/send-welcome-email.usecase';
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';

@ApiTags('Welcome Email')
@Controller('welcome-email')
export class WelcomeEmailController {
  constructor(
    private readonly sendWelcomeEmailUseCase: SendWelcomeEmailUseCase,
  ) {}

  @Post('send-welcome')
  @ApiOperation({ summary: 'Send welcome email to newly created user' })
  @ApiResponse({
    status: 200,
    description: 'Welcome email sent successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input data',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async sendWelcomeEmail(@Body() params: SendWelcomeEmailDto) {
    try {
      return await this.sendWelcomeEmailUseCase.sendWelcomeEmail(params);
    } catch (e) {
      return ResponseUtil.error(
        e.message,
        {
          data: {
            statusCode: e.status,
            message: e.message,
            error: e.name,
          },
        },
        e.status,
      );
    }
  }
}
