import { IsString, IsEnum } from 'class-validator';

export enum ExpiresIn {
  FIVE_MINUTES = 300,
  TEN_MINUTES = 600,
  FIFTY_MINUTES = 3000,
  ONE_HOUR = 3600,
  A_WEEK = 604800,
  A_MONTH = 2592000,
}

export class RedisDto {
  @IsString()
  key: string;

  @IsString()
  value: string;

  @IsEnum(ExpiresIn)
  expiresIn: ExpiresIn;
}

export class RedisErrorDto {
  nameController: string;
  key: string;
  name: string;
  error: string;
  statusCode: number;
}
