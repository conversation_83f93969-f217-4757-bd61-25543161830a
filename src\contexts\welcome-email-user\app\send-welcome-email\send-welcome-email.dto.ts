import { Is<PERSON><PERSON>, <PERSON>NotEmpty, <PERSON>E<PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class SendWelcomeEmailDto {
  @ApiProperty()
  @IsNotEmpty({ message: 'email should not be empty' })
  @IsEmail({}, { message: 'email should be a valid email' })
  email: string;

  @ApiProperty()
  @IsNotEmpty({ message: 'name_user should not be empty' })
  @IsString({ message: 'name_user should be a string' })
  name_user: string;

  @ApiProperty()
  @IsNotEmpty({ message: 'company_name should not be empty' })
  @IsString({ message: 'company_name should be a string' })
  company_name: string;

  @ApiProperty()
  @IsNotEmpty({ message: 'role_name should not be empty' })
  @IsString({ message: 'role_name should be a string' })
  role_name: string;

  @ApiProperty()
  @IsNotEmpty({ message: 'signup_date should not be empty' })
  @IsString({ message: 'signup_date should be a string' })
  signup_date: string;
}
