import {
  IsArray,
  IsString,
  IsNotEmpty,
  IsOptional,
  IsNumber,
  IsInt,
  Length,
  Matches,
  MaxLength,
  ValidateNested,
  IsDecimal,
  IsBoolean,
} from 'class-validator';

import { Type } from 'class-transformer';

class DataOrder {
  @IsString()
  id: string;

  @IsInt()
  productId: number;

  @IsString()
  concept: string;

  @IsString()
  trackingKey: string;

  @IsString()
  beneficiaryAccount: string;

  @IsString()
  beneficiaryBank: string;

  @IsString()
  beneficiaryName: string;

  @IsBoolean()
  cancel: boolean;

  @IsBoolean()
  sent: boolean;
}

class DataBank {
  @IsInt()
  code: number;

  @IsString()
  legalKey: string;

  @IsString()
  name: string;

  @IsString()
  active: string;
}

export class ParamsCreateOrderDto {
  @IsString()
  @IsNotEmpty()
  @Length(18, 18)
  payerAccount: string; // Cuenta CLABE del ordenante (18 caracteres)

  @IsString()
  @IsNotEmpty()
  concept: string; // Concepto de pago al beneficiario

  @IsString()
  @IsNotEmpty()
  @Length(11, 18)
  beneficiaryAccount: string; // Cuenta CLABE del beneficiario (18 caracteres)

  @IsString()
  @IsNotEmpty()
  beneficiaryName: string; // Nombre del beneficiario

  @IsString()
  @IsNotEmpty()
  payerName: string; // Nombre del ordenante

  @IsDecimal({ decimal_digits: '2', force_decimal: true })
  amount: string; // Monto de la transferencia

  @IsString()
  beneficiaryBank: string; // Banco del beneficiario

  @IsString()
  @IsNotEmpty()
  payerBank: string; // Banco del ordenante

  @IsNumber()
  beneficiaryAccountType: number; // Tipo de cuenta del beneficiario

  @IsNumber()
  payerAccountType: number; // Tipo de cuenta del ordenante

  @IsNumber()
  paymentType: number; // Tipo de pago asociado a la orden

  @IsOptional()
  @IsString()
  beneficiaryUid?: string; // CURP o RFC del beneficiario (cadena vacía si no aplica)

  @IsOptional()
  @IsString()
  payerUid?: string; // RFC o CURP del ordenante (cadena vacía si no aplica)

  @IsOptional()
  @IsNumber()
  schedule_order?: number; // Fecha de pago en formato YYYY-MM-DD (opcional)
}

export class PayloadCreateOrderDto {
  @IsString()
  concept: string; // Concepto de pago al beneficiario

  @IsString()
  @Length(18, 18)
  beneficiaryAccount: string; // Cuenta CLABE del beneficiario (18 caracteres)

  @IsString()
  beneficiaryBank: string; // Banco del beneficiario

  @IsString()
  beneficiaryName: string; // Nombre del beneficiario

  @IsOptional()
  @IsString()
  beneficiaryUid?: string; // CURP o RFC del beneficiario (cadena vacía si no aplica)

  @IsInt()
  beneficiaryAccountType: number; // Tipo de cuenta del beneficiario

  @IsString()
  @Length(18, 18)
  payerAccount: string; // Cuenta CLABE del ordenante (18 caracteres)

  @IsString()
  payerBank: string; // Banco del ordenante

  @IsString()
  payerName: string; // Nombre del ordenante

  @IsOptional()
  @IsString()
  payerUid?: string; // RFC o CURP del ordenante (opcional, 18 caracteres)

  @IsInt()
  payerAccountType: number; // Tipo de cuenta del ordenante

  @IsDecimal({ decimal_digits: '2', force_decimal: true })
  amount: string; // Monto de la transferencia

  @IsInt()
  @Length(7, 7)
  numericalReference: number; // Referencia numérica asociada al pago

  @IsInt()
  paymentDay: number; // Fecha de pago en formato epoch (milisegundos)

  @IsString()
  sign: string; // Firma electrónica válida

  @IsNumber()
  paymentType: number; // Tipo de pago asociado a la orden

  @IsString()
  @IsOptional()
  @MaxLength(30)
  @Matches(/^[A-Za-z0-9]*$/)
  trackingKey?: string; // Llave de rastreo (opcional, letras y números, máx. 30 caracteres)

  @IsString()
  @IsOptional()
  cepPayerName?: string; // Nombre del beneficiario en el CEP (opcional)

  @IsString()
  @IsOptional()
  @Length(0, 18)
  cepPayerUid?: string; // RFC o CURP del ordenante en el CEP (opcional, 18 caracteres)

  @IsString()
  @IsOptional()
  @Length(18, 18)
  cepPayerAccount?: string; // Cuenta CLABE del ordenante en el CEP (opcional, 18 caracteres)
}

export class ResponseCreateOrderDto {
  @IsInt()
  code: number; // Código de respuesta del servicio

  @ValidateNested({ each: true })
  @Type(() => DataOrder)
  data?: DataOrder;

  @IsOptional()
  @IsString()
  error?: string; // Mensaje de error si ocurre algún problema
}

export class ResponseBankDto {
  @IsInt()
  code: number;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DataBank)
  data: DataBank[];
}

export class ParamsBankDto {
  @IsNotEmpty()
  @IsString()
  @Length(18, 18)
  account: string; // Código del banco

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DataBank)
  data: DataBank[];
}

export class PayloadCreateSignDto {
  @IsString()
  concept: string; // Concepto de pago al beneficiario

  @IsString()
  @IsNotEmpty()
  @Length(18, 18)
  beneficiaryAccount: string; // Cuenta CLABE del beneficiario (18 caracteres)

  @IsString()
  beneficiaryBank: string; // Banco del beneficiario

  @IsString()
  beneficiaryName: string; // Nombre del beneficiario

  @IsOptional()
  @IsString()
  beneficiaryUid?: string; // CURP o RFC del beneficiario (cadena vacía si no aplica)

  @IsInt()
  @IsNotEmpty()
  beneficiaryAccountType: number; // Tipo de cuenta del beneficiario

  @IsString()
  @IsNotEmpty()
  @Length(18, 18)
  payerAccount: string; // Cuenta CLABE del ordenante (18 caracteres)

  @IsString()
  @IsNotEmpty()
  payerBank: string; // Banco del ordenante

  @IsString()
  @IsNotEmpty()
  @Matches(/^[a-zA-Z\s]*$/, {
    message:
      'payerName must only contain letters and spaces without accents or symbols',
  })
  payerName: string; // Nombre del ordenante

  @IsOptional()
  @IsString()
  payerUid?: string; // RFC o CURP del ordenante (opcional, 18 caracteres)

  @IsInt()
  @IsNotEmpty()
  payerAccountType: number; // Tipo de cuenta del ordenante

  @IsDecimal({ decimal_digits: '2', force_decimal: true })
  amount: string; // Monto de la transferencia

  @IsInt()
  @IsNotEmpty()
  @Length(7, 7)
  numericalReference: number; // Referencia numérica asociada al pago

  @IsInt()
  @IsNotEmpty()
  paymentDay: number; // Fecha de pago en formato epoch (milisegundos)

  @IsString()
  @IsNotEmpty()
  sign: string; // Firma electrónica válida

  @IsNumber()
  paymentType: number; // Tipo de pago asociado a la orden

  @IsString()
  @IsOptional()
  @MaxLength(30)
  @Matches(/^[A-Za-z0-9]*$/)
  trackingKey?: string; // Llave de rastreo (opcional, letras y números, máx. 30 caracteres)

  @IsString()
  @IsOptional()
  cepPayerName?: string; // Nombre del beneficiario en el CEP (opcional)

  @IsString()
  @IsOptional()
  @Length(0, 18)
  cepPayerUid?: string; // RFC o CURP del ordenante en el CEP (opcional, 18 caracteres)

  @IsString()
  @IsOptional()
  @Length(18, 18)
  cepPayerAccount?: string; // Cuenta CLABE del ordenante en el CEP (opcional, 18 caracteres)

  @IsOptional()
  @IsString()
  schedule_order: string; // Fecha de pago en formato YYYY-MM-DD (opcional)
}

export class SpeiOutRequestDto {
  @IsString()
  num_clabe: string;

  @IsString()
  email: string;

  @IsString()
  amount: string;

  @IsString()
  description: string;

  @IsString()
  beneficiaryBank: string;

  @IsString()
  beneficiaryName: string;

  @IsString()
  external_id: string;
}

export class ResponseSpeiOutRequestDto {
  @IsBoolean()
  order_canceled: boolean;

  @IsBoolean()
  order_sent: boolean;

  @IsBoolean()
  order_created: boolean;
}
