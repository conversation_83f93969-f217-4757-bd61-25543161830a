import { Commissions } from '../entities/commission.entity';

/* ** DTOs ** */
import {
  ParamsCommissionUpdateDto,
  ParamsSaveCommissionDto,
} from '../../shared/commission.dto';

export interface CommissionRepository {
  save(entity: ParamsSaveCommissionDto): Promise<Commissions>;
  findById(id: string): Promise<Commissions>;
  updateCommision(params: ParamsCommissionUpdateDto): Promise<boolean>;
}
