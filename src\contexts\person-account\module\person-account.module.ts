import { Module } from '@nestjs/common';

/* ** Controllers ** */
import { PersonAccountController } from '../infrastructure/http/person-account.controller';

/* ** Use Cases ** */
import { CreateNatPersAccUseCase } from '../application/create-person-account-natural/create-pers-acc-nat.usecase';
import { CreateLegPersAccUseCase } from '../application/create-person-account-legal/create-pers-acc-leg.usecase';

/* ** Providers ** */
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';

/* ** Module ** */
import { CustomRedisStoreModule } from 'src/contexts/shared/modules/redis.module';

@Module({
  imports: [CustomRedisStoreModule],
  controllers: [PersonAccountController],
  providers: [CreateNatPersAccUseCase, CreateLegPersAccUseCase, authTokenDock],
  exports: [CreateNatPersAccUseCase, CreateLegPersAccUseCase],
})
export class PersonAccountModule {}
