import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { UsersRepository } from '../../domain/repository/users.repository';
import { CreateUserDto } from '../../application/usecases-user/create-user/create-user.dto';
import {
  UpdateConveniUserDto,
  UpdateUserDto,
} from '../../application/usecases-user/update-user/update-user.dto';
import { ResPersonDto } from 'src/contexts/card-assignment/application/single-card-assignment/single-card-assignment.dto';
import { ResponseFindUserByCardIDDto } from './../../../transfer-orders/application/transfer-orders-in/transfer-orders-in.dto';
import { Users } from '../../domain/entities/users.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';
import * as argon2 from 'argon2';
import { RoleTypeEnum } from '../../domain/entities/rol.entity';
import { FilterConveniaUsersDto } from '../../application/usecases-user/read-user/read-user.dto';
import { ReadUserAccountsFilterDto } from '../../application/usecases-user-account/read-user-accounts/read-user-accounts.dto';
import { ReadUsersByAdminFilterDto } from '../../application/usecases-admin/read-admin/read-admins-filter.dto';
import { ReadAdminTotalAmountDto } from '../../application/usecases-admin/read-total-amount/read-total-amount.dto';
import { ReadTotalUsersDto } from '../../application/usecases-user/read-total-users/read-total-users.dto';
import { ResUserWithAdminSettingsDto } from '../../application/usecases-user/read-user/read-user-with-admin-settings.dto';
import { ResponseCommissionByAccountDto } from 'src/contexts/notifications/application/notification-webhook/notification-webhook.dto';
import {
  ResponseFindOnlyUserAccountDto,
  ResponseFindAccountCreditorDto,
} from '../../application/usecases-user/get-user-account/get-user-account.dto';
import { RoleEnum } from 'src/contexts/shared/enums/roles.enum';

export class UsersRepositoryImpl implements UsersRepository {
  constructor(
    @InjectRepository(Users) private readonly userRepository: Repository<Users>,
  ) {}

  async save(user: CreateUserDto): Promise<ApiResponseDto> {
    try {
      const userEntity = this.userRepository.create({
        ...user,
        personIDDock: user.personIDDock ? { id: user.personIDDock } : null,
        admin_data: user.admin_data ? { id: user.admin_data } : null,
        address: user.address ? { id: user.address } : null,
        enabled: user.enabled !== undefined ? user.enabled : false,
      });
      const hashPassword = await argon2.hash(user.password);
      const res = await this.userRepository.save({
        ...userEntity,
        password: hashPassword,
      });
      return ResponseUtil.success('Usuario creado exitosamente', res, 201);
    } catch (error) {
      return ResponseUtil.error('No se pudo crear el usuario', error, 400);
    }
  }

  async findAll(): Promise<ApiResponseDto> {
    try {
      const res = await this.userRepository.find({
        where: { isDeleted: false },
      });
      return ResponseUtil.success('Usuarios obtenidos exitosamente', res, 200);
    } catch (error) {
      return ResponseUtil.error(
        'No se pudieron obtener los usuarios',
        error,
        400,
      );
    }
  }

  async findAllUsers(): Promise<Users[]> {
    try {
      const res = await this.userRepository.find({
        where: { isDeleted: false },
      });
      return res;
    } catch (error) {
      console.error('Error al obtener todos los usuarios:', error);
      return null;
    }
  }

  async findById(id: string): Promise<ApiResponseDto> {
    try {
      const res = await this.userRepository.findOne({
        where: { id, isDeleted: false },
      });
      if (!res) {
        return ResponseUtil.error(
          'Usuario no encontrado',
          'Usuario con el ID proporcionado no existe',
          404,
        );
      }
      return ResponseUtil.success('Usuario obtenido exitosamente', res, 200);
    } catch (error) {
      return ResponseUtil.error('No se pudo obtener el usuario', error, 400);
    }
  }

  async update(id: string, user: UpdateUserDto): Promise<ApiResponseDto> {
    try {
      const updateData: any = { ...user };

      if (updateData.password) {
        const hashPassword = await argon2.hash(updateData.password);
        updateData.password = hashPassword;
      }

      // Construimos solo los campos que existan
      if (user.personIDDock !== undefined) {
        updateData.personIDDock = user.personIDDock
          ? { id: user.personIDDock }
          : null;
      } else {
        delete updateData.personIDDock;
      }

      if (user.admin_data !== undefined) {
        updateData.admin_data = user.admin_data
          ? { id: user.admin_data }
          : null;
      } else {
        delete updateData.admin_data;
      }

      if (user.address !== undefined) {
        updateData.address = user.address ? { id: user.address } : null;
      } else {
        delete updateData.address;
      }

      const res = await this.userRepository.update(id, updateData);

      if (!res.affected) {
        return ResponseUtil.error(
          'Usuario no encontrado',
          'Usuario con el ID proporcionado no existe',
          404,
        );
      }

      return ResponseUtil.success('Usuario actualizado exitosamente', res, 200);
    } catch (error) {
      return ResponseUtil.error('No se pudo actualizar el usuario', error, 400);
    }
  }

  async remove(id: string): Promise<ApiResponseDto> {
    try {
      const res = await this.userRepository.update(id, { enabled: false });
      if (!res.affected) {
        return ResponseUtil.error(
          'Usuario no encontrado',
          'Usuario con el ID proporcionado no existe',
          404,
        );
      }
      return ResponseUtil.success('Usuario eliminado exitosamente', res, 200);
    } catch (error) {
      return ResponseUtil.error('No se pudo eliminar el usuario', error, 400);
    }
  }

  async findByEmail(email: string): Promise<Users | null> {
    const user = await this.userRepository.findOneBy({
      email,
      isDeleted: false,
    });

    return user;
  }

  async findUserWithAdminSettingsByEmail(
    email: string,
  ): Promise<ResUserWithAdminSettingsDto> {
    const user = await this.userRepository.findOne({
      where: { email, isDeleted: false },
      relations: ['admin_data'],
    });

    if (!user) {
      return null;
    }

    return {
      id: user.id,
      managerId: user.admin_data?.managerId,
      isManager: user.id === user.admin_data?.managerId,
      enabled: user.enabled,
      name: user.name,
      email: user.email,
      spei_in: user.admin_data?.spei_in,
      spei_out: user.admin_data?.spei_out,
      target_refound: user.admin_data?.target_refound,
      ambassador: user.admin_data?.ambassador,
      convenia_account: user.convenia_account,
      membership_number: user?.admin_data?.membership_number,
      phone: user.phone,
    };
  }

  async getPersonIdByEmail(email: string): Promise<ResPersonDto> {
    const res = await this.userRepository
      .createQueryBuilder('users')
      .leftJoinAndSelect('users.personIDDock', 'person')
      .where('users.email = :email', { email })
      .andWhere('users.isDeleted = :isDeleted', { isDeleted: false })
      .getOne();

    return {
      id: res?.personIDDock?.id ?? '',
      person_dock_id: res?.personIDDock?.personExtID ?? '',
    };
  }

  async count({ adminId }: ReadTotalUsersDto): Promise<number> {
    const qb = this.userRepository
      .createQueryBuilder('users')
      .where('users.isDeleted = :isDeleted', { isDeleted: false });

    if (adminId) {
      qb.leftJoin('users.admin_data', 'admin');
      qb.andWhere('admin.id = :adminId', { adminId });
    }

    return await qb.getCount();
  }

  async updateConveniaUser(
    id: string,
    dto: UpdateConveniUserDto,
  ): Promise<void> {
    if (dto.password) {
      const hashPassword = await argon2.hash(dto.password);
      dto.password = hashPassword;
    } else {
      delete dto.password;
    }

    delete dto.rol;
    delete dto.admin;
    await this.userRepository.update(id, dto);
  }

  async updateConveniaAccount(id: string, account: string): Promise<void> {
    await this.userRepository.update(id, { convenia_account: account });
  }

  async findAllConveniaUsers({
    page,
    limit,
    q,
    admin,
    userId,
    roleType,
    roleName,
  }: FilterConveniaUsersDto) {
    const qb = this.userRepository
      .createQueryBuilder('users')
      .leftJoinAndSelect('users.relUserRoleAdmins', 'relUserRoleAdmins')
      .innerJoinAndSelect('relUserRoleAdmins.role', 'role')
      .leftJoinAndSelect('relUserRoleAdmins.admin', 'admin')
      .skip((page - 1) * limit)
      .take(limit);

    qb.where('users.isDeleted = :isDeleted', { isDeleted: false });

    if (userId) {
      qb.andWhere('users.id != :userId', { userId });
    }

    if (q) {
      qb.andWhere('(users.name LIKE :q OR users.email LIKE :q)', {
        q: `%${q}%`,
      });
    }

    if (admin) {
      qb.andWhere('admin.id = :admin', { admin });
    }

    if (roleType === RoleTypeEnum.CLIENT) {
      qb.andWhere('role.type = :type', { type: RoleTypeEnum.CLIENT });
    } else {
      qb.andWhere('role.type = :type', { type: RoleTypeEnum.CONVENIA });
    }

    if (roleName === RoleEnum.CLIENTE_ADMIN) {
      qb.andWhere('role.name != :excludedRole', {
        excludedRole: RoleEnum.CLIENTE_ADMIN,
      });
    }

    return await qb.getManyAndCount();
  }

  async findConveniaUserById(id: string): Promise<Users | null> {
    return this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.relUserRoleAdmins', 'relUserRoleAdmins')
      .leftJoinAndSelect('relUserRoleAdmins.role', 'role')
      .where('user.id = :id', { id })
      .andWhere('user.isDeleted = :isDeleted', { isDeleted: false })
      .getOne();
  }

  async findUserAccounts({
    q,
    page,
    limit,
    initDate,
    finalDate,
    adminId,
  }: ReadUserAccountsFilterDto) {
    const qb = this.userRepository
      .createQueryBuilder('user')
      .innerJoinAndSelect('user.admin_data', 'admin_data')
      .innerJoinAndSelect('user.personIDDock', 'personIDDock')
      .leftJoinAndSelect('personIDDock.accounts', 'accounts')
      .innerJoinAndSelect('personIDDock.transfers', 'transfers')
      .leftJoinAndSelect('user.relUserRoleAdmins', 'relUserRoleAdmins')
      .leftJoinAndSelect('relUserRoleAdmins.role', 'role')
      .where('user.isDeleted = :isDeleted', { isDeleted: false })
      .skip((page - 1) * limit)
      .take(limit);

    if (adminId) {
      qb.andWhere('admin_data.id = :adminId', { adminId });
    }

    if (q) {
      qb.andWhere(
        '(user.name LIKE :q OR user.email LIKE :q OR admin_data.alias LIKE :q)',
        { q: `%${q}%` },
      );
    }

    if (initDate) {
      qb.andWhere('user.created_at >= :initDate', { initDate });
    }

    if (finalDate) {
      qb.andWhere('user.created_at <= :finalDate', { finalDate });
    }

    return await qb.getManyAndCount();
  }

  async findUserAccountById(id: string): Promise<Users | null> {
    return this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.admin_data', 'admin_data')
      .leftJoinAndSelect('user.personIDDock', 'personIDDock')
      .leftJoinAndSelect('personIDDock.transfers', 'transfers')
      .leftJoinAndSelect('user.address', 'address')
      .where('user.id = :id', { id })
      .andWhere('user.isDeleted = :isDeleted', { isDeleted: false })
      .getOne();
  }

  async saveUser(user: Users): Promise<Users> {
    return this.userRepository.save(user);
  }

  async findUserById(id: string): Promise<Users> {
    return this.userRepository.findOne({ where: { id, isDeleted: false } });
  }

  async getUsersByAdmin(
    id: string,
    { page, limit, q }: ReadUsersByAdminFilterDto,
  ) {
    const subQuery = this.userRepository
      .createQueryBuilder('users')
      .select('users.id')
      .innerJoin('users.relUserRoleAdmins', 'relUserRoleAdmins')
      .innerJoin('relUserRoleAdmins.admin', 'admin')
      .where('admin.id = :id', { id })
      .andWhere('users.isDeleted = :isDeleted', { isDeleted: false });

    if (q) {
      subQuery.andWhere('(users.name LIKE :q OR users.email LIKE :q)', {
        q: `%${q}%`,
      });
    }

    subQuery.skip((page - 1) * limit).take(limit);

    const qb = this.userRepository
      .createQueryBuilder('users')
      .leftJoinAndSelect('users.admin_data', 'admin_data')
      .innerJoinAndSelect('users.relUserRoleAdmins', 'relUserRoleAdmins')
      .innerJoinAndSelect('relUserRoleAdmins.role', 'role')
      .innerJoin('relUserRoleAdmins.admin', 'admin')
      .where(`users.id IN (${subQuery.getQuery()})`)
      .setParameters(subQuery.getParameters());

    const [users, count] = [await qb.getMany(), await subQuery.getCount()];

    return { users, count };
  }

  async findAllIds(dto: ReadAdminTotalAmountDto) {
    const qb = this.userRepository
      .createQueryBuilder('user')
      .innerJoin('user.personIDDock', 'personIDDock')
      .innerJoin('personIDDock.accounts', 'accounts')
      .where('user.isDeleted = :isDeleted', { isDeleted: false })
      .select(['user.id', 'personIDDock.personExtID', 'accounts.accountExtID']);

    if (dto.groupId) {
      qb.leftJoin('user.admin_data', 'admin').andWhere(
        'admin.group_id = :groupId',
        { groupId: dto.groupId },
      );
    }

    if (dto.adminId) {
      qb.leftJoin('user.admin_data', 'admin').andWhere('admin.id = :adminId', {
        adminId: dto.adminId,
      });
    }

    return await qb.getMany();
  }

  async findUserWithCardID(
    card_dock_id: string,
  ): Promise<ResponseFindUserByCardIDDto> {
    const user = await this.userRepository
      .createQueryBuilder('u')
      .innerJoin('person', 'p', 'u.personIDDock = p.id')
      .innerJoin('account', 'a', 'a.personIDDock = p.id')
      .innerJoin('account_cards', 'ac', 'a.id = ac.account_id')
      .select([
        'u.email AS email',
        'u.enabled AS user_enabled',
        'u.isSpeiInEnabled AS spei_in',
        'u.isSpeiOutEnabled AS spei_out',
        'p.personExtID AS person_dock_id',
        'a.accountExtID AS accoun_dock_id',
      ])
      .where('ac.card_dock_id = :card_dock_id', { card_dock_id })
      .andWhere('u.isDeleted = :isDeleted', { isDeleted: false })
      .getRawOne();

    return user;
  }

  async countByRFC(rfc: string): Promise<number> {
    return await this.userRepository
      .createQueryBuilder('users')
      .where('users.rfc LIKE :rfc', { rfc: `${rfc}%` })
      .andWhere('users.isDeleted = :isDeleted', { isDeleted: false })
      .getCount();
  }

  async findCommisions(
    accoun_dock_id: string,
  ): Promise<ResponseCommissionByAccountDto> {
    const user = await this.userRepository
      .createQueryBuilder('u')
      .innerJoin('person', 'p', 'u.personIDDock = p.id')
      .innerJoin('account', 'a', 'a.personIDDock = p.id')
      .innerJoin('admin', 'ad', 'u.id = ad.managerId')
      .innerJoin('rel_user_role_admin', 'rura', 'u.id = rura.userId')
      .innerJoin('rol', 'r', 'rura.roleId = r.id')
      .select([
        'u.convenia_account as account',
        'ad.spei_in as spei_in',
        'ad.spei_out as spei_out',
        'ad.target_refound as target_refound',
        'ad.ambassador as ambassador',
        'r.type as role_type',
      ])
      .where('a.accountExtID = :accoun_dock_id', { accoun_dock_id })
      .andWhere('u.isDeleted = :isDeleted', { isDeleted: false })
      .getRawOne();

    return user;
  }

  async findCommisionsByEmail(
    email: string,
  ): Promise<ResponseCommissionByAccountDto> {
    const user = await this.userRepository
      .createQueryBuilder('u')
      .innerJoin('person', 'p', 'u.personIDDock = p.id')
      .innerJoin('account', 'a', 'a.personIDDock = p.id')
      .innerJoin('admin', 'ad', 'u.id = ad.managerId')
      .innerJoin('rel_user_role_admin', 'rura', 'u.id = rura.userId')
      .innerJoin('rol', 'r', 'rura.roleId = r.id')
      .select([
        'ad.spei_in as spei_in',
        'ad.spei_out as spei_out',
        'ad.target_refound as target_refound',
        'r.type as role_type',
      ])
      .where('u.email = :email', { email })
      .andWhere('u.isDeleted = :isDeleted', { isDeleted: false })
      .getRawOne();

    return user;
  }

  async findCommisionsOfSpei(
    email: string,
  ): Promise<ResponseCommissionByAccountDto> {
    const user = await this.userRepository
      .createQueryBuilder('u')
      .innerJoin('person', 'p', 'u.personIDDock = p.id')
      .innerJoin('account', 'a', 'a.personIDDock = p.id')
      .innerJoin('admin', 'ad', 'u.adminDataId = ad.id')
      .select([
        'ad.spei_in as spei_in',
        'ad.spei_out as spei_out',
        'ad.target_refound as target_refound',
      ])
      .where('u.email = :email', { email })
      .andWhere('u.isDeleted = :isDeleted', { isDeleted: false })
      .getRawOne();

    return user;
  }

  async findConveniaAccount(convenia_account: string): Promise<boolean> {
    try {
      const res = await this.userRepository.findOne({
        where: { convenia_account, isDeleted: false },
      });
      return !res;
    } catch (error) {
      console.error('Error al buscar la cuenta Convenia:', error);
      return false;
    }
  }

  async softDelete(id: string): Promise<void> {
    await this.userRepository.update(id, {
      isDeleted: true,
      deletedAt: new Date(),
      isSpeiInEnabled: false,
      isSpeiOutEnabled: false,
    });
  }

  async findUserDetail(id: string): Promise<Users | null> {
    return this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.admin_data', 'admin_data')
      .leftJoinAndSelect('user.personIDDock', 'personIDDock')
      .leftJoinAndSelect('personIDDock.accounts', 'accounts')
      .leftJoinAndSelect('accounts.cards', 'cards')
      .leftJoinAndSelect('user.address', 'address')
      .leftJoinAndSelect('user.relUserRoleAdmins', 'relUserRoleAdmins')
      .leftJoinAndSelect('relUserRoleAdmins.admin', 'admin')
      .where('user.id = :id', { id })
      .getOne();
  }

  async findConveniaAdmin(): Promise<Users | null> {
    return this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.personIDDock', 'personIDDock')
      .leftJoinAndSelect('personIDDock.accounts', 'accounts')
      .leftJoinAndSelect('accounts.cards', 'cards')
      .leftJoinAndSelect('user.relUserRoleAdmins', 'relUserRoleAdmins')
      .leftJoinAndSelect('relUserRoleAdmins.role', 'role')
      .where('role.name = :roleName', { roleName: RoleEnum.CLIENTE_ADMIN })
      .getOne();
  }

  async disabledSPEI(userId: string) {
    await this.userRepository.update(userId, {
      isSpeiInEnabled: false,
      isSpeiOutEnabled: false,
    });
  }

  async enabledSPEI(userId: string) {
    await this.userRepository.update(userId, {
      isSpeiInEnabled: true,
      isSpeiOutEnabled: true,
    });
  }

  async findOnlyUserAccount(
    id: string,
  ): Promise<ResponseFindOnlyUserAccountDto> {
    const user = await this.userRepository
      .createQueryBuilder('u')
      .innerJoin('person', 'p', 'u.personIDDock = p.id')
      .innerJoin('account_transfer', 'at2', ' p.id = at2.person_id')
      .innerJoin('rel_user_role_admin', 'rura', 'u.id = rura.userId')
      .innerJoin('admin', 'a', 'rura."adminId" = a.id')
      .select([
        'at2.clabe as clabe',
        'u.convenia_account as convenia_account',
        'a.membership_number as member_ship',
      ])
      .where('u.id = :id', { id })
      .andWhere('u.isDeleted = :isDeleted', { isDeleted: false })
      .andWhere('a.managerId = :id', { id })
      .getRawOne();

    return user;
  }

  async findUserDetails(id: string): Promise<Users | null> {
    return await this.userRepository.findOne({
      where: { id, isDeleted: false },
      relations: [
        'admin_data',
        'personIDDock',
        'personIDDock.accounts',
        'personIDDock.accounts.cards',
        'address',
        'relUserRoleAdmins',
        'relUserRoleAdmins.admin',
        'relUserRoleAdmins.role',
      ],
    });
  }

  async findAccountCreditorKey(
    accoun_dock_id: string,
  ): Promise<ResponseFindAccountCreditorDto> {
    const user = await this.userRepository
      .createQueryBuilder('u')
      .innerJoin('person', 'p', 'u.personIDDock = p.id')
      .innerJoin('account', 'a', ' p.id = a.personIDDockId')
      .innerJoin('account_transfer', 'at2', ' p.id = at2.person_id')
      .select([
        'at2.clabe as beneficiary_account',
        'u.convenia_account as convenia_account',
      ])
      .where('a.accountExtID = :accoun_dock_id', { accoun_dock_id })
      .andWhere('u.isDeleted = :isDeleted', { isDeleted: false })
      .getRawOne();

    return user;
  }
}
