import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import * as https from 'https';

/* ** DTOs ** */
import { CreateCardEmbossingDto } from './create-embossing.dto';
import { ApiResponseDto } from './../../../shared/interfaces/dtos/api-response.dto';

/* ** Utils ** */
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';

@Injectable()
export class CreateCardEmbossingUseCase {
  constructor(
    private readonly authServices: authTokenDock,
    private readonly configService: ConfigService,
  ) {}

  async executeEmbossing(
    embossing: CreateCardEmbossingDto,
  ): Promise<ApiResponseDto> {
    /* ** Get token ** */
    const auth = await this.authServices.getAuthDock();

    /* ** Agent ** */
    const httpsAgent = new https.Agent({
      cert: auth.certificate,
      key: auth.key,
      rejectUnauthorized: false,
    });

    const response = await axios.post(
      `${this.configService.get('DOCK_URL_GLOBAL')}/embossing/v1/files/${embossing.id_embossing}/generate`,
      {},
      {
        headers: {
          Authorization: `Bearer ${auth.bearer_token}`,
        },
        httpsAgent,
      },
    );

    return ResponseUtil.success(
      'Card embossing created successfully',
      response.data,
      200,
    );
  }
}
