import { CreateEntityDto } from 'src/contexts/entity/application/create-entity/create-entity.dto';
import { UpdateEntityDto } from 'src/contexts/entity/application/update-entity/update-entity.dto';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';

export interface EntityRepository {
  save(entity: CreateEntityDto): Promise<ApiResponseDto>;
  findAll(): Promise<ApiResponseDto>;
  findById(id: string): Promise<ApiResponseDto>;
  update(id: string, entity: UpdateEntityDto): Promise<ApiResponseDto>;
  delete(id: string): Promise<ApiResponseDto>;
}
