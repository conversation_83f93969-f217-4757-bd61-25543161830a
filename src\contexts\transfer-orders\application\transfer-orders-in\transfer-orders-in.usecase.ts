import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import * as https from 'https';

/* ** Repositories ** */
import { TransferOrdersRepositoryImpl } from '../../infrastructure/repository/transfer-orders.repository.impl';
import { PersonRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/person.repository.impl';
import { CommissionRepositoryImpl } from 'src/contexts/commission/infrastructure/repositories/commission.repository.impl';

/* ** DTOs ** */
import {
  SignatureDto,
  ParamsTransferOrderInDto,
  ResTransferOrderInDto,
  ParamsTransferDto,
  ResponseTransferOrderInDto,
  ParamsPayCommissionDto,
} from './transfer-orders-in.dto';

/* ** Enums ** */
import { TypeCommissionEnum } from 'src/contexts/shared/enums/commission.enum';

/* ** Utils ** */
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';
import { UsersRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/users.repository.impl';
import { CommissionsUtils } from 'src/contexts/shared/utils/commissions/commissions.utils';
import { TransferEncryptSign } from 'src/contexts/shared/utils/transfer-encrypt-sign/transfer-encrypt-sign.utils';

@Injectable()
export class TransferIn {
  /* ** Constants ** */
  private defaul_commission: number = 0.01;
  constructor(
    private readonly config: ConfigService,
    private readonly transfer: TransferOrdersRepositoryImpl,
    private readonly person: PersonRepositoryImpl,
    private readonly userAdmin: UsersRepositoryImpl,
    private readonly authDock: authTokenDock,
    private readonly commissions: CommissionsUtils,
    private readonly commissionImpl: CommissionRepositoryImpl,
    private readonly sign: TransferEncryptSign,
  ) {}
  async webhookTransferIn(
    transfer: ParamsTransferOrderInDto,
  ): Promise<ResTransferOrderInDto> {
    /** Ignorar si es una transferencia de comisión */
    if (transfer.type === 'commission_in') {
      return { returnCode: 0 };
    }

    /** Almacenar el webhook recibido */
    this.transfer.save({
      type: 'transfer-in',
      data: JSON.stringify(transfer),
    });

    const verify = await this.verifySignature(transfer.data);

    if (!verify) {
      return { returnCode: 7 };
    }

    if (!transfer.data) return { returnCode: 7 };

    const { beneficiaryAccount, amount, concept } = transfer.data;

    if (!beneficiaryAccount) return { returnCode: 6 };

    const current_balance = await this.getAccountDetails();
    if (Number(current_balance) < Number(amount)) return { returnCode: 4 };

    const user =
      await this.person.findUserWithClabeOnlyCompany(beneficiaryAccount);

    if (!user) return { returnCode: 6 };

    // if (Number(amount) > Number(user.amount)) return { returnCode: 4 };

    if (!user.spei_in) return { returnCode: 99 };

    const payload = this.createPayloadTransfer({
      amount,
      concept,
      creditor_key: user.accoun_dock_id,
      debtor_key: this.config.get<string>('DOCK_OPERATING_ACCOUNT_ID'),
    });

    const executeTransfer = async (): Promise<ResponseTransferOrderInDto> => {
      try {
        const auth = await this.authDock.getAuthDock();
        const httpsAgent = new https.Agent({
          cert: auth.certificate,
          key: auth.key,
          rejectUnauthorized: false,
        });

        const response = await axios.post(
          `${this.config.get('DOCK_URL_GLOBAL')}/dockpay/v1/transfers`,
          payload,
          {
            headers: {
              Authorization: `Bearer ${auth.bearer_token}`,
              'Content-Type': 'application/json',
            },
            httpsAgent,
          },
        );

        return {
          event_status: response.status,
          operation_instance_id: response.data.operation_instance_id,
        };
      } catch (error) {
        console.error('Error al realizar la transferencia:', error.message);
        return {
          event_status: error?.response?.status || 500,
          operation_instance_id: null,
        };
      }
    };

    const { event_status, operation_instance_id } = await executeTransfer();

    /** Solo si fue exitosa la transferencia, aplicar comisión */
    const user_admin = await this.userAdmin.findUserWithAdminSettingsByEmail(
      user.email,
    );

    if (event_status === 200 || event_status === 201) {
      const pct = user_admin.spei_in ?? 0;
      const operation = (Number(amount) * pct) / 100;
      const new_commision =
        operation >= 1 ? operation.toFixed(2) : operation.toFixed(2);
      const commission_aplied =
        Number(new_commision) < this.defaul_commission
          ? this.defaul_commission
          : new_commision;
      const commission = Number(commission_aplied);

      const concentrator_account = this.config.get<string>(
        'DOCK_OPERATING_ACCOUNT_ID',
      );

      const account_commission = this.config.get<string>(
        'DOCK_COMMISSIONS_ACCOUNT_ID',
      );

      if (
        user.accoun_dock_id !== concentrator_account &&
        user.accoun_dock_id !== account_commission
      ) {
        this.payCommission({
          debtor_key: user.accoun_dock_id,
          creditor_key: this.config.get<string>('DOCK_COMMISSIONS_ACCOUNT_ID'),
          commission,
          type: 'SPEI_IN',
          operation_instance_id,
          amount: Number(amount).toFixed(2),
          embajador_account: user_admin.ambassador ?? 'N/A',
          player_account: transfer?.data?.payerAccount || 'N/A',
        });
      }
    }

    return {
      returnCode: event_status === 200 || event_status === 201 ? 0 : 99,
    };
  }

  private createPayloadTransfer(params: ParamsTransferDto) {
    return {
      debtor: {
        key: params.debtor_key,
        key_type: 'ACCOUNT_ID',
        balance_category: 'GENERAL',
      },
      creditor: {
        key: params.creditor_key,
        key_type: 'ACCOUNT_ID',
        balance_category: 'GENERAL',
      },
      amount: Number(params.amount),
      operation_type: 'P2P - P2P_OUT',
      description: params.concept,
    };
  }

  private async getAccountDetails(): Promise<number> {
    const executeRequest = async (): Promise<number> => {
      try {
        /* ** Get the authentication token and certificates */
        const auth = await this.authDock.getAuthDock();

        /* ** Configure the HTTPS agent with the certificates */
        const httpsAgent = new https.Agent({
          cert: auth.certificate,
          key: auth.key,
          rejectUnauthorized: false,
        });

        /* ** Payload Request */
        const requestBody = {
          person_id: this.config.get<string>('DOCK_OPERATING_PERSON_ID'),
          id: this.config.get<string>('DOCK_OPERATING_ACCOUNT_ID'),
          external_account_id: this.config.get<string>(
            'DOCK_OPERATING_ACCOUNT_ID',
          ),
          metadata: {
            pagination: {
              page: 0,
              limit: 10,
            },
            sort: {
              field: 'id',
              order: 'asc',
            },
          },
        };
        /* ** Make the request to Dock's API */
        const response = await axios.post(
          `${this.config.get<string>('DOCK_URL_GLOBAL')}/account-services/management/v1/account-details`,
          requestBody,
          {
            headers: {
              Authorization: `Bearer ${auth.bearer_token}`,
              'Content-Type': 'application/json',
            },
            httpsAgent,
          },
        );

        /* ** Assign Data Balance */
        const data = response.data;
        const availableResource = data
          ? data.content[0].sub_account_instances[0]
              .balance_category_instances[0].balance_type_instances[0]
              .current_balance
          : 0;

        return availableResource;
      } catch (error) {
        console.error(
          'Error obteniendo los detalles de la cuenta desde Dock:',
          error.response?.data || error.message,
        );
        return 0;
      }
    };

    const response = await executeRequest();

    return response;
  }

  private async payCommission(
    params: ParamsPayCommissionDto,
  ): Promise<boolean> {
    const account = this.config.get<string>('DOCK_OPERATING_ACCOUNT_ID');
    const account_commission = this.config.get<string>(
      'DOCK_COMMISSIONS_ACCOUNT_ID',
    );

    if (
      params.debtor_key === account ||
      params.debtor_key === account_commission
    ) {
      return false;
    }

    const save = await this.commissionImpl.save({
      type_commission: TypeCommissionEnum.IN,
      amount: params.amount,
      embajador_account: params.embajador_account,
      player_account: params.player_account,
      status: 'PENDING',
    });

    const transaction = await this.commissions.payCommissions({
      debtor_key: params.debtor_key,
      creditor_key: params.creditor_key,
      commission: params.commission,
      type: params.type,
      operation_instance_id: params.operation_instance_id,
      external_transaction_id: save.id,
    });

    if (transaction.status_code === 200 || transaction.status_code === 201) {
      await this.commissionImpl.updateCommision({
        id: save.id,
        status: 'APPROVED',
      });
    }

    return true;
  }

  private verifySignature = async (params: any): Promise<boolean> => {
    const payload: SignatureDto = {
      beneficiaryName: params.beneficiaryName,
      beneficiaryUid: params.beneficiaryUid,
      beneficiaryBank: params.beneficiaryBank,
      beneficiaryAccount: params.beneficiaryAccount,
      beneficiaryAccountType: params.beneficiaryAccountType,
      payerAccount: params.payerAccount,
      numericalReference: params.numericalReference,
      concept: params.concept,
      amount: Number(params.amount).toFixed(2),
      payerAccountType: params.payerAccountType,
      payerName: params.payerName,
      payerUid: params.payerUid,
      payerBank: params.payerBank,
      trackingKey: params.trackingKey,
    };

    const signature = this.createSing(payload);

    const verify = await this.sign.verifySign({
      sign: params.sign,
      signature: signature,
    });

    return verify;
  };

  private createSing = (body: SignatureDto): string => {
    /* ** We create the sign ** */
    const {
      beneficiaryName,
      beneficiaryUid,
      beneficiaryBank,
      beneficiaryAccount,
      beneficiaryAccountType,
      payerAccount,
      numericalReference,
      concept,
      amount,
      payerName,
      payerUid,
      payerBank,
      payerAccountType,
      trackingKey,
    } = body;

    const sing: string = `||${beneficiaryName}|${beneficiaryUid}|${beneficiaryAccount}|${beneficiaryBank}|${beneficiaryAccountType}|${payerName}|${payerUid}|${payerAccount}|${payerBank}|${payerAccountType}|${amount}|${concept}|${trackingKey}|${numericalReference}||`;

    return sing;
  };
}
