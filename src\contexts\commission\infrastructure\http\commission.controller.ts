import { Controller, UseGuards, Get, Query } from '@nestjs/common';

/* ** Use Cases ** */
import { GetBalanceAccountUseCase } from '../../application/get-balance-account/get-balance-account.usecase';
import { CommissionFetchUseCase } from '../../application/commission-fetch/commission-fetch.usecase';

/* ** DTOs ** */
import { RespondeGetBalanceAccountDto } from '../../application/get-balance-account/get-balance-account.dto';
import {
  ParamsCommissionFetchDto,
  ResponseCommissionFetchDto,
} from '../../application/commission-fetch/commission-fetch.dto';

/* ** Enums ** */
import { RoleEnum } from 'src/contexts/shared/enums/roles.enum';

/* ** Utils ** */
import { JwtAuthGuard } from 'src/contexts/auth/guards/auth.guard';
import { RoleProtected } from 'src/contexts/auth/decorators/role-protected.decorator';
import { UserRoleGuard } from 'src/contexts/auth/guards/user-role.guard';

@UseGuards(JwtAuthGuard)
@Controller('commission')
export class CommissionController {
  constructor(
    private readonly get: GetBalanceAccountUseCase,
    private readonly table: CommissionFetchUseCase,
  ) {}

  @RoleProtected(RoleEnum.CONVENIA_ADMIN)
  @UseGuards(UserRoleGuard)
  @Get('balance/account')
  async getBalance(): Promise<RespondeGetBalanceAccountDto> {
    return await this.get.balanceAccount();
  }

  @RoleProtected(RoleEnum.CONVENIA_ADMIN)
  @UseGuards(UserRoleGuard)
  @Get('transfers/datatable')
  async fetchCommissionData(
    @Query() params: ParamsCommissionFetchDto,
  ): Promise<ResponseCommissionFetchDto> {
    return await this.table.fetchCommissionData(params);
  }
}
