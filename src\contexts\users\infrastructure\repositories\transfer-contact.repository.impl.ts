import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';
import { TransferContactRepository } from '../../domain/repository/transfer-contact.repository';
import { CreateTransferContactDto } from '../../application/usecases-transfers/create-transfer-contact/create-transfer-contact.dto';
import { TransferContact } from '../../domain/entities/transfer-contact.entity';
import { UpdateTransferContactDto } from '../../application/usecases-transfers/update-transfer-contact/update-transfer-contact.dto';

export class TransferContactRepositoryImpl
  implements TransferContactRepository
{
  constructor(
    @InjectRepository(TransferContact)
    private readonly transferContactRepository: Repository<TransferContact>,
  ) {}

  async save(entity: CreateTransferContactDto): Promise<ApiResponseDto> {
    try {
      const transferContactEntity = this.transferContactRepository.create({
        ...entity,
        user: { id: entity.userId },
      });
      const res = await this.transferContactRepository.save(
        transferContactEntity,
      );
      return ResponseUtil.success('Contacto guardado exitosamente', res, 201);
    } catch (error) {
      return ResponseUtil.error('No se pudo guardar el contacto', error, 400);
    }
  }

  async findAll(): Promise<ApiResponseDto> {
    try {
      const res = await this.transferContactRepository.find();
      return ResponseUtil.success('Contactos obtenidos exitosamente', res, 200);
    } catch (error) {
      return ResponseUtil.error(
        'No se pudieron obtener los contactos',
        error,
        400,
      );
    }
  }

  async findById(id: string): Promise<ApiResponseDto> {
    try {
      const res = await this.transferContactRepository.findOne({
        where: { id },
      });
      if (!res) {
        return ResponseUtil.error(
          'Contacto no encontrado',
          'Contacto con el ID proporcionado no existe',
          404,
        );
      }
      return ResponseUtil.success('Contacto obtenido exitosamente', res, 200);
    } catch (error) {
      return ResponseUtil.error('No se pudo obtener el contacto', error, 400);
    }
  }

  async findByUser(idUser: string): Promise<ApiResponseDto> {
    try {
      const res = await this.transferContactRepository.find({
        where: { userId: idUser },
      });
      if (!res) {
        return ResponseUtil.error(
          'Contacto no encontrado',
          'Contacto con el ID proporcionado no existe',
          404,
        );
      }
      return ResponseUtil.success('Contacto obtenido exitosamente', res, 200);
    } catch (error) {
      return ResponseUtil.error('No se pudo obtener el contacto', error, 400);
    }
  }

  async update(
    id: string,
    entity: UpdateTransferContactDto,
  ): Promise<ApiResponseDto> {
    try {
      const res = await this.transferContactRepository.update({ id }, entity);
      return ResponseUtil.success(
        'Contacto actualizado exitosamente',
        res,
        200,
      );
    } catch (error) {
      return ResponseUtil.error(
        'No se pudo actualizar el contacto',
        error,
        400,
      );
    }
  }

  async remove(id: string): Promise<ApiResponseDto> {
    try {
      const res = await this.transferContactRepository.delete({ id });
      return ResponseUtil.success('Contacto eliminado exitosamente', res, 200);
    } catch (error) {
      return ResponseUtil.error('No se pudo eliminar el contacto', error, 400);
    }
  }
}
