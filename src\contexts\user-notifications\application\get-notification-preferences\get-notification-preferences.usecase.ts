import { Injectable, Inject } from '@nestjs/common';
import { NotificationPreferencesRepository } from '../../domain/repository/notification-preferences.repository';
import { GetNotificationPreferencesDto } from './get-notification-preferences.dto';

@Injectable()
export class GetNotificationPreferencesUseCase {
  constructor(
    @Inject('NotificationPreferencesRepository')
    private readonly preferencesRepository: NotificationPreferencesRepository,
  ) {}

  async execute(userId: string): Promise<GetNotificationPreferencesDto> {
    const preferences = await this.preferencesRepository.findByUserId(userId);
    if (!preferences) {
      return {
        receive_transaction_notifications: false,
        receive_reminder_notifications: false,
      };
    }
    return preferences;
  }
}
