import { Module } from '@nestjs/common';

/* ** Controllers ** */
import { CardEmbossingController } from '../infrastructure/http/card-embossing.controller';

/* ** Use Cases ** */
import { CreateCardEmbossingUseCase } from '../application/create-embossing/create-embossing.usecase';
import { ReadEmbossingUseCase } from '../application/read-embossing/read-embossing.usecase';

/* ** Providers ** */
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';

/* ** Module ** */
import { CustomRedisStoreModule } from 'src/contexts/shared/modules/redis.module';

@Module({
  imports: [CustomRedisStoreModule],
  controllers: [CardEmbossingController],
  providers: [CreateCardEmbossingUseCase, ReadEmbossingUseCase, authTokenDock],
})
export class CardEmbossingModule {}
