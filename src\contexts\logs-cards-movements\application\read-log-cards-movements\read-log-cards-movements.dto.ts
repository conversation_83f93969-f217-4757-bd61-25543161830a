import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsDateString } from 'class-validator';
import { PaginationDto } from 'src/contexts/shared/dto/pagination.dto';

export class ReadLogCardsMovementsDto extends PaginationDto {
  @ApiProperty({
    description: 'Filtrar por tarjeta anterior',
    example: 'CARD_12345',
    required: false,
  })
  @IsOptional()
  @IsString()
  old_card?: string;

  @ApiProperty({
    description: 'Filtrar por tarjeta nueva',
    example: 'CARD_67890',
    required: false,
  })
  @IsOptional()
  @IsString()
  new_card?: string;

  @ApiProperty({
    description: 'Filtrar por motivo',
    example: 'Tarjeta dañada',
    required: false,
  })
  @IsOptional()
  @IsString()
  reason?: string;

  @ApiProperty({
    description: 'Fecha de inicio para filtrar registros',
    example: '2024-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  start_date?: string;

  @ApiProperty({
    description: 'Fecha de fin para filtrar registros',
    example: '2024-12-31',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  end_date?: string;
}