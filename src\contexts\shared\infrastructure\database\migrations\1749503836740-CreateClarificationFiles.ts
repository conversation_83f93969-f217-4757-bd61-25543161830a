import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateClarificationFiles1749503836740
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE "clarification_files" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "clarification_tracking_number" varchar(6) NOT NULL,
        "file_name" text NOT NULL,
        "file_url" text NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_clarification_files" PRIMARY KEY ("id"),
        CONSTRAINT "FK_clarification_files_clarification"
          FOREIGN KEY ("clarification_tracking_number")
          REFERENCES "clarification"("trackingNumber")
          ON DELETE CASCADE
      );
  `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "clarification_files";`);
  }
}
