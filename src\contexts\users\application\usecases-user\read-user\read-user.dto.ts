import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { PaginationDto } from 'src/contexts/shared/dto/pagination.dto';
import { RoleEnum } from 'src/contexts/shared/enums/roles.enum';
import { RoleTypeEnum } from 'src/contexts/users/domain/entities/rol.entity';

export class FilterConveniaUsersDto extends PaginationDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  q?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  admin?: string;

  userId?: string;

  roleType?: RoleTypeEnum;
  roleName?: RoleEnum;
}
