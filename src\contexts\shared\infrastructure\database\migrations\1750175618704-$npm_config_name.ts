import { MigrationInterface, QueryRunner } from "typeorm";

export class  $npmConfigName1750175618704 implements MigrationInterface {
    name = ' $npmConfigName1750175618704'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_transfer" ALTER COLUMN "amount" TYPE DECIMAL(10,2)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_transfer" ALTER COLUMN "amount" TYPE NUMERIC`);
    }

}
