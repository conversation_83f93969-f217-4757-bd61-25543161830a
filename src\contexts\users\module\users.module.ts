import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

/* ** Entities ** */
import { Users } from '../domain/entities/users.entity';
import { Admin } from '../domain/entities/admin.entity';
import { Address } from '../domain/entities/address.entity';
import { Rol } from '../domain/entities/rol.entity';
import { Person } from '../domain/entities/person.entity';
import { Account } from '../domain/entities/account.entity';
import { RelUserRoleAdmin } from '../domain/entities/rel-user-role-admin.entity';
import { AccountCards } from '../domain/entities/account-cards.entity';
import { AccountTransfer } from '../domain/entities/account-transfer.entity';
import { UserTransfer } from '../domain/entities/user_transfers.entity';

/* ** Controllers ** */
import { UserController } from '../infrastructure/http/user.controller';
import { AdminController } from '../infrastructure/http/admin.controller';

/* ** Modules ** */
import { PersonAccountModule } from 'src/contexts/person-account/module/person-account.module';
import { TransferAccountClabeModule } from 'src/contexts/shared/modules/transfer-account.module';
import { ConveniaAccountModule } from 'src/contexts/shared/modules/convenia-account.module';

/* ** Use Cases ** */
import { CreateUserUseCase } from '../application/usecases-user/create-user/create-user.usecase';
import { DeleteUserUseCase } from '../application/usecases-user/delete-user/delete-user.usecase';
import { ReadUserUseCase } from '../application/usecases-user/read-user/read-user.usecase';
import { UpdateUserUseCase } from '../application/usecases-user/update-user/update-user.usecase';
import { ReadUsersUseCase } from '../application/usecases-user/read-user/read-users.usecase';
import { CreateAdminUseCase } from '../application/usecases-admin/create-admin/create-admin.usecase';
import { DeleteAdminUseCase } from '../application/usecases-admin/delete-admin/delete-admin.usecase';
import { ReadAdminUseCase } from '../application/usecases-admin/read-admin/read-admin.usecase';
import { UpdateAdminUseCase } from '../application/usecases-admin/update-admin/update-admin.usecase';
import { ReadAdminsUseCase } from '../application/usecases-admin/read-admin/read-admins.usecase';
import { ReadRFCUseCase } from '../application/usecases-admin/read-rfc/read-rfc.usecase';
import { AddressRepositoryImpl } from '../infrastructure/repositories/addressrepository.impl';
import { RoleRepositoryImpl } from '../infrastructure/repositories/role.repository.impl';
import { CreateAddressUseCase } from '../application/usecases-address/create-address/create-address-usecase';
import { CreateAccountUseCase } from '../application/usecases-account/create-account/create-account-usecase';
import { CreatePersonUseCase } from '../application/usecases-person/create-person/create-person-usecase';
import { UpdatePersonUseCase } from '../application/usecases-person/update-person/update-person-usecase';
import { CreateAdminMassiveUseCase } from '../application/usecases-admin/create-admin-massive/create-admin-massive.usecase';
import { CreateConveniaAccountUseCase } from '../application/usecases-user/create-convenia-account/create-convenia-account.usecase';
import { GetUserAccountUseCase } from '../application/usecases-user/get-user-account/get-user-account.usecase';

/* ** Repositories ** */
import { UsersRepositoryImpl } from '../infrastructure/repositories/users.repository.impl';
import { RelUserRoleAdminRepositoryImpl } from '../infrastructure/repositories/rel-user-role-admin.repository.impl';
import { AdminRepositoryImpl } from '../infrastructure/repositories/adminrepository.impl';
import { PersonRepositoryImpl } from '../infrastructure/repositories/person.repository.impl';
import { AccountRepositoryImpl } from '../infrastructure/repositories/account.repository.impl';
import { UserTransferRepositoryImpl } from '../infrastructure/repositories/user-transfer.repository.impl';
import { UpdateAddressUseCase } from '../application/usecases-address/update-address/update-address.usecase';
import { AuthModule } from 'src/contexts/auth/module/auth.module';
import { CustomMailerModule } from 'src/contexts/shared/modules/mailer.module';
import { CreateLegPersAccUseCase } from 'src/contexts/person-account/application/create-person-account-legal/create-pers-acc-leg.usecase';
import { UpdateLegPersAccUseCase } from 'src/contexts/person-account/application/update-person-account-legal/update-person-account-legal.usecase';
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';
import { RedisService } from 'src/contexts/shared/utils/Redis/redis';
import { AccountCardRepositoryImpl } from '../infrastructure/repositories/account-card.repository.impl';
import { StorageS3Utils } from 'src/contexts/shared/utils/storageUtils/storageS3.utils';
import { TransferContact } from '../domain/entities/transfer-contact.entity';
import { CreateTransferContactUseCase } from '../application/usecases-transfers/create-transfer-contact/create-transfer-contact.usecase';
import { TransferContactRepositoryImpl } from '../infrastructure/repositories/transfer-contact.repository.impl';
import { TransferContactController } from '../infrastructure/http/transfer-contact.controller';
import { ReadAminTotalAmountUseCase } from '../application/usecases-admin/read-total-amount/read-total-amount.usecase';
import { ReadTransferContactUseCase } from '../application/usecases-transfers/read-transfer-contact/read-transfer-contact.usecase';
import { DockpayTransferUseCase } from '../../transfers/application/create-transaction/dockpay-transfer.usecase';
import { EncryptData } from 'src/contexts/shared/utils/sensitive-data/encriptData.util';
import { AccountModule } from 'src/contexts/account/module/account.module';
import { ReadTotalUsersUseCase } from '../application/usecases-user/read-total-users/read-total-users.usecase';
import { DockModule } from 'src/contexts/dock/module/dock.module';
import { DeleteTransferContactUseCase } from '../application/usecases-transfers/delete-transfer-contact/delete-transfer-contact.usecase';
import { AccountTransferRepositoryImpl } from '../infrastructure/repositories/account.transfer.repository.impl';
import { ReadRolesUseCase } from '../application/usecases-roles/read-roles/read-roles.usecase';
import { RoleController } from '../infrastructure/http/role.controller';
import { ClarificationController } from '../infrastructure/http/clarification.controller';
import { CreateClarificationUseCase } from '../application/usecases-clarification/create-clarification/create-clarification.usecase';
import { UpdateClarificationUseCase } from '../application/usecases-clarification/update-clarification/update-clarification.usecase';
import { ClarificationRepositoryImpl } from '../infrastructure/repositories/clarification.repository.impl';
import { Clarification } from '../domain/entities/clarification.entity';
import { UserAccountController } from '../infrastructure/http/user-account.controller';
import { ReadUserAccountsUseCase } from '../application/usecases-user-account/read-user-accounts/read-user-accounts.usecase';
import { UpdateUserAccountsUseCase } from '../application/usecases-user-account/update-user-account/update-user-accounts.usecase';
import { UpdateTransferContactUseCase } from '../application/usecases-transfers/update-transfer-contact/update-transfer-contact.usecase';
import { CardDockModule } from 'src/contexts/dock-cards/module/dock-cards.module';
import { S3MetadataRepositoryImpl } from '../infrastructure/repositories/s3-metadata.repository.impl';
import { AdminDocumentsRepositoryImpl } from '../infrastructure/repositories/admin-docuemnts.repository.impl';
import { S3Metadata } from '../domain/entities/s3-metadata.entity';
import { AdminDocuments } from '../domain/entities/admin-documents.entity';
import { BullQueueModule } from 'src/contexts/shared/modules/BullQueue.module';
import { BulkTransfers } from '../domain/entities/bulk-transfers.entity';
import { BulkTransfersError } from '../domain/entities/bulk-transfers-errors.entity';
import { ClarificationModule } from './clarification.module';
import { ReadClarificationUseCase } from '../application/usecases-clarification/read-clarification/read-clarification.usecase';
import { ReadOneClarificationUseCase } from '../application/usecases-clarification/read-clarification/read-one-clarification.usecase';
import { AssignRoleUseCase } from '../application/usecases-user/asign-role/asign-role.usecase';
import { ControlDeleteDockCardUseCase } from 'src/contexts/dock-cards/apps/delete-dock-card/delete-dock-card.usecase';

/* ** Processors ** */
import { DeleteAdminUsersProcessor } from '../infrastructure/queue/delete-user.processor';
import { DeleteUserAccountUseCase } from '../application/usecases-user-account/delete-user-account/delete-user-account.usecase';
import { ReadTotalCardsUseCase } from '../application/usecases-admin/read-total-cards/read-total-cards.usecase';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Users,
      Admin,
      Address,
      Rol,
      Person,
      Account,
      RelUserRoleAdmin,
      AccountCards,
      TransferContact,
      Clarification,
      AccountTransfer,
      UserTransfer,
      S3Metadata,
      AdminDocuments,
      BulkTransfers,
      BulkTransfersError,
    ]),
    ClarificationModule,
    forwardRef(() => TransferAccountClabeModule),
    forwardRef(() => ConveniaAccountModule),
    PersonAccountModule,
    AuthModule,
    CustomMailerModule,
    AccountModule,
    DockModule,
    forwardRef(() => CardDockModule),
    BullQueueModule,
  ],
  controllers: [
    UserController,
    AdminController,
    TransferContactController,
    ClarificationController,
    RoleController,
    UserAccountController,
  ],
  providers: [
    CreateUserUseCase,
    DeleteUserUseCase,
    ReadUserUseCase,
    UpdateUserUseCase,
    ReadUsersUseCase,
    ReadRFCUseCase,
    CreateAddressUseCase,
    UsersRepositoryImpl,
    RelUserRoleAdminRepositoryImpl,
    CreateAdminUseCase,
    DeleteAdminUseCase,
    ReadAdminUseCase,
    UpdateAdminUseCase,
    ReadAdminsUseCase,
    AdminRepositoryImpl,
    AddressRepositoryImpl,
    RoleRepositoryImpl,
    CreatePersonUseCase,
    UpdatePersonUseCase,
    PersonRepositoryImpl,
    CreateAccountUseCase,
    AccountRepositoryImpl,
    UpdateAddressUseCase,
    CreateLegPersAccUseCase,
    UpdateLegPersAccUseCase,
    authTokenDock,
    RedisService,
    AccountCardRepositoryImpl,
    StorageS3Utils,
    CreateTransferContactUseCase,
    ReadTransferContactUseCase,
    TransferContactRepositoryImpl,
    DockpayTransferUseCase,
    EncryptData,
    ReadTotalUsersUseCase,
    ReadAminTotalAmountUseCase,
    DeleteTransferContactUseCase,
    AccountTransferRepositoryImpl,
    ReadRolesUseCase,
    CreateClarificationUseCase,
    UpdateClarificationUseCase,
    ReadClarificationUseCase,
    ReadOneClarificationUseCase,
    ClarificationRepositoryImpl,
    ReadUserAccountsUseCase,
    UpdateUserAccountsUseCase,
    UpdateTransferContactUseCase,
    UserTransferRepositoryImpl,
    S3MetadataRepositoryImpl,
    AdminDocumentsRepositoryImpl,
    CreateAdminMassiveUseCase,
    CreateConveniaAccountUseCase,
    AssignRoleUseCase,
    ControlDeleteDockCardUseCase,
    DeleteAdminUsersProcessor,
    DeleteUserAccountUseCase,
    GetUserAccountUseCase,
    ReadTotalCardsUseCase
  ],
  exports: [
    UsersRepositoryImpl,
    AccountRepositoryImpl,
    AccountCardRepositoryImpl,
    AccountTransferRepositoryImpl,
    PersonRepositoryImpl,
    UserTransferRepositoryImpl,
    ReadUserUseCase,
  ],
})
export class UsersModule {}
