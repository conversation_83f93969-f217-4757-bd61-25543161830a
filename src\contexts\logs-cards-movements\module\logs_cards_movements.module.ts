import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LogsCardsMovements } from '../domain/entities/logs-cards-movements.entity';
import { LogsCardsMovementsController } from '../infrastructure/http/logs-cards-movements.controller';
import { LogsCardsMovementsRepositoryImpl } from '../infrastructure/repositories/logs-cards-movements.repository.impl';
import { CreateLogCardsMovementsUseCase } from '../application/create-log-cards-movements/create-log-cards-movements.usecase';
import { ReadLogCardsMovementsUseCase } from '../application/read-log-cards-movements/read-log-cards-movements.usecase';
import { AuthModule } from 'src/contexts/auth/module/auth.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([LogsCardsMovements]),
    AuthModule,
  ],
  controllers: [LogsCardsMovementsController],
  providers: [
    // Use Cases
    CreateLogCardsMovementsUseCase,
    ReadLogCardsMovementsUseCase,
    // Repository Implementation
    LogsCardsMovementsRepositoryImpl,
  ],
  exports: [
    LogsCardsMovementsRepositoryImpl,
    ReadLogCardsMovementsUseCase,
    CreateLogCardsMovementsUseCase,
  ],
})
export class LogsCardsMovementsModule {}
