import { Injectable } from '@nestjs/common';

import { UpdatePersonEmailLegalDto } from './update-person-email-legal.dto';
import { UpdatePersonAddressLegalDto } from './update-person-address-legal.dto';
import { UpdateLegalPersonDto } from './update-legal-person.dto';
import { DockLegalPersonService } from 'src/contexts/dock/infraestructure/services/dock-legal-person.service';

@Injectable()
export class UpdateLegPersAccUseCase {

  constructor(
    readonly dockLegalPersonService: DockLegalPersonService
  ) {}

  async updateEmail(dto: UpdatePersonEmailLegalDto) : Promise<void> {  
    await this.dockLegalPersonService.updateEmail(dto);
  }

  async updateLegalPersonAddress(dto:  UpdatePersonAddressLegalDto) : Promise<void> {
    await this.dockLegalPersonService.updateLegalPersonAddress(dto);
  }

  async updateLegalPerson(dto: UpdateLegalPersonDto): Promise<void> {
    await this.dockLegalPersonService.updateLegalPerson(dto);
  }

}
