import { Injectable } from '@nestjs/common';
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import * as https from 'https';

/* ** DTOs ** */
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import {
  CreateLegPersAccDto,
  LegalPersonDataDto,
  AccountLegDataDto,
  ParamsSpendingGroupDto,
} from './create-pers-acc-leg.dto';

/* ** Utils ** */
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';

@Injectable()
export class CreateLegPersAccUseCase {
  constructor(
    private readonly configService: ConfigService,
    private readonly authTokenDock: authTokenDock,
  ) {}

  async executeLegPersAcc(leg: CreateLegPersAccDto): Promise<ApiResponseDto> {
    /* ** Formamos el input para el servicio de creación de persona natural ** */
    const person = await this.formatDataLegalPerson(leg);

    /* ** Obtenemos las credenciales de autenticación ** */
    const auth = await this.authTokenDock.getAuthDock();

    // Configurar el agente HTTPS con los certificados
    const httpsAgent = new https.Agent({
      cert: auth.certificate,
      key: auth.key,
      rejectUnauthorized: false,
    });

    /* ** Creamos la persona natural ** */

    try {
       const { data: leg_person } = await axios.post(
      `${this.configService.get('DOCK_URL_GLOBAL')}/person/v1/legal-persons`,
      person,
      {
        headers: {
          Authorization: `Bearer ${auth.bearer_token}`,
          'Content-Type': 'application/json',
        },
        httpsAgent,
      },
    );

    /* ** Creamos la cuenta de la persona natural ** */
    const accoun_data: AccountLegDataDto = {
      product_id: this.configService.get('DOCK_PRODUCT_ID'),
      person_id: leg_person.person_id,
    };

    const { data: acc_person } = await axios.post(
      `${this.configService.get('DOCK_URL_GLOBAL')}/account-services/management/v1/accounts`,
      accoun_data,
      {
        headers: {
          Authorization: `Bearer ${auth.bearer_token}`,
            'Content-Type': 'application/json',
          },
          httpsAgent,
        },
      );
      return ResponseUtil.success(
        'Create Person Account Natural successfully',
        { Person: leg_person, Account: acc_person },
        200,
      );
    } catch (error) {
      console.error(error.response.data.error.error_details);
      throw new Error('Error creating legal person account: ' + error.message);
    }
  }

  private async formatDataLegalPerson(
    legal_person: CreateLegPersAccDto,
  ): Promise<LegalPersonDataDto> {
    return {
      status_id: 1,
      legal_name: legal_person.legal_name,
      documents: [
        {
          type_id: 16144,
          number: legal_person.rfc,
          is_main: true,
          country_code: legal_person.country_code,
        },
      ],
      phones: [
        {
          type_id: 58,
          is_main: true,
          dialing_code: legal_person.dialing_code,
          area_code: legal_person.area_code,
          number: legal_person.number,
          country_code: 'MX',
        },
      ],
      addresses: [
        {
          type_id: 63,
          is_main: true,
          postal_code: legal_person.postal_code,
          suffix: legal_person.suffix,
          street: legal_person.street,
          number: legal_person.addresses_number,
          city: legal_person.city,
          country_code: legal_person.country_code,
        },
      ],
      emails: [
        {
          type_id: 71,
          is_main: true,
          email: legal_person.email,
        },
      ],
    };
  }

  async assgnGroup(params: ParamsSpendingGroupDto): Promise<boolean> {
    try {
      /* ** Obtenemos las credenciales de autenticación ** */
      const auth = await this.authTokenDock.getAuthDock();

      // Configurar el agente HTTPS con los certificados
      const httpsAgent = new https.Agent({
        cert: auth.certificate,
        key: auth.key,
        rejectUnauthorized: false,
      });

      /* ** Creamos la persona natural ** */
      const data = await axios.post(
        `${this.configService.get('DOCK_URL_GLOBAL')}/spending-controls/groups/persons`,
        params,
        {
          headers: {
            Authorization: `Bearer ${auth.bearer_token}`,
            'Content-Type': 'application/json',
          },
          httpsAgent,
        },
      );

      return data.status === 200 && true;
    } catch (error) {
      throw new Error('Error assigning group: ' + error.message);
    }
  }

  private createAffiliationNumber(): string {
    return Math.floor(1000000 + Math.random() * 9000000).toString();
  }
}
