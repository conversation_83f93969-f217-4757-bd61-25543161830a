import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

/* ** Repositories ** */
import { TransferOrdersRepository } from '../../domain/repository/transfer-orders.repository';

/* ** Entities ** */
import { TransferOrders } from '../../domain/entities/transfer-orders.entity';

/* ** DTOs ** */
import { SaveDataTransferOrderDto } from '../../application/transfer-order-out/transfer-orders-out.dto';

@Injectable()
export class TransferOrdersRepositoryImpl implements TransferOrdersRepository {
  constructor(
    @InjectRepository(TransferOrders)
    private readonly transferRepository: Repository<TransferOrders>,
  ) {}

  async save(entity: SaveDataTransferOrderDto): Promise<TransferOrders> {
    return await this.transferRepository.save(entity);
  }
}
