import { Injectable } from '@nestjs/common';
import { City } from 'src/contexts/catalogs/domain/entities/city.entity';
import { CityRepositoryImpl } from 'src/contexts/catalogs/infrastructure/repositories/city.repository';

@Injectable()
export class ReadCitiesUseCase {
  constructor(private readonly cityRepository: CityRepositoryImpl) {}

  async execute(stateId: number): Promise<City[]> {
    return this.cityRepository.findByState(stateId);
  }
}