import {
  DockpayTransfertDto,
  ParamsDockPayTransfertDto,
  PayloadDockPayDto,
  ParamsValidatePaymentDto,
  TransferObjectDto,
  ResponsePaymentDto,
  ParamsBalanceToAccountDto,
  ResponsePaymentDockDto,
  ParamsCheckCommissionDto,
  DockPayBulkTransferstDto,
} from './dockpay-transfer.dto';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { BadRequestException, Injectable } from '@nestjs/common';
import * as https from 'https';
import axios from 'axios';
import { EncryptData } from 'src/contexts/shared/utils/sensitive-data/encriptData.util';
import { ConfigService } from '@nestjs/config';
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';
import { AccountCardRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/account-card.repository.impl';
import { PersonRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/person.repository.impl';
import { UserTransferRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/user-transfer.repository.impl';
import { UsersRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/users.repository.impl';
import {
  FindPanCardPayloadDto,
  ResponseSearchPanDto,
} from 'src/contexts/card-assignment/application/single-card-assignment/single-card-assignment.dto';
import { GetUserAccountService } from 'src/contexts/account/application/get-user-account/get-user-account.service';
import * as argon2 from 'argon2';
import { ReadUserUseCase } from 'src/contexts/users/application/usecases-user/read-user/read-user.usecase';

/* ** Enums ** */
import { PaymentEnum } from 'src/contexts/shared/enums/typePayment.enum';
import { CardStatus } from 'src/contexts/shared/interfaces/dtos/cards.enum.dto';
import { RoleTypeEnum } from 'src/contexts/users/domain/entities/rol.entity';
import {
  BulkTransfers,
  BulkTransfersStatus,
} from '../../../users/domain/entities/bulk-transfers.entity';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { StorageS3Utils } from 'src/contexts/shared/utils/storageUtils/storageS3.utils';
import { S3Metadata } from 'src/contexts/users/domain/entities/s3-metadata.entity';
import { Users } from 'src/contexts/users/domain/entities/users.entity';
import { BulkTransfersError } from 'src/contexts/users/domain/entities/bulk-transfers-errors.entity';
import { PaginationDto } from 'src/contexts/shared/dto/pagination.dto';

@Injectable()
export class DockpayTransferUseCase {
  constructor(
    private readonly config: ConfigService,
    private readonly authDock: authTokenDock,
    private readonly encrypt: EncryptData,
    private readonly getUserAccountService: GetUserAccountService,
    private readonly accountCardRepositoryImpl: AccountCardRepositoryImpl,
    private readonly readUserUseCase: ReadUserUseCase,
    private readonly person: PersonRepositoryImpl,
    private readonly payment: UserTransferRepositoryImpl,
    private readonly user: UsersRepositoryImpl,
    @InjectQueue('bulk-transfers')
    private readonly bulkTransfersQueue: Queue,
    private readonly s3: StorageS3Utils,
  ) {}

  async execute(data: DockpayTransfertDto): Promise<ApiResponseDto> {
    /* ** Obtenemos la cuenta segun el email ** */
    const account = await this.getUserAccountService.getAccountAndPersonByEmail(
      data.email,
    );
    /* ** Verificamos que el usuario este registrado ** */
    if (!account) {
      return { message: 'Usuario no encontrado', statusCode: 404 };
    }
    /* ** Obtenemos la tarjeta y su id segun el num_clabe ** */
    const card = await this.searchPan(data.num_clabe);
    /* ** Verificamos que la tarjeta exista ** */
    if (!card) {
      return { message: 'Tarjeta no encontrada', statusCode: 404 };
    }
    /* ** Obtenemos la cuenta a la que esta relacionada la cuenta segun el numero de tarjeta ** */
    const card_account =
      await this.accountCardRepositoryImpl.findCardWithAccount(card.id);
    const payload: PayloadDockPayDto = {
      data,
      debtorId: account.account_id,
      creditorId: card_account.account.accountExtID,
    };
    /* ** Formamos el input para el servicio de transferencia ** */
    const transferObject = await this.createTransferObject(payload);

    // Configurar el agente HTTPS con los certificados
    /* ** Obtenemos las credenciales de autenticación ** */
    const auth = await this.authDock.getAuthDock();
    const httpsAgent = new https.Agent({
      cert: auth.certificate,
      key: auth.key,
      rejectUnauthorized: false,
    });
    /* ** Realizamos la transferencia ** */
    const response = await axios.post(
      `${this.config.get('DOCK_URL_GLOBAL')}/dockpay/v1/transfers`,
      transferObject,
      {
        headers: {
          Authorization: `Bearer ${auth.bearer_token}`,
          'Content-Type': 'application/json',
        },
        httpsAgent,
      },
    );
    if (response.status === 201) {
      return { message: 'Transacción exitosa', statusCode: 201 };
    }
    return { message: 'Error al realizar la transacción', statusCode: 500 };
  }

  async executeValidatePassword(
    email: string,
    password: string,
  ): Promise<boolean> {
    const user =
      await this.readUserUseCase.executeEmailToCheckAllUserData(email);
    if (!user) return false;
    return await argon2.verify(user.password, password);
  }

  async executePay(data: ParamsDockPayTransfertDto): Promise<ApiResponseDto> {
    /* ** Variable para almecenar el resultado de la transferencia ** */
    let result: ResponsePaymentDto = null;
    let type_pay: string = null;
    /* ** Validamos el tipo de pago que se va a realizar ** */
    if (data.num_clabe.length === 11) {
      type_pay = 'ACCOUNT';
    } else if (data.num_clabe.length === 16) {
      type_pay = 'CARD';
    } else if (data.num_clabe.length === 18) {
      type_pay = 'CLABE';
    }

    if (type_pay === null) {
      return {
        message: 'Beneficiario no válido',
        statusCode: 400,
      };
    }

    /* ** Actualizar los datos de la transferencia ** */
    data.description = this.normalizeConcept(data.description);
    data.beneficiaryName = this.normalizeBeneficiaryName(data.beneficiaryName);

    /* ** Consultamos si la cuenta clabe pertenece al universo Convenial ** */
    const clabe_convenia = await this.person.findUserWithClabe(data.num_clabe);

    if (clabe_convenia) {
      type_pay = 'CARD';
      result = await this.executePaymentClabeDock(data);
    } else if (type_pay === 'CARD') {
      result = await this.executePaymentDock(data);
    } else if (type_pay === 'CLABE') {
      result = await this.executePaymentSpei(data);
    } else if (type_pay === 'ACCOUNT') {
      type_pay = 'CARD';
      result = await this.executePaymentAccountDock(data);
    }

    if (!result) {
      return { message: result.message, statusCode: result.status_code };
    }

    if (result && !result.id) {
      return {
        message: result.message || 'Error al realizar la transacción',
        statusCode: result.status_code || 500,
      };
    }

    /* ** Response de la transferencia ** */
    let status_payment: number = 400;
    let message_payment: string = 'Error al realizar la transacción';
    let cep: string = null;
    let commission: string = result.commission || '0.00';
    const transaction_id: string = result.id || null;
    const external_transaction_id: string = result.transaction_id || null;
    const reference: string = result.reference || null;

    /* ** Validamos el estado de la transferencia ** */
    if (result.status_code === 201 || result.status_code === 200) {
      /* ** Ejecutamos una serie de consultas para verificar el estado de la transferencia ** */
      for (let i = 0; i < 30; i++) {
        await this.sleep(2500);
        const state_payment = await this.payment.findPaymentState(result.id);

        const params_approve: ParamsValidatePaymentDto = {
          type: type_pay === 'CARD' ? PaymentEnum.DOCK : PaymentEnum.TRANSFER,
          state_dock: state_payment.status_dock,
          state_transfer: state_payment.status_transfer,
        };

        cep = state_payment.cep || null;

        if (this.isAproved(params_approve)) {
          status_payment = 201;
          message_payment = 'Transacción exitosa';
          commission = state_payment.commission || '0.00';
          break;
        } else if (this.isRejected(params_approve)) {
          status_payment = 400;
          message_payment = 'Transacción rechazada';
          commission = state_payment.commission || '0.00';
          break;
        } else {
          status_payment = 102;
          message_payment = 'Transacción en proceso';
        }
      }

      return {
        message: message_payment,
        statusCode: status_payment,
        data: {
          transaction_id,
          external_transaction_id,
          reference,
          cep,
          commission,
        },
      };
    }

    return { message: 'Error al realizar la transacción', statusCode: 500 };
  }

  private async createTransferObject(
    body: PayloadDockPayDto,
  ): Promise<TransferObjectDto> {
    return {
      debtor: {
        key: body.debtorId,
        key_type: 'ACCOUNT_ID',
        balance_category: 'GENERAL',
      },
      creditor: {
        key: body.creditorId,
        key_type: 'ACCOUNT_ID',
        balance_category: 'GENERAL',
      },
      amount: Number(body.data.amount),
      operation_type: 'P2P - P2P_OUT',
      description: body.data.description,
      external_transaction_id: body.external_transaction_id,
    };
  }

  private async searchPan(pan: string): Promise<ResponseSearchPanDto> {
    /* ** Encrypt data ** */
    const encrypt = await this.encrypt.encryptData({
      encrypted_data: pan,
    });

    const { bearer_token, certificate, key } =
      await this.authDock.getAuthDock();

    const payload: FindPanCardPayloadDto = {
      pan: encrypt.encrypted_data,
      iv: encrypt.iv,
      aes: encrypt.aes,
      mode: encrypt.mode,
    };

    const httpsAgent = new https.Agent({
      cert: certificate,
      key,
      rejectUnauthorized: false,
    });

    const executeSearch = async (): Promise<ResponseSearchPanDto> => {
      try {
        const data = await axios.post(
          `${this.config.get('DOCK_URL_GLOBAL')}/cards/v1/cards/pan`,
          payload,
          {
            headers: {
              Authorization: `Bearer ${bearer_token}`,
            },
            httpsAgent,
          },
        );

        return data.data;
      } catch (error) {
        const code = error.response.status;
        if (code === 400 || code === 401 || code === 403 || code === 500) {
          console.error('Error In Search Pan', error.message);
        }

        return null;
      }
    };

    const response = await executeSearch();
    return response;
  }

  private generateReference(): number {
    return Number(
      Array(7)
        .fill(0)
        .map(() => Math.floor(Math.random() * 10))
        .join(''),
    );
  }

  private async getBalanceTransfer(): Promise<number> {
    try {
      const url = `${this.config.get('TRANSFER_URL')}/api/1.0/balances/`;
      const clabe = this.config.get<string>('DOCK_OPERATING_CLABE_ID');
      const response = await axios.post(
        url,
        { account: clabe },
        {
          headers: {
            'X-Custom-Auth': this.config.get('TRANSFER_TOKEN'),
            Accept: 'application/json',
          },
        },
      );

      /* ** Balance To Account ** */
      let current_balance = 0;

      /* ** Code Response ** */
      const code = response?.data?.code;
      current_balance = code === 200 ? response.data.data.availableBalance : 0;

      return current_balance;
    } catch (error) {
      console.log('error', error);
      return 0;
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  private isAproved(params: ParamsValidatePaymentDto): boolean {
    const { type, state_dock, state_transfer } = params;

    if (type === PaymentEnum.DOCK) {
      return state_dock === 'APPROVED';
    }
    if (type === PaymentEnum.TRANSFER) {
      return state_dock === 'APPROVED' && state_transfer === 'scattered';
    }
    return false;
  }

  private isRejected(params: ParamsValidatePaymentDto): boolean {
    const { type, state_dock, state_transfer } = params;

    if (type === PaymentEnum.DOCK) {
      return state_dock === 'DENIED';
    }
    if (type === PaymentEnum.TRANSFER) {
      return (
        state_dock === 'APPROVED' &&
        (state_transfer === 'canceled' || state_transfer === 'returned')
      );
    }
    return false;
  }

  private async executePaymentDock(
    data: ParamsDockPayTransfertDto,
  ): Promise<ResponsePaymentDto> {
    /* ** Obtenemos la cuenta segun el email ** */
    const account = await this.person.findUserWithEmail(data.email);

    /* ** Verificamos que el usuario este registrado ** */
    if (!account) {
      return { message: 'Usuario no encontrado', status_code: 404 };
    }

    /* ** Validamos si tiene activa la transferencia ** */
    if (!account.spei_out) {
      return {
        message: 'Acceso denegado, transferencia no habilitada',
        status_code: 403,
      };
    }

    // ─── 2) Validar PAN y beneficiario ─────────────────────────────────────────
    const card = await this.searchPan(data.num_clabe);

    /* ** Verificamos que la tarjeta exista ** */
    if (!card) {
      return { message: 'Tarjeta no encontrada', status_code: 404 };
    }
    if (card.status !== CardStatus.NORMAL) {
      return {
        message: 'La tarjeta del beneficiario no se encuentra activa',
        status_code: 403,
      };
    }
    const card_account = await this.user.findUserWithCardID(card.id);

    /* ** Validamos cuenta del credior ** */
    if (!card_account) {
      return {
        message: 'Cuenta del beneficiario no encontrada',
        status_code: 404,
      };
    }
    if (!card_account.spei_in) {
      return {
        message:
          'Acceso denegado, beneficiario no habilitado para recibir transferencias',
        status_code: 403,
      };
    }

    /* ** Validamos si el usuario tiene fondos suficientes para realizar la transferencia con comisión ** */
    const { totalAmount, commission } = await this.checkCommissions({
      email: data.email,
      amount: data.amount,
      type: PaymentEnum.DOCK,
      creditor_dock_id: card_account.accoun_dock_id,
    });

    const balance_account = await this.getAccountDetails({
      person_id: account.person_dock_id,
      account_id: account.accoun_dock_id,
    });
    if (balance_account && totalAmount > balance_account) {
      return {
        message:
          'El monto a transferir más la comisión excede el saldo disponible',
        status_code: 403,
      };
    }

    // ─── 3) Guardar orden y enviar a Dock ──────────────────────────────────────
    const reference = this.generateReference();

    /* ** Almacenamos la order de transferencia ** */
    const save_payment = await this.payment.save({
      reference,
      payment_type: PaymentEnum.DOCK,
      bank: null,
      beneficiary_account: data.num_clabe ?? null,
      beneficiary_name: data.beneficiaryName ?? null,
      player_account: account.clabe,
      email: data.email,
      player_account_id: account.accoun_dock_id,
      amount: data.amount,
      bank_name: null,
      concept: data.description ?? null,
    });

    /* ** Payload de la transferencia dependiendo del tipo de pago ** */
    const payload: PayloadDockPayDto = {
      data,
      debtorId: account.accoun_dock_id,
      creditorId: card_account.accoun_dock_id,
      external_transaction_id: save_payment.id,
    };
    const transferObject = await this.createTransferObject(payload);
    const dock = await this.executeRequestPayment(transferObject);

    // ─── 4) Armar respuesta incluyendo comisión ────────────────────────────────
    return {
      status_code: dock.status_code,
      message:
        dock.status_code === 201 || dock.status_code === 200
          ? 'Transacción exitosa'
          : 'Error al realizar la transacción',
      id: save_payment.id,
      reference: String(reference),
      transaction_id: dock.id || null,
      commission,
    };
  }

  private async executePaymentClabeDock(
    data: ParamsDockPayTransfertDto,
  ): Promise<ResponsePaymentDto> {
    /* ** Obtenemos la cuenta segun el email ** */
    const account = await this.person.findUserWithEmail(data.email);

    /* ** Verificamos que el usuario este registrado ** */
    if (!account) {
      return { message: 'Usuario no encontrado', status_code: 404 };
    }

    /* ** Validamos si tiene activa la transferencia ** */
    if (!account.spei_out) {
      return {
        message: 'Acceso denegado, transferencia no habilitada',
        status_code: 403,
      };
    }

    // ─── 2) Validar cuenta del beneficiario SPEI_IN ─────────────────────────────
    const account_beneficiary = await this.person.findUserWithClabe(
      data.num_clabe,
    );

    /* ** Verificamos que la cuenta del beneficiario exista ** */
    if (!account_beneficiary) {
      return {
        message: 'Cuenta del beneficiario no encontrada',
        status_code: 404,
      };
    }
    if (!account_beneficiary.spei_in) {
      return {
        message: 'Beneficiario no habilitado para recibir transferencias',
        status_code: 403,
      };
    }

    /* ** Validamos si el usuario tiene fondos suficientes para realizar la transferencia con comisión ** */
    const { totalAmount, commission } = await this.checkCommissions({
      email: data.email,
      amount: data.amount,
      type: PaymentEnum.DOCK,
      creditor_dock_id: account_beneficiary.accoun_dock_id,
    });
    const balance_account = await this.getAccountDetails({
      person_id: account.person_dock_id,
      account_id: account.accoun_dock_id,
    });

    if (balance_account && totalAmount > balance_account) {
      return {
        message:
          'El monto a transferir más la comisión excede el saldo disponible',
        status_code: 403,
      };
    }

    // ─── 3) Guardar orden y enviar a Dock ──────────────────────────────────────
    const reference = this.generateReference();

    /* ** Almacenamos la order de transferencia ** */
    const save_payment = await this.payment.save({
      reference,
      payment_type: PaymentEnum.DOCK,
      bank: null,
      beneficiary_account: data.num_clabe ?? null,
      beneficiary_name: data.beneficiaryName ?? null,
      player_account: account.clabe,
      email: data.email,
      player_account_id: account.accoun_dock_id,
      amount: data.amount,
      bank_name: null,
      concept: data.description ?? null,
    });

    /* ** Payload de la transferencia dependiendo del tipo de pago ** */
    const payload: PayloadDockPayDto = {
      data,
      debtorId: account.accoun_dock_id,
      creditorId: account_beneficiary.accoun_dock_id,
      external_transaction_id: save_payment.id,
    };
    const transferObject = await this.createTransferObject(payload);
    const dock = await this.executeRequestPayment(transferObject);

    // ─── 4) Respuesta con comisión ─────────────────────────────────────────────
    return {
      status_code: dock.status_code,
      message:
        dock.status_code === 201 || dock.status_code === 200
          ? 'Transacción exitosa'
          : 'Error al realizar la transacción',
      id: save_payment.id,
      reference: String(reference),
      transaction_id: dock.id || null,
      commission,
    };
  }

  private async executePaymentAccountDock(
    data: ParamsDockPayTransfertDto,
  ): Promise<ResponsePaymentDto> {
    /* ** Obtenemos la cuenta segun el email ** */
    const account = await this.person.findUserWithEmail(data.email);

    /* ** Verificamos que el usuario este registrado ** */
    if (!account) {
      return { message: 'Usuario no encontrado', status_code: 404 };
    }

    /* ** Validamos si tiene activa la transferencia ** */
    if (!account.spei_out) {
      return {
        message: 'Acceso denegado, transferencia no habilitada',
        status_code: 403,
      };
    }

    // ─── 2) Validar cuenta del beneficiario SPEI_IN ─────────────────────────────
    const account_beneficiary = await this.person.findUserWithConveniaAccount(
      data.num_clabe,
    );

    /* ** Verificamos que la cuenta del beneficiario exista ** */
    if (!account_beneficiary) {
      return {
        message: 'Cuenta del beneficiario no encontrada',
        status_code: 404,
      };
    }
    if (!account_beneficiary.spei_in) {
      return {
        message: 'Beneficiario no habilitado para recibir transferencias',
        status_code: 403,
      };
    }

    /* ** Validamos si el usuario tiene fondos suficientes para realizar la transferencia con comisión ** */
    const { totalAmount, commission } = await this.checkCommissions({
      email: data.email,
      amount: data.amount,
      type: PaymentEnum.DOCK,
      creditor_dock_id: account_beneficiary.accoun_dock_id,
    });
    const balance_account = await this.getAccountDetails({
      person_id: account.person_dock_id,
      account_id: account.accoun_dock_id,
    });

    if (balance_account && totalAmount > balance_account) {
      return {
        message:
          'El monto a transferir más la comisión excede el saldo disponible',
        status_code: 403,
      };
    }

    // ─── 3) Guardar orden y enviar a Dock ──────────────────────────────────────
    const reference = this.generateReference();

    /* ** Almacenamos la order de transferencia ** */
    const save_payment = await this.payment.save({
      reference,
      payment_type: PaymentEnum.DOCK,
      bank: null,
      beneficiary_account: data.num_clabe ?? null,
      beneficiary_name: data.beneficiaryName ?? null,
      player_account: account.clabe,
      email: data.email,
      player_account_id: account.accoun_dock_id,
      amount: data.amount,
      bank_name: null,
      concept: data.description ?? null,
    });

    /* ** Payload de la transferencia dependiendo del tipo de pago ** */
    const payload: PayloadDockPayDto = {
      data,
      debtorId: account.accoun_dock_id,
      creditorId: account_beneficiary.accoun_dock_id,
      external_transaction_id: save_payment.id,
    };
    const transferObject = await this.createTransferObject(payload);
    const dock = await this.executeRequestPayment(transferObject);

    // ─── 4) Respuesta con comisión ─────────────────────────────────────────────
    return {
      status_code: dock.status_code,
      message:
        dock.status_code === 201 || dock.status_code === 200
          ? 'Transacción exitosa'
          : 'Error al realizar la transacción',
      id: save_payment.id,
      reference: String(reference),
      transaction_id: dock.id || null,
      commission,
    };
  }

  private async executePaymentSpei(
    data: ParamsDockPayTransfertDto,
  ): Promise<ResponsePaymentDto> {
    /* ** Obtenemos la cuenta segun el email ** */
    const account = await this.person.findUserWithEmail(data.email);

    /* ** Verificamos que el usuario este registrado ** */
    if (!account) {
      return { message: 'Usuario no encontrado', status_code: 404 };
    }

    /* ** Validamos si tiene activa la transferencia ** */
    if (!account.spei_out) {
      return {
        message: 'Acceso denegado, transferencia no habilitada',
        status_code: 403,
      };
    }

    /* ** Validamos si el usuario tiene fondos suficientes para realizar la transferencia con comisión ** */
    const { totalAmount, commission } = await this.checkCommissionsSpei({
      email: data.email,
      amount: data.amount,
      type: PaymentEnum.TRANSFER,
      creditor_dock_id: '',
    });

    const balance_dock = await this.getAccountDetails({
      person_id: account.person_dock_id,
      account_id: account.accoun_dock_id,
    });

    if (balance_dock && totalAmount > balance_dock) {
      return {
        message:
          'El monto a transferir más la comisión excede el saldo disponible',
        status_code: 403,
      };
    }

    /* ** Verificamos el saldo en Transfer para poder realizar la transferencia ** */
    const balance_spei = await this.getBalanceTransfer();
    /* ** Verificamos si tiene saldo suficiente para realizar la transferencia ** */
    if (Number(data.amount) > Number(balance_spei)) {
      return {
        message:
          'El saldo en Transfer es insuficiente para realizar la transferencia',
        status_code: 400,
      };
    }

    /* ** Reference de la transferencia ** */
    const reference = this.generateReference();

    /* ** Almacenamos la order de transferencia ** */
    const save_payment = await this.payment.save({
      reference,
      payment_type: PaymentEnum.TRANSFER,
      bank: data.legalBank ?? null,
      beneficiary_account: data.num_clabe ?? null,
      beneficiary_name: data.beneficiaryName ?? null,
      player_account: account.clabe,
      email: data.email,
      player_account_id: account.accoun_dock_id,
      amount: data.amount,
      bank_name: data.NameBank ?? null,
      concept: data.description ?? null,
    });

    /* ** Payload de la transferencia dependiendo del tipo de pago ** */
    const payload: PayloadDockPayDto = {
      data,
      debtorId: account.accoun_dock_id,
      creditorId: this.config.get('DOCK_OPERATING_ACCOUNT_ID'),
      external_transaction_id: save_payment.id,
    };

    const transferObject = await this.createTransferObject(payload);

    const dock = await this.executeRequestPayment(transferObject);

    return {
      status_code: dock.status_code,
      message:
        dock.status_code === 201 || dock.status_code === 200
          ? 'Transacción exitosa'
          : 'Error al realizar la transacción',
      id: save_payment.id,
      reference: String(reference),
      transaction_id: dock.id || null,
      commission,
    };
  }

  private async executeRequestPayment(
    body: TransferObjectDto,
  ): Promise<ResponsePaymentDockDto> {
    try {
      const auth = await this.authDock.getAuthDock();
      const httpsAgent = new https.Agent({
        cert: auth.certificate,
        key: auth.key,
        rejectUnauthorized: false,
      });
      /* ** Realizamos la transferencia ** */
      const response = await axios.post(
        `${this.config.get('DOCK_URL_GLOBAL')}/dockpay/v1/transfers`,
        body,
        {
          headers: {
            Authorization: `Bearer ${auth.bearer_token}`,
            'Content-Type': 'application/json',
          },
          httpsAgent,
        },
      );

      const data_response: ResponsePaymentDockDto = {
        status_code: response.status,
        id: response?.data?.operation_instance_id || null,
      };

      return data_response;
    } catch (error) {
      return { status_code: error.response.status };
    }
  }

  private async getAccountDetails(
    body: ParamsBalanceToAccountDto,
  ): Promise<number> {
    const executeRequest = async (): Promise<number> => {
      try {
        /* ** Get the authentication token and certificates */
        const auth = await this.authDock.getAuthDock();

        /* ** Configure the HTTPS agent with the certificates */
        const httpsAgent = new https.Agent({
          cert: auth.certificate,
          key: auth.key,
          rejectUnauthorized: false,
        });

        /* ** Payload Request */
        const requestBody = {
          person_id: body.person_id,
          id: body.account_id,
          external_account_id: body.account_id,
          metadata: {
            pagination: {
              page: 0,
              limit: 10,
            },
            sort: {
              field: 'id',
              order: 'asc',
            },
          },
        };
        /* ** Make the request to Dock's API */
        const response = await axios.post(
          `${this.config.get<string>('DOCK_URL_GLOBAL')}/account-services/management/v1/account-details`,
          requestBody,
          {
            headers: {
              Authorization: `Bearer ${auth.bearer_token}`,
              'Content-Type': 'application/json',
            },
            httpsAgent,
          },
        );

        /* ** Assign Data Balance */
        const data = response.data;
        const availableResource = data
          ? data.content[0].sub_account_instances[0]
              .balance_category_instances[0].balance_type_instances[0]
              .current_balance
          : 0;

        return availableResource;
      } catch (error) {
        console.error(
          'Error obteniendo los detalles de la cuenta desde Dock:',
          error.response?.data || error.message,
        );
        return 0;
      }
    };

    const response = await executeRequest();

    return response;
  }

  private async checkCommissions(
    params: ParamsCheckCommissionDto,
  ): Promise<{ totalAmount: number; commission: string }> {
    /* ** Consultamos la comisión del usuario ** */
    const user = await this.user.findCommisionsByEmail(params.email);

    const creditor_commission = await this.user.findCommisions(
      params.creditor_dock_id,
    );

    // Comisión fija (si el usuario tiene spei_out configurado > 0)
    if (
      user &&
      user.target_refound > 0 &&
      (user.role_type === RoleTypeEnum.CLIENT ||
        params.type === PaymentEnum.TRANSFER) &&
      user.role_type !== creditor_commission?.role_type
    ) {
      const pct = user.target_refound ?? 0;
      const commissionAmount = (Number(params.amount) * pct) / 100;
      const new_commision =
        commissionAmount >= 1
          ? commissionAmount.toFixed(2)
          : commissionAmount.toFixed(3);
      const transferAmount = Number(params.amount);
      const totalAmount = transferAmount + Number(new_commision);

      return { totalAmount, commission: new_commision };
    }

    // Si no hay comisión, devuelvo monto original y cero de comisión
    return { totalAmount: Number(params.amount), commission: '0.00' };
  }

  private async checkCommissionsSpei(
    params: ParamsCheckCommissionDto,
  ): Promise<{ totalAmount: number; commission: string }> {
    /* ** Consultamos la comisión del usuario ** */
    const user = await this.user.findCommisionsOfSpei(params.email);

    // Comisión fija (si el usuario tiene spei_out configurado > 0)
    if (user && user.spei_out > 0) {
      const speiOutCommission = user.spei_out;
      const transferAmount = Number(params.amount);
      const totalAmount = transferAmount + speiOutCommission;

      return { totalAmount, commission: speiOutCommission.toFixed(2) };
    }

    // Si no hay comisión, devuelvo monto original y cero de comisión
    return { totalAmount: Number(params.amount), commission: '0.00' };
  }

  async executePayBulk(
    dto: DockPayBulkTransferstDto,
    user: Users,
  ): Promise<BulkTransfers> {
    const data: ParamsDockPayTransfertDto[] = dto.data;

    const total = data.length;

    if (total === 0) {
      throw new BadRequestException('No hay datos para procesar');
    }

    const bulk = new BulkTransfers();
    bulk.total = total;
    bulk.status = BulkTransfersStatus.PENDING;
    bulk.successCount = 0;
    bulk.failureCount = 0;
    bulk.creationUser = user;

    if (dto.file) {
      await this.s3.moveFileToPermanentLocation(
        dto.file,
        'users/'.concat(user.id),
      );

      const path = 'users/'.concat(user.id).concat('/').concat(dto.file);
      const s3Metada = new S3Metadata();
      s3Metada.key = path;

      const s3File = await s3Metada.save();

      bulk.file = s3File;
    }

    await bulk.save();

    console.log('Bulk transfer created:', bulk.id);
    try {
      const job = await this.bulkTransfersQueue.add(
        'process',
        {
          bulkId: bulk.id,
          transfers: data,
        },
        {
          priority: 1,
          delay: 1000,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
          removeOnComplete: 5,
          removeOnFail: 3,
        },
      );

      console.log(
        'Bulk transfer added to queue successfully:',
        bulk.id,
        'Job ID:',
        job.id,
      );
    } catch (error) {
      console.error('Error adding job to queue:', error);
      throw error;
    }

    return bulk;
  }

  async getBulkTransferStatus(id: string) {
    const bulk = await BulkTransfers.findOne({
      where: { id },
      relations: ['file'],
    });

    if (!bulk) {
      throw new BadRequestException('Bulk transfer not found');
    }

    const { file, ...data } = bulk;

    const response = {
      ...data,
      file: file
        ? {
            id: file.id,
            key: file.key,
            url: await this.s3.generatePresignedUrl(file?.key),
          }
        : null,
    };

    return response;
  }

  async getBulkTransfersfailed(id: string): Promise<BulkTransfersError[]> {
    const errors = await BulkTransfersError.find({
      where: {
        bulkTransfer: { id },
      },
    });

    return errors;
  }

  async getBulkTransfersByUserId(id: string, pagination: PaginationDto) {
    const user = await this.user.findUserById(id);

    if (!user) {
      throw new BadRequestException('User not found');
    }

    const bulkTransfers = await BulkTransfers.createQueryBuilder('bulk')
      .where('bulk.creationUserId = :userId', { userId: user.id })
      .orderBy('bulk.createdAt', 'DESC')
      .skip(pagination.page * pagination.limit)
      .take(pagination.limit)
      .getManyAndCount();

    return {
      data: bulkTransfers[0],
      count: bulkTransfers[1],
    };
  }

  private normalizeConcept(input: string): string {
    if (!input) return '';
    return input
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-zA-Z\s]/g, '');
  }

  private normalizeBeneficiaryName(input: string): string {
    if (!input) return '';
    return input
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-zA-Z\s]/g, '');
  }
}
