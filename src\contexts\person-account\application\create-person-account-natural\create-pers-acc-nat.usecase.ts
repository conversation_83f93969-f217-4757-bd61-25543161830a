import { Injectable } from '@nestjs/common';
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import * as https from 'https';

/* ** DTOs ** */
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import {
  CreateNatPersAccDto,
  NaturalPersonDataDto,
  AccountDataDto,
} from './create-pers-acc-nat.dto';

/* ** Utils ** */
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';

@Injectable()
export class CreateNatPersAccUseCase {
  constructor(
    private readonly configService: ConfigService,
    private readonly authTokenDock: authTokenDock,
  ) {}

  async executeNatPersAcc(nat: CreateNatPersAccDto): Promise<ApiResponseDto> {
    /* ** Formamos el input para el servicio de creación de persona natural ** */
    const person = await this.formatDataNaturalPerson(nat);

    /* ** Obtenemos las credenciales de autenticación ** */
    const auth = await this.authTokenDock.getAuthDock();

    // Configurar el agente HTTPS con los certificados
    const httpsAgent = new https.Agent({
      cert: auth.certificate,
      key: auth.key,
      rejectUnauthorized: false,
    });

    /* ** Creamos la persona natural ** */
    const { data: nat_person } = await axios.post(
      `${this.configService.get('DOCK_URL_GLOBAL')}/person/v1/natural-persons`,
      person,
      {
        headers: {
          Authorization: `Bearer ${auth.bearer_token}`,
          'Content-Type': 'application/json',
        },
        httpsAgent,
      },
    );

    /* ** Creamos la cuenta de la persona natural ** */
    const accoun_data: AccountDataDto = {
      product_id: this.configService.get('DOCK_PRODUCT_ID'),
      person_id: nat_person.person_id,
    };

    const { data: acc_person } = await axios.post(
      `${this.configService.get('DOCK_URL_GLOBAL')}/account-services/management/v1/accounts`,
      accoun_data,
      {
        headers: {
          Authorization: `Bearer ${auth.bearer_token}`,
          'Content-Type': 'application/json',
        },
        httpsAgent,
      },
    );

    console.log('nat_person', acc_person);

    return ResponseUtil.success(
      'Create Person Account Natural successfully',
      { Person: nat_person, Account: acc_person },
      200,
    );
  }

  private async formatDataNaturalPerson(
    person: CreateNatPersAccDto,
  ): Promise<NaturalPersonDataDto> {
    return {
      status_id: 1,
      person_full_name: person.person_name,
      preferred_name: person.person_name.split(' ')[0] ?? 'Nombre',
      documents: [
        {
          type_id: 90,
          number: person.voter_id,
          is_main: true,
          country_code: 'MX',
        },
      ],
      phones: [
        {
          type_id: 58,
          is_main: true,
          dialing_code: person.dialing_code,
          area_code: person.area_code,
          number: person.number,
          country_code: person.country_code,
        },
      ],
      addresses: [
        {
          type_id: 63,
          is_main: true,
          postal_code: '37600',
          suffix: 'GTO',
          street: 'Aguamarina',
          number: '311',
          city: 'San Felipe',
          country_code: 'MX',
        },
      ],
      emails: [
        {
          type_id: 71,
          is_main: true,
          email: person.email,
        },
      ],
    };
  }
}
