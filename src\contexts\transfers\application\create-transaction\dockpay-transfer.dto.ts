import {
  IsNotEmpty,
  <PERSON><PERSON><PERSON>ber,
  IsOptional,
  IsString,
  ValidateNested,
  IsEnum,
} from 'class-validator';

import { Type } from 'class-transformer';

/* ** Enums ** */
import { PaymentEnum } from 'src/contexts/shared/enums/typePayment.enum';
import { ApiProperty } from '@nestjs/swagger';

export class DockpayTransfertDto {
  @IsString()
  num_clabe: string;

  @IsString()
  email: string;

  @IsNumber()
  amount: number;

  @IsString()
  description: string;
}

export class ParamsDockPayTransfertDto {
  @IsString()
  num_clabe: string;

  @IsString()
  email: string;

  @IsNumber()
  amount: number;

  @IsString()
  @IsNotEmpty()
  description: string;

  @IsString()
  @IsOptional()
  beneficiaryName?: string;

  @IsString()
  @IsOptional()
  legalBank?: string;

  @IsString()
  @IsOptional()
  NameBank?: string;
}

export class PayloadDockPayDto {
  @ValidateNested({ each: true })
  @Type(() => ParamsDockPayTransfertDto)
  data: ParamsDockPayTransfertDto;

  @IsString()
  @IsNotEmpty()
  debtorId: string;

  @IsString()
  @IsNotEmpty()
  creditorId: string;

  @IsOptional()
  @IsString()
  external_transaction_id?: string;
}

export class createPaymentDto {
  @IsNotEmpty()
  @IsEnum(PaymentEnum)
  payment_type: PaymentEnum;

  @IsString()
  bank: string;

  @IsString()
  beneficiary_name: string;

  @IsString()
  beneficiary_account: string;

  @IsString()
  player_account: string;

  @IsNumber()
  reference: number;

  @IsString()
  email: string;

  @IsString()
  player_account_id: string;

  @IsNumber()
  amount: number;

  @IsString()
  bank_name: string;

  @IsString()
  concept: string;
}

export class ResponseCreatePaymentDto {
  @IsNotEmpty()
  @IsString()
  id: string;
}

export class ResponseFindPaymentDto {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsNotEmpty()
  @IsEnum(PaymentEnum)
  payment_type: PaymentEnum;

  @IsString()
  @IsOptional()
  bank?: string;

  @IsString()
  @IsOptional()
  beneficiary_name?: string;

  @IsString()
  @IsOptional()
  beneficiary_account?: string;

  @IsString()
  @IsOptional()
  reference: number;

  @IsString()
  @IsOptional()
  email: string;

  @IsString()
  player_account_id: string;

  @IsNumber()
  @IsOptional()
  amount?: number;

  @IsOptional()
  @IsString()
  order_id?: string;

  @IsOptional()
  @IsString()
  reference_dock_id?: string;

  @IsOptional()
  @IsString()
  player_account?: string;
}

export class ParamsBalanceToAccount {
  @IsNotEmpty()
  @IsString()
  clabe: string;
}

export class ResponseFindPaymentStateDto {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsNotEmpty()
  @IsEnum(PaymentEnum)
  payment_type: PaymentEnum;

  @IsOptional()
  @IsString()
  status_dock: string | null;

  @IsOptional()
  @IsString()
  status_transfer: string | null;

  @IsOptional()
  @IsString()
  cep?: string | null;

  @IsOptional()
  @IsString()
  commission?: string | null;
}

export class ParamsValidatePaymentDto {
  @IsNotEmpty()
  @IsEnum(PaymentEnum)
  type: PaymentEnum;

  @IsOptional()
  @IsString()
  state_dock: string | null;

  @IsOptional()
  @IsString()
  state_transfer: string | null;
}

class AccountDto {
  @IsNotEmpty()
  @IsString()
  key: string;

  @IsNotEmpty()
  @IsString()
  key_type: string;

  @IsNotEmpty()
  @IsString()
  balance_category: string;
}

export class TransferObjectDto {
  @ValidateNested()
  @Type(() => AccountDto)
  debtor: AccountDto;

  @ValidateNested()
  @Type(() => AccountDto)
  creditor: AccountDto;

  @IsNotEmpty()
  @IsNumber()
  amount: number;

  @IsNotEmpty()
  @IsString()
  operation_type: string;

  @IsNotEmpty()
  @IsString()
  description: string;

  @IsNotEmpty()
  @IsString()
  external_transaction_id: string;
}

export class ResponsePaymentDto {
  @IsOptional()
  @IsString()
  id?: string;

  @IsNumber()
  status_code: number;

  @IsString()
  message: string;

  @IsOptional()
  @IsString()
  transaction_id?: string;

  @IsOptional()
  @IsString()
  reference?: string;

  @IsOptional()
  @IsNumber()
  commission?: string;
}

export class ParamsBalanceToAccountDto {
  @IsString()
  person_id: string;

  @IsString()
  account_id: string;
}

export class ResponsePaymentDockDto {
  @IsOptional()
  @IsString()
  id?: string;

  @IsNumber()
  status_code: number;
}

export class ParamsCheckCommissionDto {
  @IsString()
  email: string;

  @IsNumber()
  amount: number;

  @IsString()
  type: 'TRANSFER_ORDERS' | 'DOCK_PAY';

  @IsString()
  creditor_dock_id: string;
}

export class DockPayBulkTransferstDto {
  @ApiProperty({ type: [ParamsDockPayTransfertDto] })
  @ValidateNested({ each: true })
  data: ParamsDockPayTransfertDto[];

  @IsString()
  file: string;
}
