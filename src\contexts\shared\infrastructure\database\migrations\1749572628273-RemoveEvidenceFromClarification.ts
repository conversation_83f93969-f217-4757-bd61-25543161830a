import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveEvidenceFromClarification1749572628273
  implements MigrationInterface
{
  name = 'RemoveEvidenceFromClarification1749572628273';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "clarification" DROP COLUMN "evidence"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "clarification" ADD "evidence" text`);
  }
}
