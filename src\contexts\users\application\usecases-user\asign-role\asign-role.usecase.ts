import { ConflictException, Injectable, NotFoundException } from "@nestjs/common";
import { AssignRoleDto } from "./asign-role.dto";
import { RoleRepositoryImpl } from "src/contexts/users/infrastructure/repositories/role.repository.impl";
import { AdminRepositoryImpl } from "src/contexts/users/infrastructure/repositories/adminrepository.impl";
import { UsersRepositoryImpl } from "src/contexts/users/infrastructure/repositories/users.repository.impl";
import { RoleTypeEnum } from "src/contexts/users/domain/entities/rol.entity";
import { RelUserRoleAdmin } from "src/contexts/users/domain/entities/rel-user-role-admin.entity";
import { Users } from "src/contexts/users/domain/entities/users.entity";

@Injectable()
export class AssignRoleUseCase {

    constructor(
        private readonly roleRepository: RoleRepositoryImpl,
        private readonly adminRespository: AdminRepositoryImpl,
        private readonly userRepository: UsersRepositoryImpl
    ){}

    async execute(dto: AssignRoleDto): Promise<void> {
     
        const user = await this.userRepository.findUserDetails(dto.userId);

        if (!user) {
            throw new NotFoundException('User not found');
        }

        if(user.relUserRoleAdmins.some( rel => rel.admin.id === dto.adminId))
            throw new ConflictException('Este usuario ya se encuentra registrado en esta empresa.');

        const role = await this.roleRepository.findById(dto.roleId);

        if (!role) {
            throw new NotFoundException('Role not found');
        }

        if(role.type !== RoleTypeEnum.CLIENT)
            throw new ConflictException('Only CLIENT role can be assigned to users');

        const admin = await this.adminRespository.findById(dto.adminId);

        if (!admin) {
            throw new NotFoundException('Admin not found');
        }

        const rel = new RelUserRoleAdmin();
        rel.user = user;
        rel.role = role;
        rel.admin = admin;

        await rel.save();

    }
}