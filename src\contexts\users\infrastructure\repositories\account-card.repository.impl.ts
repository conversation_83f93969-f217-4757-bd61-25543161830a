import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

/* * * DTOs ** */
import {
  CreateAccountCardDto,
  ResponseCreateAccountCardDto,
} from 'src/contexts/card-assignment/application/single-card-assignment/single-card-assignment.dto';

/* ** Enum */
import { CardType } from 'src/contexts/shared/interfaces/dtos/cards.enum.dto';

import { ParamsDBChangeCardStatus } from 'src/contexts/dock-cards/apps/control-dock-card/control-dock-card-dto';

/* ** Entities ** */
import { AccountCards } from '../../domain/entities/account-cards.entity';

/* ** Repositories ** */
import { AccountCardRepository } from '../../domain/repository/account-card.repository';

@Injectable()
export class AccountCardRepositoryImpl implements AccountCardRepository {
  constructor(
    @InjectRepository(AccountCards)
    private readonly aliasCoreRepository: Repository<AccountCards>,
  ) {}

  async saveAccountCard(
    entity: CreateAccountCardDto,
  ): Promise<ResponseCreateAccountCardDto> {
    const res = await this.aliasCoreRepository.save(entity);
    return res;
  }

  async findCardByID(card_id: string): Promise<boolean> {
    const card = await this.aliasCoreRepository.findOneBy({
      card_dock_id: card_id,
    });

    return card && true;
  }

  async findCardType(card_dock_id: string): Promise<string> {
    const card = await this.aliasCoreRepository.findOneBy({
      card_dock_id: card_dock_id,
    });

    return card?.card_type || '';
  }

  async updateCardStatus(card: ParamsDBChangeCardStatus): Promise<boolean> {
    const res = await this.aliasCoreRepository.update(
      { card_dock_id: card.card_dock_id },
      {
        enabled: card.status,
      },
    );

    return res && true;
  }

  async findCardWithAccount(
    card_dock_id: string,
  ): Promise<AccountCards | null> {
    return await this.aliasCoreRepository.findOne({
      where: { card_dock_id },
      relations: ['account'],
    });
  }

  async findCardVirtualByAccountId(
    account_id: string,
  ): Promise<AccountCards[]> {
    return await this.aliasCoreRepository.find({
      where: {
        account_id,
        card_type: CardType.VIRTUAL,
      },
    });
  }

  async countCardsByAdminId(adminId: string): Promise<number> {
    return await this.aliasCoreRepository.count({
      where: {
        account: {
          personIDDock: {
            users: {
              admin_data: {
                id: adminId,
              },
            },
          },
        },
        enabled: true,
      },
      relations: ['account', 'account.personIDDock.users.admin_data'],
    });
  }
}
