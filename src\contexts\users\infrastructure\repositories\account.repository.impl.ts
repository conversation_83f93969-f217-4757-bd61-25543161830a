import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';
import { AccountRepository } from '../../domain/repository/account.repository';
import { Account } from '../../domain/entities/account.entity';
import { CreateAccountDto } from '../../application/usecases-account/create-account/create-account.dto';
import { ResAccountDto } from 'src/contexts/card-assignment/application/single-card-assignment/single-card-assignment.dto';

export class AccountRepositoryImpl implements AccountRepository {
  constructor(
    @InjectRepository(Account)
    private readonly accountRepository: Repository<Account>,
  ) {}

  async save(account: CreateAccountDto): Promise<ApiResponseDto> {
    try {
      const addressEntity = this.accountRepository.create({
        ...account,
        personIDDock: account.personIDDock
          ? { id: account.personIDDock }
          : undefined,
      });
      const res = await this.accountRepository.save(addressEntity);
      return ResponseUtil.success('Cuenta creada exitosamente', res, 201);
    } catch (error) {
      return ResponseUtil.error('No se pudo crear la cuenta', error, 400);
    }
  }

  async findAll(): Promise<ApiResponseDto> {
    try {
      const res = await this.accountRepository.find();
      return ResponseUtil.success('Cuentas obtenidas exitosamente', res, 200);
    } catch (error) {
      return ResponseUtil.error(
        'No se pudieron obtener las cuentas',
        error,
        400,
      );
    }
  }

  async findById(id: string): Promise<ApiResponseDto> {
    try {
      const res = await this.accountRepository.findOne({
        where: { accountExtID: id },
      });
      if (!res) {
        return ResponseUtil.error(
          'Cuenta no encontrada',
          'Cuenta con el ID proporcionado no existe',
          404,
        );
      }
      return ResponseUtil.success('Cuenta obtenida exitosamente', res, 200);
    } catch (error) {
      return ResponseUtil.error('No se pudo obtener la cuenta', error, 400);
    }
  }

  async update(id: string, account: CreateAccountDto): Promise<ApiResponseDto> {
    try {
      const res = await this.accountRepository.update(
        { accountExtID: id },
        {
          ...account,
          personIDDock: account.personIDDock
            ? { id: account.personIDDock }
            : undefined,
        },
      );
      return ResponseUtil.success('Cuenta actualizada exitosamente', res, 200);
    } catch (error) {
      return ResponseUtil.error('No se pudo actualizar la cuenta', error, 400);
    }
  }

  async remove(id: string): Promise<ApiResponseDto> {
    try {
      const res = await this.accountRepository.delete(id);
      return ResponseUtil.success('Cuenta eliminada exitosamente', res, 200);
    } catch (error) {
      return ResponseUtil.error('No se pudo eliminar la cuenta', error, 400);
    }
  }

  async getAccountIdByPersonId(person_id: string): Promise<ResAccountDto> {
    const account = await this.accountRepository
      .createQueryBuilder('account')
      .leftJoinAndSelect('account.personIDDock', 'person')
      .where('account.personIDDock = :person_id', { person_id })
      .getOne();

    return {
      id: account?.id || '',
      account_dock_id: account?.accountExtID || '',
    };
  }

  async findByPersonId(personId: string) {
    return await this.accountRepository.findOne({
      where: { 
        personIDDock : {
          id: personId
        }
       }
    })
  }
}
