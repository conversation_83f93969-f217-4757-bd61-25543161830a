import { ApiResponseDto } from "src/contexts/shared/interfaces/dtos/api-response.dto";
import { CreateAdminDto } from "../../application/usecases-admin/create-admin/create-admin.dto";
import { UpdateAdminDto } from "../../application/usecases-admin/update-admin/update-admin.dto";
import { Admin } from "../entities/admin.entity";
import { PaginationDto } from "src/contexts/shared/dto/pagination.dto";

export interface AdminRepository {
  save(entity: CreateAdminDto): Promise<Admin>;
  findAll(dto: PaginationDto): Promise<[Admin[], number]>;
  findById(id: string): Promise<Admin | null>;
  update(id: string, entity: UpdateAdminDto): Promise<void>;
  remove(id: string): Promise<ApiResponseDto>;
  findByRFC(rfc: string): Promise<Admin | null >;
  findByMembership(membership: number): Promise<Admin | null>;
}