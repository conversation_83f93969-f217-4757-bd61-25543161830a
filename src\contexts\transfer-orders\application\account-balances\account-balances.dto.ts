import {
  IsString,
  <PERSON><PERSON><PERSON>ber,
  IsNotEmpty,
  ValidateNested,
} from 'class-validator';

import { Type } from 'class-transformer';

class AccountBalancesDataDto {
  @IsString()
  @IsNotEmpty()
  account: string;

  @IsNumber()
  balance: number;

  @IsNumber()
  transitBalance: number;
}

export class ParamsAccountBalancesDto {
  @IsString()
  @IsNotEmpty()
  account: string;
}

export class ResponseAccountBalancesDto {
  @IsNumber()
  code: number;

  @ValidateNested({ each: true })
  @Type(() => AccountBalancesDataDto)
  data?: AccountBalancesDataDto;
}
