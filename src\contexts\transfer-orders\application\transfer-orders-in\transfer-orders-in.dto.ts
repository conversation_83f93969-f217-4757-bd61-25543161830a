import {
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  IsISO8601,
  ValidateNested,
  IsOptional,
  IsBoolean,
  IsDecimal,
} from 'class-validator';
import { Type } from 'class-transformer';

class DataDto {
  @IsOptional()
  @IsString()
  beneficiaryBank?: string;

  @IsOptional()
  @IsString()
  payerBank?: string;

  @IsOptional()
  @IsNumber()
  amount?: number;

  @IsOptional()
  @IsString()
  payerUid?: string;

  @IsOptional()
  @IsNumber()
  beneficiaryAccountType?: number;

  @IsOptional()
  @IsString()
  concept?: string;

  @IsOptional()
  @IsString()
  sign?: string;

  @IsOptional()
  @IsString()
  payerAccount?: string;

  @IsOptional()
  @IsNumber()
  numericalReference?: number;

  @IsOptional()
  @IsNumber()
  payerAccountType?: number;

  @IsOptional()
  @IsString()
  trackingKey?: string;

  @IsOptional()
  @IsString()
  beneficiaryName?: string;

  @IsOptional()
  @IsString()
  beneficiaryAccount?: string;

  @IsOptional()
  @IsString()
  beneficiaryUid?: string;

  @IsOptional()
  @IsString()
  payerName?: string;

  @IsOptional()
  @IsISO8601()
  operationDate?: string; // ISO date string

  @IsOptional()
  @IsNumber()
  receivedTimestamp?: number; // Unix timestamp
}

export class ParamsTransferOrderInDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => DataDto)
  data?: DataDto;

  @IsOptional()
  @IsString()
  type?: string;
}

export class ResTransferOrderInDto {
  @IsNumber()
  returnCode: number;
}

export class ResponseFindUserDto {
  @IsString()
  email: string;

  @IsBoolean()
  user_enabled: boolean;

  @IsString()
  person_dock_id: string;

  @IsBoolean()
  person_enabled: boolean;

  @IsString()
  clabe: string;

  @IsBoolean()
  transfer_enabled: boolean;

  @IsNumber()
  amount: number;

  @IsBoolean()
  spei_in: boolean;

  @IsBoolean()
  spei_out: boolean;

  @IsString()
  accoun_dock_id: string;

  @IsString()
  name: string;
}

export class ParamsTransferDto {
  @IsString()
  creditor_key: string;

  @IsString()
  debtor_key: string;

  @IsNumber()
  amount: number;

  @IsString()
  concept: string;
}

export class ResponseFindUserByCardIDDto {
  @IsString()
  email: string;

  @IsBoolean()
  user_enabled: boolean;

  @IsBoolean()
  spei_in: boolean;

  @IsBoolean()
  spei_out: boolean;

  @IsString()
  accoun_dock_id: string;

  @IsString()
  person_dock_id: string;
}

export class ResponseTransferOrderInDto {
  @IsString()
  event_status: number;

  @IsString()
  operation_instance_id: string;
}
export class ParamsPayCommissionDto {
  @IsString()
  debtor_key: string;

  @IsString()
  creditor_key: string;

  @IsNumber()
  commission: number;

  @IsString()
  type: 'SPEI_IN' | 'SPEI_OUT' | 'P2P_IN' | 'P2P_OUT';

  @IsString()
  operation_instance_id: string;

  @IsString()
  amount: string;

  @IsString()
  embajador_account: string;

  @IsString()
  player_account: string;
}

export class SignatureDto {
  @IsString()
  beneficiaryName: string;

  @IsString()
  beneficiaryUid: string;

  @IsString()
  beneficiaryBank: string;

  @IsString()
  beneficiaryAccount: string;

  @IsNumber()
  beneficiaryAccountType: number;

  @IsString()
  payerAccount: string;

  @IsNumber()
  numericalReference: number;

  @IsString()
  payerName: string;

  @IsString()
  payerUid: string;

  @IsString()
  payerBank: string;

  @IsNumber()
  payerAccountType: number;

  @IsString()
  trackingKey: string;

  @IsString()
  concept: string;

  @IsDecimal({ decimal_digits: '2', force_decimal: true })
  amount: string;
}
