import {
  Entity as ORMA<PERSON>unt<PERSON><PERSON>s<PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

/* ** Entidades** */
import { Person } from './person.entity';
import { DepositLimits } from './deposit-limits.entity';

@ORMAccountTransfer()
export class AccountTransfer {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ default: true })
  enabled: boolean;

  @Column({ unique: true })
  clabe: string;

  @ManyToOne(() => Person, (person) => person.transfers)
  @JoinColumn({ name: 'person_id' })
  person: Person;

  @Column({ nullable: false })
  person_id: string;

  @ManyToOne(() => DepositLimits, (limit) => limit.limits)
  @JoinColumn({ name: 'limit_id' })
  limit: DepositLimits;

  @Column({ nullable: true })
  limit_id: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
