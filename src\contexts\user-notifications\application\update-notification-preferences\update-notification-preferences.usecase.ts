import { Injectable, Inject } from '@nestjs/common';
import { NotificationPreferencesRepository } from '../../domain/repository/notification-preferences.repository';
import { UpdateNotificationPreferencesDto } from './update-notification-preferences.dto';

@Injectable()
export class UpdateNotificationPreferencesUseCase {
  constructor(
    @Inject('NotificationPreferencesRepository') // Inyecta el alias registrado en el módulo
    private readonly preferencesRepository: NotificationPreferencesRepository,
  ) {}

  async execute(
    userId: string,
    preferences: UpdateNotificationPreferencesDto,
  ): Promise<void> {
    await this.preferencesRepository.createOrUpdate(userId, preferences);
  }
}