// src/contexts/clarification/application/usecases/update-clarification.usecase.ts

import { Injectable } from '@nestjs/common';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { ClarificationRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/clarification.repository.impl';
import { UpdateClarificationDto } from './update-clarification.dto';

@Injectable()
export class UpdateClarificationUseCase {
  constructor(private readonly clarificationRepositoryImpl: ClarificationRepositoryImpl) {}

  async execute(trackingNumber: string, dto: UpdateClarificationDto): Promise<ApiResponseDto> {
    return this.clarificationRepositoryImpl.update(trackingNumber, dto);
  }
}
