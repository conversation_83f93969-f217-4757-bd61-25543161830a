import { GenerateEntityDto } from '../../app/generate-otp/generate-otp.dto';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { VerifyEntityDto } from '../../app/verify-otp/verify-otp.dto';
import { findEntityOtpDto } from '../../app/verify-otp/verify-otp.dto';

export interface OtpRepository {
  save(params: GenerateEntityDto): Promise<ApiResponseDto>;
  updateToken(id: string, token: string): Promise<ApiResponseDto>;
  findOneByID(id_otp: string): Promise<findEntityOtpDto>;
  deleteOTP(params: VerifyEntityDto): Promise<boolean>;
}
