import { Body, Controller, Post, HttpStatus } from '@nestjs/common';
import { GenerateEntityDto } from '../../../otp/app/generate-otp/generate-otp.dto';
import { GenerateOtpUseCase } from '../../app/generate-otp/generate-otp.usecase';

import { ResponseUtil } from 'src/contexts/shared/utils/response.util';

@Controller('reset-password')
export class OtpController {
  constructor(private readonly generateOtpUseCase: GenerateOtpUseCase) {}

  @Post('generate-otp')
  async createCodeOtp(@Body() params: GenerateEntityDto) {
    try {
      return await this.generateOtpUseCase.createOtpEntry(params);
    } catch (e) {
      return ResponseUtil.error(
        e.message,
        {
          data: {
            statusCode: e.status,
            message: e.message,
            error: e.name,
          },
        },
        e.status,
      );
    }
  }
}
