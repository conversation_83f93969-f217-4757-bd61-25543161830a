import {
  IsString,
  IsNotEmpty,
  IsBoolean,
  IsDateString,
  ValidateNested,
  IsObject,
  IsOptional,
  IsEnum,
  IsInt,
} from 'class-validator';
import { Type } from 'class-transformer';

import {
  CardType,
  ActiveFunction,
} from 'src/contexts/shared/interfaces/dtos/cards.enum.dto';

class TransactionSettings {
  @IsBoolean()
  ecommerce: boolean;

  @IsBoolean()
  international: boolean;

  @IsBoolean()
  stripe: boolean;

  @IsBoolean()
  wallet: boolean;

  @IsBoolean()
  withdrawal: boolean;

  @IsBoolean()
  contactless: boolean;
}

class SecuritySettings {
  @IsBoolean()
  pin_offline: boolean;
}

class Settings {
  @ValidateNested()
  @Type(() => TransactionSettings)
  transaction: TransactionSettings;

  @ValidateNested()
  @Type(() => SecuritySettings)
  security: SecuritySettings;
}

class Address {
  @IsString()
  @IsNotEmpty()
  street: string;

  @IsString()
  @IsNotEmpty()
  number: string;

  @IsString()
  @IsNotEmpty()
  city: string;

  @IsString()
  @IsNotEmpty()
  administrative_area_code: string;

  @IsString()
  @IsNotEmpty()
  country_code: string;

  @IsString()
  @IsNotEmpty()
  postal_code: string;

  @IsString()
  @IsNotEmpty()
  neighborhood: string;

  @IsString()
  complement: string;
}

export class CreateDockCardPhysicalDto {
  @IsString()
  @IsOptional()
  profile_id: string;

  @IsString()
  @IsOptional()
  embossing_setup_id: string;

  @IsEnum(CardType)
  @IsNotEmpty()
  type: CardType;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  cardholder_name: string;

  @IsEnum(ActiveFunction)
  @IsNotEmpty()
  active_function: ActiveFunction;

  @IsDateString()
  @IsNotEmpty()
  @IsOptional()
  expiration_date: string;

  @IsString()
  @IsNotEmpty()
  pin: string;

  @ValidateNested()
  @IsOptional()
  @Type(() => Settings)
  settings: Settings;

  @ValidateNested()
  @IsOptional()
  @Type(() => Address)
  address: Address;

  @IsObject()
  @IsOptional()
  metadata: object;
}

export class CreateDockCardVirtualDto {
  @IsString()
  @IsOptional()
  profile_id: string;

  @IsEnum(CardType)
  @IsNotEmpty()
  type: CardType;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  cardholder_name: string;

  @IsEnum(ActiveFunction)
  @IsNotEmpty()
  active_function: ActiveFunction;

  @IsDateString()
  @IsNotEmpty()
  @IsOptional()
  expiration_date: string;

  @IsString()
  @IsNotEmpty()
  pin: string;

  @ValidateNested()
  @IsOptional()
  @Type(() => Settings)
  settings: Settings;

  @ValidateNested()
  @IsOptional()
  @Type(() => Address)
  address: Address;

  @IsObject()
  @IsOptional()
  metadata: object;
}

export class CreateDockCardBatchDto {
  @IsInt()
  @IsNotEmpty()
  quantity: number;

  @IsString()
  @IsOptional()
  profile_id: string;

  @IsString()
  @IsOptional()
  embossing_setup_id: string;

  @IsEnum(CardType)
  @IsNotEmpty()
  type: CardType;

  @IsEnum(ActiveFunction)
  @IsNotEmpty()
  active_function: ActiveFunction;

  @IsDateString()
  @IsNotEmpty()
  @IsOptional()
  expiration_date: string;

  @ValidateNested()
  @IsOptional()
  @Type(() => Settings)
  settings: Settings;

  @ValidateNested()
  @IsOptional()
  @Type(() => Address)
  address: Address;

  @IsObject()
  @IsOptional()
  metadata: object;
}
