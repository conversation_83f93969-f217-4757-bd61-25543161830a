import { Injectable, NotFoundException } from '@nestjs/common';
import { GetUserAccountService } from '../get-user-account/get-user-account.service';
import { DockAccountDetailsService } from '../../../shared/utils/accountDetailsDock/getAccountDetailsDockService';

@Injectable()
export class GetAccountDetailsUseCase {
  constructor(
    private readonly getUserAccountService: GetUserAccountService,
    private readonly dockAccountDetailsService: DockAccountDetailsService,
  ) {}

  async execute(email: string): Promise<any> {
    // Obtener account_id y person_id a partir del email
    const resp =
      await this.getUserAccountService.getAccountAndPersonByEmail(email);

    if(!resp) {
      throw new NotFoundException('Account not found');
    }

    const { account_id, person_id, clabe } = resp;

    if(!account_id || !person_id ) {
      throw new NotFoundException('Account not found');
    }
    
    // Consumir la API de Dock para obtener los detalles de la cuenta
    const accountDetails =
      await this.dockAccountDetailsService.getAccountDetails(
        account_id,
        person_id,
      );
    let availableResource =
      accountDetails.content[0].sub_account_instances[0]
        .balance_category_instances[0].balance_type_instances[0]
        .available_resource;
    return {
      account_id,
      available_resource: availableResource,
      clabe,
    };
  }
}
