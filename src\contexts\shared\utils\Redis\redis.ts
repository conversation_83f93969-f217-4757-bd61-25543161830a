import { Injectable } from '@nestjs/common';
import { InjectRedis } from '@nestjs-modules/ioredis';
import { Redis } from 'ioredis';
import {
  RedisDto,
  RedisErrorDto,
  ExpiresIn,
} from '../../interfaces/dtos/redis.dto';

@Injectable()
export class RedisService {
  constructor(@InjectRedis() private readonly redisClient: Redis) {}

  async setCache(params: RedisDto): Promise<void> {
    await this.redisClient.set(
      params.key,
      params.value,
      'EX',
      params.expiresIn,
    );
  }

  async getCache(key: string): Promise<string | null> {
    return await this.redisClient.get(key);
  }

  async logError(params: RedisErrorDto): Promise<void> {
    // Generate a unique identifier for the error log entry
    const errorId = params.key
      ? `${params.key}:${new Date().getTime()}`
      : `api:log:${new Date().getTime()}`;

    const logEntry = {
      controller: params.nameController,
      message: params.name,
      error: params.error,
      timestamp: new Date().toISOString(),
      statusCode: params.statusCode,
    };

    await this.redisClient.lpush(errorId, JSON.stringify(logEntry));
    await this.redisClient.expire(errorId, ExpiresIn.ONE_HOUR);
  }
}
