import { Injectable } from '@nestjs/common';
import Decimal from 'decimal.js-light';
import { AdminTotalAmount } from 'src/contexts/users/infrastructure/vo/admin.vo';
import { ReadAdminTotalAmountDto } from './read-total-amount.dto';
import { DockLegalPersonService } from 'src/contexts/dock/infraestructure/services/dock-legal-person.service';
import { UsersRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/users.repository.impl';
import { Admin } from 'typeorm';
import { AdminRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/adminrepository.impl';

@Injectable()
export class ReadAminTotalAmountUseCase {
  constructor(
    readonly dockLegalPersonService: DockLegalPersonService,
    private readonly userRepository: UsersRepositoryImpl,
    private readonly adminRepository: AdminRepositoryImpl
  ) {}

  async execute(dto: ReadAdminTotalAmountDto) : Promise<AdminTotalAmount> {

      let total = new Decimal(0);
      const admins = await this.adminRepository.findManagerAccount(dto);

      for(const admin of admins){

        const account = admin.manager.personIDDock.accounts[0];
        const dockAccountDetail = await this.dockLegalPersonService.getAccountDetail(account.accountExtID, admin.manager.personIDDock.personExtID).catch(() => null);

        if(!dockAccountDetail || !dockAccountDetail.content.length)
          return { totalAmount: '0' };

        const amount = dockAccountDetail.content[0].sub_account_instances[0]
          .balance_category_instances[0].balance_type_instances[0]
          .available_resource;

        total = total.plus(amount);
      }

      global.gc && global.gc();

      return { totalAmount: total.toString() };
    }

}
