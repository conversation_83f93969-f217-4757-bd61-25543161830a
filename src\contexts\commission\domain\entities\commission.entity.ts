import {
  Column,
  Entity as <PERSON>m<PERSON>om<PERSON>,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

/* ** Enum */
import { TypeCommissionEnum } from 'src/contexts/shared/enums/commission.enum';

@OrmCommission()
export class Commissions {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: TypeCommissionEnum,
    default: TypeCommissionEnum.OUT,
    nullable: false,
  })
  type_commission: string;

  @Column()
  player_account: string;

  @Column()
  embajador_account: string;

  @Column()
  amount: string;

  @Column()
  status: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
