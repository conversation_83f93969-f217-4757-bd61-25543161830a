import {
  ConflictException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { CreateAdminDto } from './create-admin.dto';
import { UsersRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/users.repository.impl';
import { AdminRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/adminrepository.impl';
import { RelUserRoleAdminRepositoryImpl } from '../../../infrastructure/repositories/rel-user-role-admin.repository.impl';
import { ReadUserUseCase } from '../../usecases-user/read-user/read-user.usecase';
import { ConfigService } from '@nestjs/config';
import { MailerService } from '@nestjs-modules/mailer';
import { CreateAddressUseCase } from '../../usecases-address/create-address/create-address-usecase';
import { RoleRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/role.repository.impl';
import { RoleEnum } from 'src/contexts/shared/enums/roles.enum';
import { CreateLegPersAccUseCase } from 'src/contexts/person-account/application/create-person-account-legal/create-pers-acc-leg.usecase';
import { CreatePersonUseCase } from '../../usecases-person/create-person/create-person-usecase';
import { CreateLegPersAccDto } from 'src/contexts/person-account/application/create-person-account-legal/create-pers-acc-leg.dto';
import { CreateUserUseCase } from '../../usecases-user/create-user/create-user.usecase';
import { CommonUtil } from 'src/contexts/shared/utils/common.util';
import { Admin } from 'src/contexts/users/domain/entities/admin.entity';
import { StorageS3Utils } from 'src/contexts/shared/utils/storageUtils/storageS3.utils';

import * as Handlebars from 'handlebars';
import { Users } from 'src/contexts/users/domain/entities/users.entity';
import { TransferAccount } from 'src/contexts/shared/utils/transfer-account/transfer-account.utils';
import { S3Metadata } from 'src/contexts/users/domain/entities/s3-metadata.entity';
import { AdminDocuments } from 'src/contexts/users/domain/entities/admin-documents.entity';
import { ConveniaAccount } from 'src/contexts/shared/utils/convenia-account/convenia-account.utils';

@Injectable()
export class CreateAdminUseCase {
  constructor(
    private readonly adminRepositoryImpl: AdminRepositoryImpl,
    private readonly usersRepositoryImpl: UsersRepositoryImpl,
    private readonly relUserRoleAdminRepositoryImpl: RelUserRoleAdminRepositoryImpl,
    private readonly roleRepository: RoleRepositoryImpl,
    private readonly readUserUseCase: ReadUserUseCase,
    private readonly createAdressUseCase: CreateAddressUseCase,
    readonly createUserUseCase: CreateUserUseCase,
    readonly createLegPersAccUseCase: CreateLegPersAccUseCase,
    readonly createPersonUseCase: CreatePersonUseCase,
    private readonly config: ConfigService,
    private readonly mailer: MailerService,
    private readonly s3Utils: StorageS3Utils,
    private readonly transfer: TransferAccount,
    private readonly s3: StorageS3Utils,
    private readonly conveniaAccount: ConveniaAccount,
  ) {}

  async execute(dto: CreateAdminDto): Promise<Admin> {
    const { user, address } = dto;

    const userExist = await this.readUserUseCase.executeEmail(user.email);

    if (userExist)
      throw new ConflictException(
        'El usuario con el correo electrónico ' +
          user.email +
          ' se ha registrado previamente.',
      );

    let { rfc } = dto;

    const alreadyExistsRFC = await this.adminRepositoryImpl.findByRFC(rfc);

    if (alreadyExistsRFC && !dto.is_sucursal)
      throw new ConflictException('RFC: ' + rfc + ' se encuentra en uso.');

    let group_id = CommonUtil.generateRandomCode(5);

    if (dto.is_sucursal && alreadyExistsRFC) {
      const rfcSequence = await this.adminRepositoryImpl.countByRFC(rfc);
      rfc = CommonUtil.generateRFCIdentifier(rfc, rfcSequence);
      group_id = alreadyExistsRFC.group_id;
    }

    let code: number;
    let codeAlreadyExists = true;

    while (codeAlreadyExists) {
      code = CommonUtil.generateRandomCode(7);
      codeAlreadyExists =
        (await this.adminRepositoryImpl.findByMembership(code)) !== null;
    }

    const { convenia_account } =
      await this.conveniaAccount.createConveniaAccount();

    const role = await this.roleRepository.findByName(RoleEnum.CLIENTE_ADMIN);

    if (!role)
      throw new InternalServerErrorException(
        'Error al obtener el rol del administrador',
      );

    // Extraer el código de área y número
    const phoneStr = user.phone.toString();
    const area_code = phoneStr.slice(0, 2); // Primeros 2 dígitos
    const number = phoneStr.slice(2); // Resto del número

    // Crear cuenta de persona legal
    const personAccountData: CreateLegPersAccDto = {
      legal_name: dto.company_name,
      email: user.email,
      password: user.password, // Add appropriate value for password
      rfc: rfc,
      dialing_code: '52',
      area_code: area_code.toString(),
      number: number.toString(),
      postal_code: dto.address.zip_code.toString(),
      suffix: this.getSuffix(dto.address.street.toString()), // Add appropriate value for suffix
      street: dto.address.street.toString(),
      addresses_number: dto.address.num_ext.toString(),
      city: dto.address.city.toString(),
      country_code: 'MX',
    };

    const personAccount =
      await this.createLegPersAccUseCase.executeLegPersAcc(personAccountData);

    await this.createLegPersAccUseCase.assgnGroup({
      person_id: personAccount.data.Person.person_id,
      group_id: this.config.get('DOCK_SPENDING_STD_VIP_GROUP_ID'),
    });

    const person = await this.createPersonUseCase.execute(
      personAccount.data.Person.person_id,
      personAccount.data.Account.id,
    );

    user.personIDDock = person.data.id;

    const adressSaved = await this.createAdressUseCase.execute(address);

    const userSaved = await this.usersRepositoryImpl.save({
      ...user,
      convenia_account,
      address: adressSaved.data.id,
    });

    const admin = await this.adminRepositoryImpl.save({
      ...dto,
      membership_number: code,
      manager: userSaved.data.id,
      group_id,
      rfc,
    });

    if (!dto.enterprises) {
      dto.enterprises = [];
    }

    const userDB = userSaved.data as Users;

    userDB.admin_data = admin;

    await this.usersRepositoryImpl.saveUser(userDB);

    dto.enterprises.push({ adminId: admin.id });

    for (const enterprise of dto.enterprises) {
      await this.relUserRoleAdminRepositoryImpl.save({
        admin_data: enterprise.adminId,
        user_id: userSaved.data.id as string,
        rol: role.id,
      });
    }

    await this.transfer.createAccount({
      person_id: person.data.id,
      limit_id: 'b9f9ad15-3ff0-4b7c-8bfa-608947f034d8',
    });

    const string_template: string = await this.s3Utils.getTemplateFromS3(
      'templates/bienvenida.html',
    );

    const template = Handlebars.compile(string_template);

    const { files } = dto;

    if (files && files.length) {
      for (const file of files) {
        await this.s3.moveToPermamentLocation(file.file, admin.id);

        const path = 'admin/'.concat(admin.id).concat('/').concat(file.file);
        const s3Metada = new S3Metadata();
        s3Metada.key = path;

        const s3File = await s3Metada.save();

        const document = new AdminDocuments();
        document.file = s3File;
        document.admin = admin;
        document.documentType = file.type;

        await document.save();
      }
    }

    await this.mailer.sendMail({
      to: user.email,
      from: `<${this.config.get('EMAIL_FROM_NOTIFICATIONS')}>`,
      subject: 'Bienvenido a Convenia',
      html: template({
        name_user: user.name,
        code,
      }),
    });

    return admin;
  }

  private getSuffix(street: string): string {
    let suffix = '';

    if (street.includes('Calle')) {
      suffix = 'C.';
    } else if (street.includes('Avenida') || street.includes('Av')) {
      suffix = 'Av.';
    } else if (street.includes('Boulevard') || street.includes('Blvd')) {
      suffix = 'Blvd.';
    } else if (street.includes('Carrera')) {
      suffix = 'Cra.';
    } else if (street.includes('Camino')) {
      suffix = 'Cam.';
    } else if (street.includes('Pasaje') || street.includes('Pje')) {
      suffix = 'Pje.';
    } else if (street.includes('Carretera') || street.includes('Carr')) {
      suffix = 'Carr.';
    } else {
      suffix = 'C.';
    }
    return suffix;
  }
}
