import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as https from 'https';
import axios from 'axios';

/* ** Utils ** */
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';

/* ** DTOs ** */
import { RespondeGetBalanceAccountDto } from './get-balance-account.dto';

@Injectable()
export class GetBalanceAccountUseCase {
  constructor(
    private readonly config: ConfigService,
    private readonly auth: authTokenDock,
  ) {}

  async balanceAccount(): Promise<RespondeGetBalanceAccountDto> {
    try {
      /* ** Get the authentication token and certificates */
      const auth = await this.auth.getAuthDock();

      /* ** Account ID and Person ID */
      const account_id = this.config.get<string>('DOCK_COMMISSIONS_ACCOUNT_ID');
      const person_id = this.config.get<string>('DOCK_COMMISSIONS_PERSON_ID');

      /* ** Configure the HTTPS agent with the certificates */
      const httpsAgent = new https.Agent({
        cert: auth.certificate,
        key: auth.key,
        rejectUnauthorized: false,
      });

      /* ** Payload Request */
      const requestBody = {
        person_id: person_id,
        id: account_id,
        external_account_id: account_id,
        metadata: {
          pagination: {
            page: 0,
            limit: 10,
          },
          sort: {
            field: 'id',
            order: 'asc',
          },
        },
      };

      /* ** Make the request to Dock's API */
      const response = await axios.post(
        `${this.config.get<string>('DOCK_URL_GLOBAL')}/account-services/management/v1/account-details`,
        requestBody,
        {
          headers: {
            Authorization: `Bearer ${auth.bearer_token}`,
            'Content-Type': 'application/json',
          },
          httpsAgent,
        },
      );

      /* ** Assign Data Balance */
      const data = response.data;
      const availableResource = data
        ? data.content[0].sub_account_instances[0].balance_category_instances[0]
            .balance_type_instances[0].current_balance
        : 0;

      return {
        balance: Number(availableResource).toFixed(2),
        statusCode: response.status,
      };
    } catch (error) {
      return {
        statusCode: error?.status || 500,
        balance: '0.00',
        error: error?.message || 'Error fetching balance',
      };
    }
  }
}
