import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

/* * * DTOs ** */
import {
  CreateClabeDto,
  ResponseCreateClabeDto,
} from 'src/contexts/shared/interfaces/dtos/transfer-account.dto';

/* ** Entities ** */
import { AccountTransfer } from '../../domain/entities/account-transfer.entity';

/* ** Repositories ** */
import { AccountTransferRepository } from '../../domain/repository/account-transfer.repository';
import { ReadTotalUsersDto } from '../../application/usecases-user/read-total-users/read-total-users.dto';

@Injectable()
export class AccountTransferRepositoryImpl
  implements AccountTransferRepository
{
  constructor(
    @InjectRepository(AccountTransfer)
    private readonly clabe: Repository<AccountTransfer>,
  ) {}

  async saveAccount(entity: CreateClabeDto): Promise<ResponseCreateClabeDto> {
    const res = await this.clabe.save(entity);
    return res;
  }

  async findClabe(clabe: string): Promise<boolean> {
    const account = await this.clabe.findOneBy({
      clabe,
    });

    return account && true;
  }

  async count({adminId} : ReadTotalUsersDto) : Promise<number>{
    const qb = this.clabe.createQueryBuilder("clabe")
              .leftJoin("clabe.person", "person")
              .leftJoin("person.users", "user")

      if(adminId){
        qb.leftJoin("user.admin_data", "admin_data");
        qb.andWhere("admin_data.id = :adminId", { adminId });
      }

      return await qb.getCount();
  }
}
