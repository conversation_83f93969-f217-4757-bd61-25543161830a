import { RelUserRoleAdmin } from "../../domain/entities/rel-user-role-admin.entity";
import { Users } from "../../domain/entities/users.entity";
import { EnterprisesVO, ManagerVO } from "../vo/manager.vo";

export class ManagerParser {
    static parseManagerToVO(manager: Users) : ManagerVO {
        return {
            id: manager.id,
            name: manager.name,
            phone: manager.phone,
            email: manager.email,
            dockId: manager.personIDDock?.personExtID,
            enterprises: manager.relUserRoleAdmins ?  manager.relUserRoleAdmins.map( this.parseRelRoleAdminsToEnterprises ) : [],
            clabe: manager.personIDDock?.transfers?.[0]?.clabe || null,
        }
    }

    static parseRelRoleAdminsToEnterprises(relUserRoleAdmins: RelUserRoleAdmin): EnterprisesVO {
    
       return {
           name: relUserRoleAdmins.admin?.company_name,
           id: relUserRoleAdmins.admin?.id,
           roleId: relUserRoleAdmins.role.name,
           roleName: relUserRoleAdmins.role.name
       }
    }
}