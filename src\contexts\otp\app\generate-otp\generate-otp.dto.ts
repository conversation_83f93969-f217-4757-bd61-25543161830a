import { IsString, IsNotEmpty } from 'class-validator';

export class GenerateEntityDto {
  @IsNotEmpty({ message: 'name should not be empty' })
  @IsString({ message: 'name should be a string' })
  email: string;
}

export class CreateOtpDto {
  @IsNotEmpty()
  @IsString()
  email: string;

  @IsNotEmpty()
  @IsString()
  acces_token: string;

  @IsNotEmpty()
  @IsString()
  code_otp: string;

  @IsNotEmpty()
  @IsString()
  attempts: number;
}
