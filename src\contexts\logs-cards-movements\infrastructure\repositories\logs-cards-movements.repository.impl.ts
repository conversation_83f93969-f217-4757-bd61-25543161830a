import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LogsCardsMovements } from '../../domain/entities/logs-cards-movements.entity';
import { LogsCardsMovementsRepository } from '../../domain/repository/logs-cards-movements.repository';
import { CreateLogCardsMovementsDto } from '../../application/create-log-cards-movements/create-log-cards-movements.dto';
import { ReadLogCardsMovementsDto } from '../../application/read-log-cards-movements/read-log-cards-movements.dto';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';

@Injectable()
export class LogsCardsMovementsRepositoryImpl implements LogsCardsMovementsRepository {
  constructor(
    @InjectRepository(LogsCardsMovements)
    private readonly logsCardsMovementsRepository: Repository<LogsCardsMovements>,
  ) {}

  async save(logCardsMovements: CreateLogCardsMovementsDto): Promise<ApiResponseDto> {
    try {
      const logEntity = this.logsCardsMovementsRepository.create(logCardsMovements);
      const savedLog = await this.logsCardsMovementsRepository.save(logEntity);
      
      return ResponseUtil.success(
        'Log de movimiento de tarjeta creado exitosamente',
        savedLog,
        201,
      );
    } catch (error) {
      return ResponseUtil.error(
        'No se pudo crear el log de movimiento de tarjeta',
        error,
        400,
      );
    }
  }

  async findAll(filters?: ReadLogCardsMovementsDto): Promise<ApiResponseDto> {
    try {
      const queryBuilder = this.logsCardsMovementsRepository
        .createQueryBuilder('logs')
        .orderBy('logs.created_at', 'DESC');

      // Apply filters
      if (filters?.old_card) {
        queryBuilder.andWhere('logs.old_card ILIKE :old_card', {
          old_card: `%${filters.old_card}%`,
        });
      }

      if (filters?.new_card) {
        queryBuilder.andWhere('logs.new_card ILIKE :new_card', {
          new_card: `%${filters.new_card}%`,
        });
      }

      if (filters?.reason) {
        queryBuilder.andWhere('logs.reason ILIKE :reason', {
          reason: `%${filters.reason}%`,
        });
      }

      if (filters?.start_date) {
        queryBuilder.andWhere('logs.created_at >= :start_date', {
          start_date: filters.start_date,
        });
      }

      if (filters?.end_date) {
        queryBuilder.andWhere('logs.created_at <= :end_date', {
          end_date: filters.end_date,
        });
      }

      // Apply pagination
      if (filters?.limit) {
        queryBuilder.limit(filters.limit);
      }

      const [logs, total] = await queryBuilder.getManyAndCount();

      return ResponseUtil.success(
        'Logs de movimientos de tarjetas obtenidos exitosamente',
        {
          data: logs,
          total,
          count: logs.length,
        },
        200,
      );
    } catch (error) {
      return ResponseUtil.error(
        'No se pudieron obtener los logs de movimientos de tarjetas',
        error,
        400,
      );
    }
  }

  async findById(id: string): Promise<ApiResponseDto> {
    try {
      const log = await this.logsCardsMovementsRepository.findOne({
        where: { id },
      });

      if (!log) {
        return ResponseUtil.error(
          'Log de movimiento de tarjeta no encontrado',
          null,
          404,
        );
      }

      return ResponseUtil.success(
        'Log de movimiento de tarjeta obtenido exitosamente',
        log,
        200,
      );
    } catch (error) {
      return ResponseUtil.error(
        'No se pudo obtener el log de movimiento de tarjeta',
        error,
        400,
      );
    }
  }
}