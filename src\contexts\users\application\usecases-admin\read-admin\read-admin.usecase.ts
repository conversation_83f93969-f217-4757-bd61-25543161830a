import { Injectable, NotFoundException } from '@nestjs/common';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { StorageS3Utils } from 'src/contexts/shared/utils/storageUtils/storageS3.utils';
import { AdminParser } from 'src/contexts/users/infrastructure/parsers/admin.parser';
import { AdminRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/adminrepository.impl';
import { AdminDetailVO } from 'src/contexts/users/infrastructure/vo/admin.vo';
import { ReadUsersByAdminFilterDto } from './read-admins-filter.dto';
import { UsersRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/users.repository.impl';
import { AdminUsersListVO } from 'src/contexts/users/infrastructure/vo/user.vo';
import { UserParser } from 'src/contexts/users/infrastructure/parsers/user.parser';
import { ReadAminTotalAmountUseCase } from '../read-total-amount/read-total-amount.usecase';

@Injectable()
export class ReadAdminUseCase {
  constructor(
    private readonly adminRepositoryImpl: AdminRepositoryImpl,
    private readonly s3: StorageS3Utils,
    private readonly userRepository: UsersRepositoryImpl,
    private readonly readAminTotalAmountUseCase: ReadAminTotalAmountUseCase,
  ) {}

  async readById(id: string): Promise<AdminDetailVO> {
    const admin = await this.adminRepositoryImpl.findById(id);

    if (!admin) throw new NotFoundException('Admin no encontrado');

    const adminReponse = AdminParser.parseAdminToVO(admin);

    if (admin.documents && admin.documents.length) {
      for (const document of admin.documents) {
        const url = await this.s3.generatePresignedUrl(
          `documentos/${document.file.key}`,
        );
        adminReponse.files.push({
          type: document.documentType,
          url: url,
          id: document.id,
        });
      }
    }

    adminReponse.amount = (
      await this.readAminTotalAmountUseCase.execute({ adminId: id })
    ).totalAmount;

    return adminReponse;
  }

  async executeMemebershipNumber(
    membership_number: number,
  ): Promise<ApiResponseDto> {
    return this.adminRepositoryImpl.findByMembershipNumber(membership_number);
  }

  async getUsers(
    id: string,
    filter: ReadUsersByAdminFilterDto,
  ): Promise<AdminUsersListVO> {
    const { users, count } = await this.userRepository.getUsersByAdmin(
      id,
      filter,
    );

    const usersParsed = users.map(UserParser.parseToAdminUser);

    return { users: usersParsed, count };
  }
}
