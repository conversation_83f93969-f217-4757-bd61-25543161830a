import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

/* ** Entities ** */
import { BulkTransfers } from 'src/contexts/users/domain/entities/bulk-transfers.entity';
import { BulkTransfersError } from 'src/contexts/users/domain/entities/bulk-transfers-errors.entity';

/* ** Controllers ** */
import { TransfersController } from '../infrastructure/http/transfers.controller';

/* ** Modules ** */
import { PersonAccountModule } from 'src/contexts/person-account/module/person-account.module';
import { AuthModule } from 'src/contexts/auth/module/auth.module';
import { CustomMailerModule } from 'src/contexts/shared/modules/mailer.module';
import { AccountModule } from 'src/contexts/account/module/account.module';
import { UsersModule } from 'src/contexts/users/module/users.module';

/* ** Use Cases ** */
import { DockpayTransferUseCase } from '../application/create-transaction/dockpay-transfer.usecase';

/* ** Utils ** */
import { CommissionsUtils } from 'src/contexts/shared/utils/commissions/commissions.utils';
import { EncryptData } from 'src/contexts/shared/utils/sensitive-data/encriptData.util';
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';
import { RedisService } from 'src/contexts/shared/utils/Redis/redis';
import { BullQueueModule } from 'src/contexts/shared/modules/BullQueue.module';
import { BulkTransferProcessor } from '../infrastructure/queue/bulk-transfer.processor';
import { StorageS3Utils } from 'src/contexts/shared/utils/storageUtils/storageS3.utils';
import { CheckOrders } from 'src/contexts/transfer-orders/application/check-orders/check-orders.usecase';

/* ** Repositories ** */

@Module({
  imports: [
    TypeOrmModule.forFeature([BulkTransfers, BulkTransfersError]),
    PersonAccountModule,
    AuthModule,
    CustomMailerModule,
    AccountModule,
    UsersModule,
    BullQueueModule
  ],
  controllers: [TransfersController],
  providers: [
    DockpayTransferUseCase,
    EncryptData,
    authTokenDock,
    RedisService,
    CommissionsUtils,
    BulkTransferProcessor,
    StorageS3Utils,
    CheckOrders
  ],
  exports: [],
})
export class TransactionsModule {}
