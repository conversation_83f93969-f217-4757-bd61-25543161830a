import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as https from 'https';
import axios from 'axios';
import { DateTime } from 'luxon';

/* ** Utils ** */
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';
import { DecryptData } from 'src/contexts/shared/utils/sensitive-data/decryptData.util';
import { RedisService } from 'src/contexts/shared/utils/Redis/redis';
import { SecurePayloadUtil } from 'src/contexts/shared/utils/data-encrypt/data-encrypt.util';

/* ** DTOs ** */
import { ParamsCheckCvv, ResCheckCvv, DataCvv } from './cvv-dock-card.dto';

/* ** Errors ** */
import {
  ERROR_PHYSICAL_CVV,
  ERROR_VIRTUAL_CVV,
} from './../../infrastructure/errors/errors-dock-cards';

@Injectable()
export class QueryDockCardCvvUseCase {
  constructor(
    private readonly config: ConfigService,
    private readonly auth: authTokenDock,
    private readonly decryptData: DecryptData,
    private readonly log: RedisService,
    private readonly secure: SecurePayloadUtil,
  ) {}

  async queryCVV(card: ParamsCheckCvv): Promise<ResCheckCvv> {
    /* ** Set the expiration date ** */
    const current_date = DateTime.now().setZone(card.time_zone);
    const expiration_date = current_date.plus({ minutes: 5 }).toUTC().toISO();

    const data_cvv =
      card.card_type === 'PHYSICAL'
        ? await this.getCardPhysicalCVV({ ...card, expiration_date })
        : await this.getCardVirtualCVV({ ...card, expiration_date });

    if (!data_cvv)
      throw card.card_type === 'PHYSICAL'
        ? new ERROR_PHYSICAL_CVV()
        : new ERROR_VIRTUAL_CVV();

    return {
      statusCode: 200,
      message: 'Success',
      error: '',
      data: data_cvv,
    };
  }

  public async getCardPhysicalCVV(card: ParamsCheckCvv): Promise<DataCvv> {
    const { bearer_token, certificate, key } = await this.auth.getAuthDock();

    /* ** Agent ** */
    const httpsAgent = new https.Agent({
      cert: certificate,
      key,
      rejectUnauthorized: false,
    });

    const execute = async (): Promise<DataCvv> => {
      try {
        const data_card = await axios.get(
          `${this.config.get('DOCK_URL_GLOBAL')}/cards/v1/cards/${card.card_dock_id}/data`,
          {
            headers: {
              Authorization: `Bearer ${bearer_token}`,
            },
            httpsAgent,
          },
        );

        const { aes, iv, cvv, id } = data_card.data;

        const decrypt_cvv = await this.decryptData.decryptData({
          aes,
          iv,
          encrypt_data: cvv,
        });

        /* ** Set the expiration date ** */
        const current_date = DateTime.now().setZone(card.time_zone);
        const expiration_date = current_date
          .plus({ minutes: 6 })
          .toUTC()
          .toISO();
        const generation_date = current_date.toUTC().toISO();

        const format_data: DataCvv = {
          cvv: decrypt_cvv,
          card_id: id,
          expiration_date,
          generation_date,
          time_zone: card.time_zone,
        };

        return format_data;
      } catch (error) {
        const code = error.response.status;
        this.log.logError({
          key: 'api:log',
          name: 'ERROR_CVV_PHYSICAL_DOCK',
          error: error?.response?.data || error?.message,
          nameController: 'QueryDockCardCvvUseCase',
          statusCode: code,
        });

        return null;
      }
    };
    const response = await execute();

    return response;
  }

  private async getCardVirtualCVV(card: ParamsCheckCvv): Promise<DataCvv> {
    const { bearer_token, certificate, key } = await this.auth.getAuthDock();

    /* ** Agent ** */
    const httpsAgent = new https.Agent({
      cert: certificate,
      key,
      rejectUnauthorized: false,
    });

    const execute = async (): Promise<DataCvv> => {
      try {
        const data_card = await axios.post(
          `${this.config.get('DOCK_URL_GLOBAL')}/cards/v1/cards/${card.card_dock_id}/dynamic-cvv`,
          { expiration_date: card.expiration_date },
          {
            headers: {
              Authorization: `Bearer ${bearer_token}`,
            },
            httpsAgent,
          },
        );

        const { aes, iv, cvv, card_id, expiration_date, generation_date } =
          data_card.data;

        const decrypt_cvv = await this.decryptData.decryptData({
          aes,
          iv,
          encrypt_data: cvv,
        });

        const format_data: DataCvv = {
          cvv: this.secure.encryptData(decrypt_cvv),
          card_id,
          expiration_date,
          generation_date,
          time_zone: card.time_zone,
        };

        return format_data;
      } catch (error) {
        const code = error.response.status;
        this.log.logError({
          key: 'api:log',
          name: 'ERROR_CVV_VIRTUAL_DOCK',
          error: error?.response?.data || error?.message,
          nameController: 'QueryDockCardCvvUseCase',
          statusCode: code,
        });

        return null;
      }
    };

    const query = async (): Promise<DataCvv> => {
      try {
        const data_card = await axios.get(
          `${this.config.get('DOCK_URL_GLOBAL')}/cards/v1/cards/${card.card_dock_id}/dynamic-cvv`,
          {
            headers: {
              Authorization: `Bearer ${bearer_token}`,
            },
            httpsAgent,
          },
        );

        const { aes, iv, cvv, card_id, expiration_date, generation_date } =
          data_card.data;

        const decrypt_cvv = await this.decryptData.decryptData({
          aes,
          iv,
          encrypt_data: cvv,
        });

        const format_data: DataCvv = {
          cvv: this.secure.encryptData(decrypt_cvv),
          card_id,
          expiration_date,
          generation_date,
          time_zone: card.time_zone,
        };

        return format_data;
      } catch (error) {
        console.log('error', error.response.data.error);
        const code = error.response.status;
        this.log.logError({
          key: 'api:log',
          name: 'ERROR_QUERY_VIRTUAL_DOCK',
          error: error?.response?.data || error?.message,
          nameController: 'QueryDockCardCvvUseCase',
          statusCode: code,
        });

        return null;
      }
    };

    const response_execute = await execute();

    const response = response_execute ? response_execute : await query();

    return response;
  }
}
