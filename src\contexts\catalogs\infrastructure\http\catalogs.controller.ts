import { Controller, Get, Param, ParseIntPipe, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from 'src/contexts/auth/guards/auth.guard';
import { ReadStatesUseCase } from '../../application/usecases-states/read-states/read-states.usecase';
import { ReadCitiesUseCase } from '../../application/usecases-states/read-cities/read-cities.usecase';
import { ApiResponse } from '@nestjs/swagger';
import { State } from 'src/contexts/catalogs/domain/entities/state.entity';
import { City } from 'src/contexts/catalogs/domain/entities/city.entity';


@UseGuards(JwtAuthGuard)
@Controller('catalogs')
export class CatalogsController {
  constructor(
    private readonly readStatesUseCase: ReadStatesUseCase,
    private readonly readCitiesUseCase: ReadCitiesUseCase
  ) {}

  @ApiResponse({
      status: 200,
      type: [State]
    })
  @Get("states")
  getStates() {
    return this.readStatesUseCase.execute();
  }

  @ApiResponse({
      status: 200,
      type: [City]
  })
  @Get("states/:stateId/cities")
  getCities(@Param("stateId", ParseIntPipe) stateId: number) {
    return this.readCitiesUseCase.execute(stateId);
  }

}