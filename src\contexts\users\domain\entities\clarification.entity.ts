import {
  <PERSON><PERSON><PERSON>,
  PrimaryC<PERSON>umn,
  Column,
  CreateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { Users } from './users.entity';
import { ClarificationFile } from './clarification-file.entity';

@Entity('clarification')
export class Clarification {
  @PrimaryColumn({ length: 6 })
  trackingNumber: string;

  @Column()
  type: string;

  @Column('text')
  description: string;

  @CreateDateColumn()
  createdAt: Date;

  @Column()
  status: string;

  @ManyToOne(() => Users, (user) => user.clarificationsCreated, { eager: true })
  @JoinColumn({ name: 'created_by' })
  createdBy: Users;

  @ManyToOne(() => Users, (user) => user.clarificationsAssigned, {
    nullable: true,
    eager: true,
  })
  @JoinColumn({ name: 'assigned_admin' })
  assignedAdmin?: Users;

  @OneToMany(() => ClarificationFile, (file) => file.clarification, {
    cascade: true,
    eager: true,
  })
  files: ClarificationFile[];
}
