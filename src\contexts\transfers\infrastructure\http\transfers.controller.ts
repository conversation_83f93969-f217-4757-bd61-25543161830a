import { Body, Controller, Post, UseGuards, Get, Param, ParseUUIDPipe } from '@nestjs/common';
import { DockpayTransferUseCase } from '../../application/create-transaction/dockpay-transfer.usecase';
import { DockPayBulkTransferstDto, ParamsDockPayTransfertDto } from '../../application/create-transaction/dockpay-transfer.dto';
import { DataConfirmTransferDto } from '../../application/create-transaction/confirm-password.dto';
import { JwtAuthGuard } from 'src/contexts/auth/guards/auth.guard';
import { GetUserDecorator } from 'src/contexts/auth/decorators/get-user.decorator';
import { Users } from 'src/contexts/users/domain/entities/users.entity';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { PaginationDto } from 'src/contexts/shared/dto/pagination.dto';

@UseGuards(JwtAuthGuard)
@Controller('transfer')
export class TransfersController {
  constructor(
    private readonly dockpayTransferUseCase: DockpayTransferUseCase,
    @InjectQueue('bulk-transfers') private readonly bulkTransfersQueue: Queue,
  ) {}

  @Post('dock')
  transferToDock(@Body() createTransferContact: ParamsDockPayTransfertDto) {
    return this.dockpayTransferUseCase.executePay(createTransferContact);
  }

  @Post('validatePasswordToTransfer')
  validatePasswordToTransfer(@Body() data: DataConfirmTransferDto) {
    return this.dockpayTransferUseCase.executeValidatePassword(
      data.email,
      data.password,
    );
  }

  @Post('dock/bulk')
  transferToDockBulk(
    @Body() dto: DockPayBulkTransferstDto,
    @GetUserDecorator() user: Users
  ) {
    return this.dockpayTransferUseCase.executePayBulk(dto, user);
  }

  @Get('dock/bulk/:id')
  async getBulkTransferStatus(@Param('id', ParseUUIDPipe) id: string) {
    return this.dockpayTransferUseCase.getBulkTransferStatus(id);
  }

  @Get('dock/bulk/:id/errors')
  async getBulkTransfersfailed(@Param('id', ParseUUIDPipe) id: string) {
    return this.dockpayTransferUseCase.getBulkTransfersfailed(id);
  }

  @Get('dock/user/:id/bulk')
  async getUserBulkTransfers(@Param('id', ParseUUIDPipe) id: string, @Param() dto: PaginationDto) {
    return this.dockpayTransferUseCase.getBulkTransfersByUserId(id, dto);
  }


  // @Get('queue/status')
  // async getQueueStatus() {
  //   try {
  //     console.log('Getting queue status...');
  //     const waiting = await this.bulkTransfersQueue.getWaiting();
  //     const active = await this.bulkTransfersQueue.getActive();
  //     const completed = await this.bulkTransfersQueue.getCompleted();
  //     const failed = await this.bulkTransfersQueue.getFailed();

  //     console.log('Queue status retrieved successfully');
      
  //     return {
  
  //       waiting: waiting.length,
  //       active: active.length,
  //       completed: completed.length,
  //       failed: failed.length,
  //       activeJobs: active.map(job => ({
  //         id: job.id,
  //         progress: job.progress(),
  //         data: job.data
  //       }))
  //     };
  //   } catch (error) {
  //     console.error('Error getting queue status:', error);
  //     return { error: error.message };
  //   }
  // }

  // @Get('queue/job/:jobId')
  // async getJobStatus(@Param('jobId') jobId: string) {
  //   const job = await this.bulkTransfersQueue.getJob(jobId);
  //   if (!job) {
  //     return { error: 'Job not found' };
  //   }

  //   return {
  //     id: job.id,
  //     progress: job.progress(),
  //     state: await job.getState(),
  //     data: job.data,
  //     failedReason: job.failedReason,
  //     processedOn: job.processedOn,
  //     finishedOn: job.finishedOn
  //   };
  // }
}
