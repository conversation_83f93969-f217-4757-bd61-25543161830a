import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Patch,
  UseGuards,
} from '@nestjs/common';
import { CreateClarificationUseCase } from '../../application/usecases-clarification/create-clarification/create-clarification.usecase';
import { CreateClarificationDto } from '../../application/usecases-clarification/create-clarification/create-clarification.dto';
import { UpdateClarificationUseCase } from '../../application/usecases-clarification/update-clarification/update-clarification.usecase';
import { UpdateClarificationDto } from '../../application/usecases-clarification/update-clarification/update-clarification.dto';
import { JwtAuthGuard } from 'src/contexts/auth/guards/auth.guard';
import { RoleProtected } from 'src/contexts/auth/decorators/role-protected.decorator';
import { UserRoleGuard } from 'src/contexts/auth/guards/user-role.guard';
import { RoleEnum } from 'src/contexts/shared/enums/roles.enum';

import { ReadClarificationUseCase } from '../../application/usecases-clarification/read-clarification/read-clarification.usecase';
import { ClarificationVO } from 'src/contexts/users/infrastructure/vo/clarification.vo';
import { ReadOneClarificationUseCase } from '../../application/usecases-clarification/read-clarification/read-one-clarification.usecase';

@Controller('clarification')
@UseGuards(JwtAuthGuard)
export class ClarificationController {
  constructor(
    private readonly createClarificationUseCase: CreateClarificationUseCase,
    private readonly updateClarificationUseCase: UpdateClarificationUseCase,
    private readonly readClarificationUseCase: ReadClarificationUseCase,
    private readonly readOneClarificationUseCase: ReadOneClarificationUseCase,
  ) {}

  @Post('save')
  createClarification(@Body() createClarificationDto: CreateClarificationDto) {
    return this.createClarificationUseCase.execute(createClarificationDto);
  }

  @Patch('update/:id')
  @RoleProtected(RoleEnum.CONVENIA_ADMIN)
  @UseGuards(UserRoleGuard)
  updateClarification(
    @Param('id') id: string,
    @Body() updateClarificationDto: UpdateClarificationDto,
  ) {
    return this.updateClarificationUseCase.execute(id, updateClarificationDto);
  }

  @Get()
  async getClarifications(
    @Query('limit') limit = 10,
    @Query('offset') offset = 0,
  ): Promise<ClarificationVO[]> {
    return this.readClarificationUseCase.execute({
      limit: Number(limit),
      offset: Number(offset),
    });
  }

  @Get(':trackingNumber')
  getOne(@Param('trackingNumber') trackingNumber: string) {
    return this.readOneClarificationUseCase.execute(trackingNumber);
  }
}
