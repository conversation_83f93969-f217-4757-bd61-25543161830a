import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import * as https from 'https';

/* ** DTOs ** */
import {
  CreateDockCardPhysicalDto,
  CreateDockCardVirtualDto,
  CreateDockCardBatchDto,
} from './create-dock-card.dto';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';

/* ** Enums ** */
import { CardType } from 'src/contexts/shared/interfaces/dtos/cards.enum.dto';

/* ** Utils ** */
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';

@Injectable()
export class CreateDockCardUseCase {
  constructor(
    private readonly authTokenDock: authTokenDock,
    private readonly configService: ConfigService,
  ) {}

  async createDockCardPhysical(
    bodyCard: CreateDockCardPhysicalDto,
  ): Promise<ApiResponseDto> {
    const { bearer_token, certificate, key } =
      await this.authTokenDock.getAuthDock();

    /* ** Actualizamos profile_id ** */
    bodyCard.profile_id = this.configService.get('DOCK_PROFILE_ID');

    if (bodyCard.type === CardType.PHYSICAL) {
      bodyCard.embossing_setup_id = this.configService.get(
        'DOCK_EMBOSSING_SETUP_ID',
      );
    }

    /* ** Agent ** */
    const httpsAgent = new https.Agent({
      cert: certificate,
      key,
      rejectUnauthorized: false,
    });

    const card = await axios.post(
      `${this.configService.get('DOCK_URL_GLOBAL')}/cards/v1/cards`,
      bodyCard,
      {
        headers: {
          Authorization: `Bearer ${bearer_token}`,
        },
        httpsAgent,
      },
    );

    return ResponseUtil.success('Successfully', { response: card.data }, 200);
  }

  async createDockCardVirtual(
    bodyCard: CreateDockCardVirtualDto,
  ): Promise<ApiResponseDto> {
    const { bearer_token, certificate, key } =
      await this.authTokenDock.getAuthDock();

    /* ** Actualizamos profile_id ** */
    bodyCard.profile_id = this.configService.get('DOCK_PROFILE_ID');

    /* ** Agent ** */
    const httpsAgent = new https.Agent({
      cert: certificate,
      key,
      rejectUnauthorized: false,
    });

    const card = await axios.post(
      `${this.configService.get('DOCK_URL_GLOBAL')}/cards/v1/cards`,
      bodyCard,
      {
        headers: {
          Authorization: `Bearer ${bearer_token}`,
        },
        httpsAgent,
      },
    );
    return ResponseUtil.success('Successfully', { response: card.data }, 200);
  }

  async createDockCardBatch(
    bodyBatch: CreateDockCardBatchDto,
  ): Promise<ApiResponseDto> {
    const { bearer_token, certificate, key } =
      await this.authTokenDock.getAuthDock();

    /* ** Actualizamos profile_id ** */
    bodyBatch.profile_id = this.configService.get('DOCK_PROFILE_ID');
    bodyBatch.embossing_setup_id = this.configService.get(
      'DOCK_EMBOSSING_SETUP_ID',
    );

    /* ** Agent ** */
    const httpsAgent = new https.Agent({
      cert: certificate,
      key,
      rejectUnauthorized: false,
    });

    const card = await axios.post(
      `${this.configService.get('DOCK_URL_GLOBAL')}/cards/v1/batches`,
      bodyBatch,
      {
        headers: {
          Authorization: `Bearer ${bearer_token}`,
        },
        httpsAgent,
      },
    );
    return ResponseUtil.success('Successfully', { response: card.data }, 200);
  }
}
