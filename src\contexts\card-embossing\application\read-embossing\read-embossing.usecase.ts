import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import * as https from 'https';

/* ** DTOs ** */
import { ApiResponseDto } from './../../../shared/interfaces/dtos/api-response.dto';
import { ReadEmbossingParamsDto } from './read-embossing.dto';

/* ** Utils ** */
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';

@Injectable()
export class ReadEmbossingUseCase {
  constructor(
    private readonly authServices: authTokenDock,
    private readonly configService: ConfigService,
  ) {}

  async readEmbossing(query: ReadEmbossingParamsDto): Promise<ApiResponseDto> {
    /* ** Get token ** */
    const auth = await this.authServices.getAuthDock();

    /* ** Agent ** */
    const httpsAgent = new https.Agent({
      cert: auth.certificate,
      key: auth.key,
      rejectUnauthorized: false,
    });

    const files_embossing = await axios.get(
      `${this.configService.get('DOCK_URL_GLOBAL')}/embossing/v1/files`,
      {
        headers: {
          Authorization: `Bearer ${auth.bearer_token}`,
          Accept: 'application/json',
        },
        httpsAgent,
        params: query,
      },
    );

    return ResponseUtil.success(
      'List of embossing files',
      { data: files_embossing.data },
      200,
    );
  }
}
