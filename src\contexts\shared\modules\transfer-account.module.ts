import { Module, forwardRef } from '@nestjs/common';

/* ** Modules ** */
import { UsersModule } from 'src/contexts/users/module/users.module';

/* ** Utils ** */
import { TransferAccount } from '../utils/transfer-account/transfer-account.utils';

@Module({
  imports: [forwardRef(() => UsersModule)],
  providers: [TransferAccount],
  exports: [TransferAccount],
})
export class TransferAccountClabeModule {}
