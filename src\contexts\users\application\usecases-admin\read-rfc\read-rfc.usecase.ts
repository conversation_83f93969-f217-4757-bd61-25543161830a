import { Injectable } from '@nestjs/common';
import { Admin } from 'src/contexts/users/domain/entities/admin.entity';
import { AdminRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/adminrepository.impl';

@Injectable()
export class ReadRFCUseCase {
  constructor(private readonly adminRepositoryImpl: AdminRepositoryImpl) {}

  async execute(rfc: string): Promise<Admin> {
    return this.adminRepositoryImpl.findByRFC(rfc);
  }
}
