import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotificationPreferencesRepository } from '../../domain/repository/notification-preferences.repository';
import { UserNotificationPreference } from '../../domain/entities/notification-preference.entity';
import { UpdateNotificationPreferencesDto } from '../../application/update-notification-preferences/update-notification-preferences.dto';

@Injectable()
export class NotificationPreferencesRepositoryImpl
  implements NotificationPreferencesRepository
{
  constructor(
    @InjectRepository(UserNotificationPreference)
    private readonly repository: Repository<UserNotificationPreference>,
  ) {}

  async createOrUpdate(
    userId: string,
    preferences: UpdateNotificationPreferencesDto,
  ): Promise<UserNotificationPreference> {
    // Busca si ya existen preferencias para el usuario
    let existingPreference = await this.repository.findOne({
      where: { user: { id: userId } },
    });

    if (existingPreference) {
      // Si ya existe, actualiza las preferencias
      Object.assign(existingPreference, preferences);
      return await this.repository.save(existingPreference);
    }

    // Si no existe, crea un nuevo registro
    const newPreference = this.repository.create({
      user: { id: userId },
      ...preferences,
    });
    return await this.repository.save(newPreference);
  }

  async findByUserId(
    userId: string,
  ): Promise<UserNotificationPreference | null> {
    return await this.repository.findOne({
      where: { user: { id: userId } },
    });
  }
}
