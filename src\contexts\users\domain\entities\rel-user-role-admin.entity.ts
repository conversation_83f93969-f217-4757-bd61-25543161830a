import { BaseEntity, <PERSON>tity, ManyTo<PERSON>ne, PrimaryGeneratedColumn, Unique } from "typeorm";
import { Users } from "./users.entity";
import { Rol } from "./rol.entity";
import { Admin } from "./admin.entity";


@Entity()
export class RelUserRoleAdmin extends BaseEntity {

    @PrimaryGeneratedColumn("uuid")
    id: string;

    @ManyToOne(() => Users, user => user.relUserRoleAdmins)
    user: Users;

    @ManyToOne(() => Rol, role => role.relUserRoleAdmins)
    role: Rol;

    @ManyToOne(() => Admin, admin => admin.relUserRoleAdmins, {nullable: true})
    admin: Admin;

}