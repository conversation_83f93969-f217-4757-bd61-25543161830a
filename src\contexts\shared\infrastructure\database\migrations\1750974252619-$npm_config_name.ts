import { MigrationInterface, QueryRunner } from 'typeorm';

export class $npmConfigName1750974252619 implements MigrationInterface {
  name = ' $npmConfigName1750974252619';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."commissions_type_commission_enum" AS ENUM('SPEI IN', 'SPEI OUT', 'ACCOUNT FUNDING')`,
    );
    await queryRunner.query(
      `CREATE TABLE "commissions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "type_commission" "public"."commissions_type_commission_enum" NOT NULL DEFAULT 'SPEI OUT', "player_account" character varying NOT NULL, "embajador_account" character varying NOT NULL, "amount" character varying NOT NULL, "status" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_d108d70411783e2a3a84e386601" PRIMARY KEY ("id"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "commissions"`);
    await queryRunner.query(
      `DROP TYPE "public"."commissions_type_commission_enum"`,
    );
  }
}
