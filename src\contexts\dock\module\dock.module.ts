import { Module } from '@nestjs/common';
import { DockCoreService } from '../infraestructure/services/dock-core.service';
import { DockLegalPersonService } from '../infraestructure/services/dock-legal-person.service';
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';
import { RedisService } from 'src/contexts/shared/utils/Redis/redis';
import { DockTransactionsService } from '../infraestructure/services/dock-transactions.service';
@Module({
  imports: [],
  providers: [
    DockCoreService,
    DockLegalPersonService,
    authTokenDock,
    RedisService,
    DockTransactionsService
  ],
  exports: [DockLegalPersonService, DockTransactionsService],
})
export class DockModule {}
