import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsEmail,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { Rol } from '../../domain/entities/rol.entity';
import { CountVO } from 'src/contexts/shared/vo/count.vo';
import { BasicAdminVO } from './admin.vo';

export class TotalUsersVO {
  count: number;
}

class Role {
  @ApiProperty()
  @IsUUID()
  id: string;

  @ApiProperty()
  @IsString()
  name: string;
}

export class ConveniaUserVO {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsNumber()
  phone: number;

  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsDate()
  createdAt: Date;

  @ApiProperty({ type: [Rol] })
  roles: Rol[];

  @ApiProperty({type: BasicAdminVO})
  admin: BasicAdminVO;

}

export class ConveniUsersListVO extends CountVO {
  @ApiProperty({type: [ConveniaUserVO]})
  users: ConveniaUserVO[]
}

export class UserAccountVO {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsString()
  adminAlias: string;

  @ApiProperty()
  @IsString()
  clabe: string;

  @ApiProperty()
  @IsNumber()
  amount: number;

  @ApiProperty()
  @IsBoolean()
  speiIn: boolean;

  @ApiProperty()
  @IsBoolean()
  speiOut: boolean;

  @ApiProperty()
  @IsBoolean()
  enabled: boolean;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  isDeleteable?: boolean;

  @ApiProperty()
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  roles?: Role[];
}

export class UserAccountListVO extends CountVO {
  @ApiProperty({type: [UserAccountVO]})
  userAccounts: UserAccountVO[]
}


export class UserAccountDeatilVO {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsNumber()
  phone: number;

  @ApiProperty({type: BasicAdminVO})
  admin: BasicAdminVO;

}

export class AdminUserVO {
  @ApiProperty()
  @IsUUID()
  id: string;

  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsEmail()
  email: string;

  @ApiProperty({type: Role})
  role: Role;

  @ApiProperty()
  @IsDate()
  createdAt: Date;
}

export class AdminUsersListVO extends CountVO {
  @ApiProperty({ type: [AdminUserVO] })
  users: AdminUserVO[];
}