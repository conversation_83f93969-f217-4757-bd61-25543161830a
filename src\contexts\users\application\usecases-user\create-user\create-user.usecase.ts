import { RelUserRoleAdmin } from './../../../domain/entities/rel-user-role-admin.entity';
import { ConflictException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { CreateConveniaUserDto, CreateUserDto } from './create-user.dto';
import { UsersRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/users.repository.impl';
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';
import { RelUserRoleAdminRepositoryImpl } from '../../../infrastructure/repositories/rel-user-role-admin.repository.impl';
import { ReadUserUseCase } from '../read-user/read-user.usecase';
import { AdminRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/adminrepository.impl';
import { CreateLegPersAccUseCase } from 'src/contexts/person-account/application/create-person-account-legal/create-pers-acc-leg.usecase';
import { CreateLegPersAccDto } from 'src/contexts/person-account/application/create-person-account-legal/create-pers-acc-leg.dto';
import { CreatePersonUseCase } from '../../usecases-person/create-person/create-person-usecase';
import { Users } from 'src/contexts/users/domain/entities/users.entity';
import { UserParser } from 'src/contexts/users/infrastructure/parsers/user.parser';
import { ConveniaUserVO } from 'src/contexts/users/infrastructure/vo/user.vo';
import { RoleRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/role.repository.impl';

/* ** Utils ** */
import { TransferAccount } from 'src/contexts/shared/utils/transfer-account/transfer-account.utils';
import { CommonUtil } from 'src/contexts/shared/utils/common.util';
import { ConveniaAccount } from 'src/contexts/shared/utils/convenia-account/convenia-account.utils';

@Injectable()
export class CreateUserUseCase {
  constructor(
    private readonly usersRepositoryImpl: UsersRepositoryImpl,
    private readonly relUserRoleAdminRepositoryImpl: RelUserRoleAdminRepositoryImpl,
    private readonly readUserUseCase: ReadUserUseCase,
    private readonly adminRepositoryImpl: AdminRepositoryImpl,
    readonly createLegPersAccUseCase: CreateLegPersAccUseCase,
    readonly createPersonUseCase: CreatePersonUseCase,
    private readonly roleRepository: RoleRepositoryImpl,
    private readonly config: ConfigService,
    private readonly transfer: TransferAccount,
    private readonly conveniaAccount: ConveniaAccount,
  ) {}

  async executeApp(user: CreateUserDto): Promise<ApiResponseDto> {
    const userExist = await this.readUserUseCase.executeEmail(user.email);

    if (userExist) {
      return ResponseUtil.error('User already exist', null, 400);
    }

    const adminExist = await this.adminRepositoryImpl.findByMembershipNumber(
      user.membership_number,
    );

    if (adminExist.error) {
      return ResponseUtil.error(
        'No se pudo crear el usuario',
        'El número de membresía proporcionado no existe',
        400,
      );
    } else if (
      !adminExist.data.relUserRoleAdmins[0] ||
      !adminExist.data.relUserRoleAdmins[0].user.address
    ) {
      return ResponseUtil.error(
        'No se pudo crear el usuario',
        'El número de membresía proporcionado no tiene una dirección válida',
        400,
      );
    }

    // Extraer el código de área y número
    const phoneStr = user.phone.toString();
    const area_code = phoneStr.slice(0, 2); // Primeros 2 dígitos
    const number = phoneStr.slice(2); // Resto del número
    const rfcSequence = await this.adminRepositoryImpl.countByRFC(
      adminExist.data.rfc,
    );
    const rfcSequenceApp = await this.usersRepositoryImpl.countByRFC(
      adminExist.data.rfc,
    );
    const rfcUserApp = CommonUtil.generateRFCIdentifierAppUser(
      adminExist.data.rfc,
      rfcSequence + rfcSequenceApp,
    );

    // Crear cuenta de persona legal
    const personAccountData: CreateLegPersAccDto = {
      legal_name: user.name,
      email: user.email,
      password: user.password, // Add appropriate value for password
      rfc: rfcUserApp,
      dialing_code: user.dialing_code || '52', // Default to '52' if not provided
      area_code: area_code,
      number: number,
      postal_code:
        adminExist.data.relUserRoleAdmins[0].user.address.zip_code.toString(),
      suffix: this.getSuffix(
        adminExist.data.relUserRoleAdmins[0].user.address.street.toString(),
      ), // Add appropriate value for suffix
      street:
        adminExist.data.relUserRoleAdmins[0].user.address.street.toString(),
      addresses_number:
        adminExist.data.relUserRoleAdmins[0].user.address.num_ext.toString(),
      city: adminExist.data.relUserRoleAdmins[0].user.address.city.toString(),
      country_code: user.country_code || 'MX', // Default to 'MX' if not provided
    };

    const personAccount =
      await this.createLegPersAccUseCase.executeLegPersAcc(personAccountData);

    await this.createLegPersAccUseCase.assgnGroup({
      person_id: personAccount.data.Person.person_id,
      group_id: this.config.get('DOCK_SPENDING_STD_VIP_GROUP_ID'),
    });

    const person = await this.createPersonUseCase.execute(
      personAccount.data.Person.person_id,
      personAccount.data.Account.id,
    );

    const { convenia_account } =
      await this.conveniaAccount.createConveniaAccount();

    await this.transfer.createAccount({
      person_id: person.data.id,
      limit_id: 'b9f9ad15-3ff0-4b7c-8bfa-608947f034d8',
    });

    delete user.country_code;
    delete user.dialing_code;
    user.address = adminExist.data.relUserRoleAdmins[0].user.address.id;
    user.personIDDock = person.data.id;
    user.admin_data = adminExist.data.id;
    user.rfc = rfcUserApp;
    const userCreated = await this.usersRepositoryImpl.save({
      ...user,
      convenia_account,
    });
    await this.relUserRoleAdminRepositoryImpl.save({
      rol: null,
      user_id: userCreated.data.id,
      admin_data: adminExist.data.id,
    });
    return userCreated;
  }

  async executeConveniaUser(
    dto: CreateConveniaUserDto,
  ): Promise<ConveniaUserVO> {
    const userExist = await this.readUserUseCase.executeEmail(dto.email);

    if (userExist) {
      throw new ConflictException('El correo ya se encuentra registrado.');
    }

    const role = await this.roleRepository.findById(dto.rol);

    let admin = null;
    let rfcUserApp = null;

    if (dto.admin_data) {
      admin = await this.adminRepositoryImpl.findById(dto.admin_data);

      if (!admin) throw new ConflictException('Admin not found');

      const rfcSequence = await this.adminRepositoryImpl.countByRFC(admin.rfc);

      rfcUserApp = CommonUtil.generateRFCIdentifierAppUser(
        admin.rfc,
        rfcSequence,
      );
    }

    const res = await this.usersRepositoryImpl.save({
      ...dto,
      rfc: rfcUserApp,
    });

    if (res.error) {
      throw new ConflictException(res.message);
    }

    const user = res.data as Users;

    const relRoleAdmin = new RelUserRoleAdmin();
    relRoleAdmin.user = user;
    relRoleAdmin.role = role;
    relRoleAdmin.admin = admin;

    await relRoleAdmin.save();

    if (dto.admin_data) {
      const phoneStr = user.phone.toString();
      const area_code = phoneStr.slice(0, 2); // Primeros 2 dígitos
      const number = phoneStr.slice(2); // Resto del número

      const personAccountData: CreateLegPersAccDto = {
        legal_name: user.name,
        email: user.email,
        password: user.password, // Add appropriate value for password
        rfc: rfcUserApp,
        dialing_code: dto.dialing_code ? dto.dialing_code : '52',
        area_code: area_code,
        number: dto.phone ? dto.phone.toString() : number,
        postal_code:
          admin.relUserRoleAdmins[0].user.address.zip_code.toString(),
        suffix: this.getSuffix(
          admin.relUserRoleAdmins[0].user.address.street.toString(),
        ), // Add appropriate value for suffix
        street: admin.relUserRoleAdmins[0].user.address.street.toString(),
        addresses_number:
          admin.relUserRoleAdmins[0].user.address.num_ext.toString(),
        city: admin.relUserRoleAdmins[0].user.address.city.toString(),
        country_code: dto.country_code ? dto.country_code : 'MX',
      };

      const personAccount =
        await this.createLegPersAccUseCase.executeLegPersAcc(personAccountData);

      await this.createLegPersAccUseCase.assgnGroup({
        person_id: personAccount.data.Person.person_id,
        group_id: this.config.get('DOCK_SPENDING_STD_VIP_GROUP_ID'),
      });

      const person = await this.createPersonUseCase.execute(
        personAccount.data.Person.person_id,
        personAccount.data.Account.id,
      );

      const { convenia_account } =
        await this.conveniaAccount.createConveniaAccount();

      await this.transfer.createAccount({
        person_id: person.data.id,
        limit_id: 'b9f9ad15-3ff0-4b7c-8bfa-608947f034d8',
      });

      user.personIDDock = person.data.id;
      user.address = admin.relUserRoleAdmins[0].user.address.id;

      await this.usersRepositoryImpl.saveUser({
        ...user,
        convenia_account,
      });
    }

    return UserParser.parseToConveniaUser(user, [role], admin);
  }

  async executeAdmin(user: CreateUserDto): Promise<ApiResponseDto> {
    const { rol, admin_data } = user;

    if (rol && !admin_data) {
      return ResponseUtil.error(
        'Admin data is required when role is provided',
        null,
        400,
      );
    }

    if (admin_data && !rol) {
      return ResponseUtil.error(
        'Role is required when admin data is provided',
        null,
        400,
      );
    }

    const responseDto = await this.usersRepositoryImpl.save(user);

    if (responseDto.statusCode !== 201 && admin_data && rol) {
      await this.relUserRoleAdminRepositoryImpl.save({
        admin_data,
        rol: rol,
        user_id: responseDto.data.id,
      });
    }

    return responseDto;
  }

  private getSuffix(street: string): string {
    let suffix = '';

    if (street.includes('Avenida') || street.includes('Av')) {
      suffix = 'Av.';
    } else if (street.includes('Boulevard') || street.includes('Blvd')) {
      suffix = 'Blvd.';
    } else if (street.includes('Carrera')) {
      suffix = 'Cra.';
    } else if (street.includes('Camino')) {
      suffix = 'Cam.';
    } else if (street.includes('Pasaje') || street.includes('Pje')) {
      suffix = 'Pje.';
    } else if (street.includes('Carretera') || street.includes('Carr')) {
      suffix = 'Carr.';
    } else {
      suffix = 'C.';
    }
    return suffix;
  }
}
