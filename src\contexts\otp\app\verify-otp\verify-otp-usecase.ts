import { Injectable, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';

/* ** DTOs ** */
import { VerifyEntityDto } from './verify-otp.dto';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { OtpJwtDto } from 'src/contexts/shared/interfaces/dtos/jwt.dto';

/* ** Repositories ** */
import { OtpRepositoryImpl } from '../../infrastructure/repositories/otp.repository.impl';

/* ** Utils ** */
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';

@Injectable()
export class VerifyOtpUseCase {
  constructor(
    private readonly OtpRepositoryImpl: OtpRepositoryImpl,
    private readonly jwt: JwtService,
    private readonly config: ConfigService,
  ) {}

  async executeVerifyOTP(params: VerifyEntityDto): Promise<ApiResponseDto> {
    /* ** Verificar OTP ** */
    const payload: OtpJwtDto = this.jwt.verify(params.access, {
      secret: this.config.get('JWT_SECRET'),
    });

    const get_otp = await this.OtpRepositoryImpl.findOneByID(payload.id_otp);

    if (get_otp.attempts >= 3) {
      await this.OtpRepositoryImpl.deleteOtp(get_otp.id);

      return ResponseUtil.error(
        'Se ha excedido el número de intentos',
        {
          data: {
            statusCode: HttpStatus.UNAUTHORIZED,
            message: 'AttemptsExceededError',
            error: 'ATTEMPTS_EXCEEDED',
          },
        },
        401,
      );
    }

    if (get_otp.email !== payload.email) {
      await this.OtpRepositoryImpl.addAttempts(get_otp.id, get_otp.attempts);

      return ResponseUtil.error(
        'El correo electrónico no coincide',
        {
          data: {
            statusCode: HttpStatus.UNAUTHORIZED,
            message: 'InvalidEmailError',
            error: 'EMAIL_INVALID',
          },
        },
        401,
      );
    }

    /* ** Verificar OTP ** */
    if (get_otp.code_otp !== params.code) {
      await this.OtpRepositoryImpl.addAttempts(get_otp.id, get_otp.attempts);

      return ResponseUtil.error(
        'Código OTP incorrecto',
        {
          data: {
            statusCode: HttpStatus.UNAUTHORIZED,
            message: 'InvalidOTPError',
            error: 'OTP_INVALID',
          },
        },
        401,
      );
    }

    /* ** Eliminar OTP ** */
    await this.OtpRepositoryImpl.deleteOtp(get_otp.id);

    return ResponseUtil.success('Successfully', { params }, 200);
  }

  async deleteOtpEntry(params: VerifyEntityDto): Promise<boolean> {
    return await this.OtpRepositoryImpl.deleteOTP(params);
  }
}
