import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DateTime } from 'luxon';

/* ** services ** */
import { DockTransactionsService } from 'src/contexts/dock/infraestructure/services/dock-transactions.service';

/* ** Repositories ** */
import { CommissionRepositoryImpl } from '../../infrastructure/repositories/commission.repository.impl';
import { UserTransferRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/user-transfer.repository.impl';

/* ** DTOs ** */
import {
  ParamsCommissionFetchDto,
  ResponseCommissionFetchDto,
  DataTransaction,
  Pagination,
} from './commission-fetch.dto';

@Injectable()
export class CommissionFetchUseCase {
  /* ** Zone Horaria ** */
  private zonaHoraria: string = 'America/Mexico_City';

  constructor(
    private readonly config: ConfigService,
    private readonly dock: DockTransactionsService,
    private readonly commission: CommissionRepositoryImpl,
    private readonly userTransfer: UserTransferRepositoryImpl,
  ) {}

  async fetchCommissionData(
    params: ParamsCommissionFetchDto,
  ): Promise<ResponseCommissionFetchDto> {
    const { page, sort, transfer_way } = params;
    /* ** Get the account ID from the configuration */
    const commission_account = this.config.get<string>(
      'DOCK_COMMISSIONS_ACCOUNT_ID',
    );

    const items: DataTransaction[] = [];

    const transactions =
      transfer_way === 'IN'
        ? await this.dock.getInTransactions(
            commission_account,
            page,
            'OUT',
            sort,
          )
        : await this.dock.getTransactions(
            commission_account,
            page,
            transfer_way,
            sort,
          );

    if (transactions.items.length === 0) {
      return {
        statusCode: 404,
        message: 'No transactions found',
        error: 'Not Found',
      };
    }

    const pagination: Pagination = {
      previous_page: transactions.previous_page,
      current_page: transactions.current_page,
      next_page: transactions.next_page,
      total_pages: transactions.total_pages,
      total_items: transactions.total_items,
      max_items_per_page: transactions.max_items_per_page,
      total_items_page: transactions.total_items_page,
    };

    for (const transaction of transactions.items) {
      const external_id = transaction?.external_transaction_id ?? null;

      let own_commission = null;

      if (external_id && transfer_way === 'IN') {
        own_commission = await this.commission.findById(external_id);
      } else if (external_id && transfer_way === 'OUT') {
        own_commission = await this.userTransfer.findByIDPayment(external_id);
      }

      const dateString =
        typeof transaction.transaction_date === 'string'
          ? transaction.transaction_date
          : transaction.transaction_date?.toString();

      const dt = DateTime.fromISO(dateString, {
        zone: 'utc',
      }).setZone(this.zonaHoraria);

      const parsedTransaction: DataTransaction = {
        description: transaction.description,
        cargo: own_commission?.amount || 'N/A',
        commission: transaction.amount.toFixed(2),
        hora: dt.toFormat('HH:mm:ss'),
        fecha: dt.toFormat('yyyy-MM-dd'),
        player_account: own_commission?.player_account || 'N/A',
        type_commission:
          transfer_way === 'OUT'
            ? 'SPEI OUT'
            : own_commission?.type_commission || 'N/A',
      };

      items.push(parsedTransaction);
    }

    return {
      statusCode: 200,
      message: 'Commissions fetched successfully',
      items: items,
      pagination: pagination,
    };
  }
}
