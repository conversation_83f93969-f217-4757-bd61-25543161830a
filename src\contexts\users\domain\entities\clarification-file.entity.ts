import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  JoinColumn,
} from 'typeorm';
import { Clarification } from './clarification.entity';

@Entity('clarification_files')
export class ClarificationFile {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  file_name: string;

  @Column()
  file_url: string;

  @CreateDateColumn()
  created_at: Date;

  @ManyToOne(() => Clarification, (clarification) => clarification.files, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'clarification_tracking_number' })
  clarification: Clarification;
}
