import { Injectable } from '@nestjs/common';
import { UpdateEntityDto } from 'src/contexts/entity/application/update-entity/update-entity.dto';
import { EntityRepositoryImpl } from 'src/contexts/entity/infrastructure/repositories/entity.repository.impl';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';

@Injectable()
export class UpdateEntityUseCase {
  constructor(private readonly entityRepositoryImpl: EntityRepositoryImpl) {}

  async execute(id: string, entity: UpdateEntityDto): Promise<ApiResponseDto> {
    return this.entityRepositoryImpl.update(id, entity);
  }
}
