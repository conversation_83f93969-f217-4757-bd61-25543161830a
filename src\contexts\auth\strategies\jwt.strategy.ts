import { ExtractJwt, Strategy } from "passport-jwt";
import { ConfigService } from "@nestjs/config";
import { PassportStrategy } from "@nestjs/passport";
import { Repository } from "typeorm";
import { InjectRepository } from "@nestjs/typeorm";
import { Injectable, UnauthorizedException } from "@nestjs/common";
import { IJwtPayload } from "../interfaces/jwt-payload.interface";
import { Users } from "src/contexts/users/domain/entities/users.entity";

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) { 
  constructor(
    @InjectRepository(Users)
    private readonly userRepository: Repository<Users>,
    private readonly configService: ConfigService
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET_AUTH')
    });
  }

  async validate(payload: IJwtPayload) : Promise<Users> {
 
    const { id } = payload;

    const user = await this.userRepository.createQueryBuilder('user')
    .where('user.id = :id', { id })
    .leftJoinAndSelect('user.relUserRoleAdmins', 'relUserRoleAdmin')
    .leftJoinAndSelect('relUserRoleAdmin.admin', 'relAdmin')
    .leftJoinAndSelect('relUserRoleAdmin.role', 'relRol')
    .getOne();

    if(!user)
        throw new UnauthorizedException('Invalid credentials');

    return user;
  }
}