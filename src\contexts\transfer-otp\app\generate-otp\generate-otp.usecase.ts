import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { MailerService } from '@nestjs-modules/mailer';
import * as Handlebars from 'handlebars';

/* ** DTOs ** */
import {
  GenerateEntityDto,
  CreateOtpDto,
} from '../../../otp/app/generate-otp/generate-otp.dto';
import { OtpJwtDto } from 'src/contexts/shared/interfaces/dtos/jwt.dto';

/* ** Repositories ** */
import { OtpRepositoryImpl } from '../../../otp/infrastructure/repositories/otp.repository.impl';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';

/* ** Utils ** */
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';
import { StorageS3Utils } from 'src/contexts/shared/utils/storageUtils/storageS3.utils';
import { ReadUserUseCase } from 'src/contexts/users/application/usecases-user/read-user/read-user.usecase';

@Injectable()
export class GenerateOtpUseCase {
  constructor(
    private readonly OtpRepositoryImpl: OtpRepositoryImpl,
    private readonly jwt: JwtService,
    private readonly config: ConfigService,
    private readonly mailer: MailerService,
    private readonly s3Utils: StorageS3Utils,
    private readonly readonlyUsersUseCase: ReadUserUseCase,
  ) {}

  async createOtpEntry(params: GenerateEntityDto): Promise<ApiResponseDto> {
    const user = await this.readonlyUsersUseCase.executeEmail(params.email);
    /* ** Generate OTP de 4 digitos ** */
    const code_otp: number = this.generateOtpCode();
    const string_template: string = await this.s3Utils.getTemplateFromS3(
      'templates/dispersion-saldos.html',
    );

    /* ** Remplazamos los valores del template ** */
    const template = Handlebars.compile(string_template);

    await this.mailer.sendMail({
      to: params.email,
      from: `🔔 Convenia Support`,
      subject: 'Dispersión de saldos Convenia',
      html: template({
        name_user: user.name,
        code: code_otp.toString(),
      }), // './' is necessary for the path to resolve correctly
    });

    /* ** Entity ** */
    const entity_otp = this.buildOtpEntity(params.email, String(code_otp));

    /* ** create OTP ** */
    const { data: save_otp } = await this.OtpRepositoryImpl.save(entity_otp);

    /* ** Token ** */
    const token = this.generateJwtToken(save_otp.data.id, params.email);

    await this.OtpRepositoryImpl.updateToken(save_otp.data.id, token);

    /* ** Retorno ** */
    return ResponseUtil.success('Successfully', { token }, 200);
  }

  private generateOtpCode(): number {
    return Math.floor(1000 + Math.random() * 9000);
  }

  private buildOtpEntity(email: string, code_otp: string): CreateOtpDto {
    return {
      email,
      acces_token: '',
      code_otp: code_otp,
      attempts: 0,
    };
  }

  private generateJwtToken(id_otp: string, email: string): string {
    const payload: OtpJwtDto = { id_otp, email };
    return this.jwt.sign(payload, {
      expiresIn: '5m',
      secret: this.config.get('JWT_SECRET'),
    });
  }
}
