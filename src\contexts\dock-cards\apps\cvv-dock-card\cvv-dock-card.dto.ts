import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON><PERSON>al,
  Is<PERSON>tring,
  <PERSON><PERSON><PERSON><PERSON>,
  ValidateNested,
} from 'class-validator';

import { Type } from 'class-transformer';

import { CardType } from 'src/contexts/shared/interfaces/dtos/cards.enum.dto';

export class DataCvv {
  @IsString()
  @IsUUID()
  card_id: string;

  @IsString()
  cvv: string;

  @IsString()
  expiration_date: string;

  @IsString()
  generation_date: string;

  @IsString()
  time_zone: string;
}

export class ParamsCheckCvv {
  @IsString()
  @IsUUID()
  card_dock_id: string;

  @IsEnum(CardType)
  card_type: CardType;

  @IsOptional()
  @IsString()
  time_zone: string;

  @IsOptional()
  @IsString()
  expiration_date: string;
}

export class ResCheckCvv {
  @IsNumber()
  statusCode: number;

  @IsString()
  message: string;

  @IsString()
  error: string;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => DataCvv)
  data?: DataCvv;
}
