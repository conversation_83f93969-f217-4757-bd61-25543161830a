import { ApiProperty } from "@nestjs/swagger";
import { <PERSON><PERSON><PERSON><PERSON>, IsE<PERSON>, IsString, Is<PERSON>UI<PERSON> } from "class-validator";


export class EnterprisesVO {
    @IsString()
    name: string;

    @IsUUID()
    id: string;

    @IsString()
    roleId: string;

    @IsString()
    roleName: string;
}
export class ManagerVO {
    @ApiProperty()
    @IsUUID()
    id: string;
    @ApiProperty()
    @IsString()
    name: string;
    @ApiProperty()
    @IsString()
    phone: number;

    @ApiProperty()
    @IsString()
    @IsEmail()
    email: string;

    @IsString()
    dockId?: string;

    @ApiProperty()
    @IsArray()
    @ApiProperty({ type: [EnterprisesVO] })
    enterprises: EnterprisesVO[];

    @ApiProperty()
    @IsString()
    clabe?: string;
}
