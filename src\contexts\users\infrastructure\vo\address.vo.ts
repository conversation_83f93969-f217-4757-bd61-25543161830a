import { ApiProperty } from "@nestjs/swagger";
import { <PERSON>N<PERSON><PERSON>, IsString, IsUUID } from "class-validator";

export class AdressVO {
    @ApiProperty()
    @IsUUID()
    id: string;
    @ApiProperty()
    @IsString()
    state: string;
    @ApiProperty()
    @IsString()
    city: string;
    @ApiProperty()
    @IsString()
    colonia: string;
    @ApiProperty()
    @IsString()
    street: string;
    @ApiProperty()
    @IsString()
    numExt: string;
    @ApiProperty()
    @IsString()
    zipCode: string;
}