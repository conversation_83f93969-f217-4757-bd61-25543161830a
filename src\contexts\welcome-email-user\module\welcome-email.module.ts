import { Module } from '@nestjs/common';

/* ** Modules ** */
import { CustomMailerModule } from 'src/contexts/shared/modules/mailer.module';

/* ** Controllers ** */
import { WelcomeEmailController } from '../infrastructure/http/welcome-email.controller';

/* ** Use Cases ** */
import { SendWelcomeEmailUseCase } from '../app/send-welcome-email/send-welcome-email.usecase';

/* ** Utils ** */
import { StorageS3Utils } from 'src/contexts/shared/utils/storageUtils/storageS3.utils';

@Module({
  imports: [CustomMailerModule],
  controllers: [WelcomeEmailController],
  providers: [SendWelcomeEmailUseCase, StorageS3Utils],
  exports: [SendWelcomeEmailUseCase],
})
export class WelcomeEmailModule {}
