import {
  IsString,
  <PERSON><PERSON><PERSON>,
  IsNotEmpty,
  IsOptional,
  IsArray,
  ValidateNested,
  IsInt,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateNatPersAccDto {
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  person_name: string;

  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  password: string;

  @IsString()
  @IsNotEmpty()
  affiliation_number?: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  voter_id?: string;

  @IsString()
  @IsNotEmpty()
  dialing_code: string;

  @IsString()
  @IsNotEmpty()
  area_code: string;

  @IsString()
  @IsNotEmpty()
  number: string;

  @IsString()
  @IsNotEmpty()
  country_code: string;
}

class DocumentDto {
  @IsInt()
  type_id: number;

  @IsString()
  @IsNotEmpty()
  number: string;

  @IsNotEmpty()
  is_main: boolean;

  @IsString()
  @IsNotEmpty()
  country_code: string;
}

class PhoneDto {
  @IsInt()
  type_id: number;

  @IsNotEmpty()
  is_main: boolean;

  @IsString()
  @IsNotEmpty()
  dialing_code: string;

  @IsString()
  @IsNotEmpty()
  area_code: string;

  @IsString()
  @IsNotEmpty()
  number: string;

  @IsString()
  @IsNotEmpty()
  country_code: string;
}

class AddressDto {
  @IsInt()
  type_id: number;

  @IsNotEmpty()
  is_main: boolean;

  @IsString()
  @IsNotEmpty()
  postal_code: string;

  @IsString()
  @IsNotEmpty()
  suffix: string;

  @IsString()
  @IsNotEmpty()
  street: string;

  @IsString()
  @IsNotEmpty()
  number: string;

  @IsString()
  @IsNotEmpty()
  city: string;

  @IsString()
  @IsNotEmpty()
  country_code: string;
}

class EmailDto {
  @IsInt()
  type_id: number;

  @IsNotEmpty()
  is_main: boolean;

  @IsEmail()
  @IsNotEmpty()
  email: string;
}

export class NaturalPersonDataDto {
  @IsInt()
  status_id: number;

  @IsString()
  @IsNotEmpty()
  person_full_name: string;

  @IsString()
  @IsNotEmpty()
  preferred_name: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DocumentDto)
  documents: DocumentDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PhoneDto)
  phones: PhoneDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AddressDto)
  addresses: AddressDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EmailDto)
  emails: EmailDto[];
}

export class AccountDataDto {
  @IsString()
  @IsNotEmpty()
  product_id: string;

  @IsString()
  @IsNotEmpty()
  person_id: string;
}
