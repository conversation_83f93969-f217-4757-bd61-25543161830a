import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export class ParamsCommissionFetchDto {
  @IsString()
  @IsNotEmpty()
  sort: 'DESC' | 'ASC';

  @IsString()
  page: number;

  @IsString()
  @IsNotEmpty()
  transfer_way: 'IN' | 'OUT';
}

export class Pagination {
  @IsNumber()
  previous_page: number;

  @IsNumber()
  current_page: number;

  @IsNumber()
  next_page: number;

  @IsNumber()
  total_pages: number;

  @IsNumber()
  total_items: number;

  @IsNumber()
  max_items_per_page: number;

  @IsNumber()
  total_items_page: number;
}

export class DataTransaction {
  @IsString()
  description: string;

  @IsString()
  cargo: string;

  @IsString()
  commission: string;

  @IsString()
  hora: string;

  @IsString()
  fecha: string;

  @IsString()
  player_account: string;

  @IsString()
  type_commission: string;
}

export class ResponseCommissionFetchDto {
  @IsNumber()
  statusCode: number;

  @IsString()
  message: string;

  @IsString()
  @IsOptional()
  error?: string;

  @IsArray()
  @ValidateNested()
  @Type(() => DataTransaction)
  items?: DataTransaction[];

  @ValidateNested()
  @Type(() => Pagination)
  pagination?: Pagination;
}
