import { Injectable } from '@nestjs/common';
import { BiometricPreferencesRepository } from '../../infrastructure/repository/biometric-preferences.repository';
import { BiometricPreference } from '../../domain/entities/biometric-preference.entity';

@Injectable()
export class UpdateBiometricPreferencesUseCase {
  constructor(
    private readonly biometricPreferencesRepository: BiometricPreferencesRepository,
  ) {}

  async execute(
    userId: string,
    preferences: Partial<BiometricPreference>,
  ): Promise<BiometricPreference> {
    return await this.biometricPreferencesRepository.createOrUpdate(
      userId,
      preferences,
    );
  }
}
