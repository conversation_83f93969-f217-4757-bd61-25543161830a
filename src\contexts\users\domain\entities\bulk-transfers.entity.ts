import { BaseEntity, Column, CreateDateColumn, Entity, JoinColumn, ManyToOne, OneToMany, OneToOne, PrimaryGeneratedColumn } from "typeorm";
import { Users } from "./users.entity";
import { S3Metadata } from "./s3-metadata.entity";
import { BulkTransfersError } from "./bulk-transfers-errors.entity";
import { ApiProperty } from "@nestjs/swagger";
import { IsDateString, IsEnum, IsNumber, IsString, IsUUID } from "class-validator";

export enum BulkTransfersStatus {
    PENDING = 'PENDING',
    PROCESSING = 'PROCESSING',
    COMPLETED = 'COMPLETED',
    FAILED = 'FAILED'
}

@Entity()
export class BulkTransfers extends BaseEntity{
    @ApiProperty()
    @IsUUID()
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @ApiProperty()
    @IsString()
    @Column()
    total: number;

    @ApiProperty()
    @IsDateString()
    @CreateDateColumn()
    createdAt: Date;

    @ApiProperty({ enum: BulkTransfersStatus })
    @IsEnum(BulkTransfersStatus)
    @Column({ default: 'PENDING' })
    status: BulkTransfersStatus;

    @ApiProperty()
    @IsNumber()
    @Column({ default: 0 })
    successCount: number;

    @ApiProperty()
    @IsNumber()
    @Column({ default: 0 })
    failureCount: number;

    @ManyToOne(type => Users, user => user.bulkTransfers)
    @JoinColumn()
    creationUser: Users;

    @OneToOne(type => S3Metadata, s3Metadata => s3Metadata.bulkTransfer)
    @JoinColumn()
    file: S3Metadata;

    @OneToMany(type => BulkTransfersError, error => error.bulkTransfer)
    errors: BulkTransfersError[];
}