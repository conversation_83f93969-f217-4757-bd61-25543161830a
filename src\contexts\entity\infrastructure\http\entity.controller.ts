import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Param,
  Post,
  Put,
} from '@nestjs/common';
import { CreateEntityUseCase } from 'src/contexts/entity/application/create-entity/create-entity.usecase';
import { CreateEntityDto } from '../../application/create-entity/create-entity.dto';
import { ReadEntityUseCase } from 'src/contexts/entity/application/read-entities/read-entity.usecase';
import { UpdateEntityUseCase } from 'src/contexts/entity/application/update-entity/update-entity.usecase';
import { UpdateEntityDto } from '../../application/update-entity/update-entity.dto';
import { ReadEntitiesUseCase } from 'src/contexts/entity/application/read-entities/read-entities.usecase';
import { DeleteEntityUseCase } from 'src/contexts/entity/application/delete-entity/delete-entity.usecase';

@Controller('entities')
export class EntityController {
  constructor(
    private readonly createEntityUseCase: CreateEntityUseCase,
    private readonly readEntitiesUseCase: ReadEntitiesUseCase,
    private readonly readEntityUseCase: ReadEntityUseCase,
    private readonly updateEntityUseCase: UpdateEntityUseCase,
    private readonly deleteEntityUseCase: DeleteEntityUseCase,
  ) {}

  @Post()
  async createEntity(@Body() entity: CreateEntityDto) {
    const res = await this.createEntityUseCase.execute(entity);
    if (res.error) {
      throw new HttpException(res, 400);
    }
    return res;
  }

  @Get()
  async getAllEntities() {
    const res = await this.readEntitiesUseCase.execute();
    if (res.error) {
      throw new HttpException(res, 404);
    }
    return res;
  }

  @Get(':id')
  async getEntity(@Param('id') id: string) {
    const res = await this.readEntityUseCase.execute(id);
    if (res.error) {
      throw new HttpException(res, 404);
    }
    return res;
  }

  @Put(':id')
  async updateEntity(@Param('id') id: string, @Body() entity: UpdateEntityDto) {
    const res = await this.updateEntityUseCase.execute(id, entity);
    if (res.error) {
      throw new HttpException(res, 400);
    }
    return res;
  }

  @Delete(':id')
  async deleteEntity(@Param('id') id: string) {
    const res = await this.deleteEntityUseCase.execute(id);
    if (res.error) {
      throw new HttpException(res, 400);
    }
    return res;
  }
}
