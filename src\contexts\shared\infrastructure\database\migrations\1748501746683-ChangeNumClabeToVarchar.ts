import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeNumClabeToVarchar1748501746683
  implements MigrationInterface
{
  name = 'ChangeNumClabeToVarchar1748501746683';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "transfer_contact" DROP COLUMN "num_clabe"`,
    );
    await queryRunner.query(
      `ALTER TABLE "transfer_contact" ADD "num_clabe" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "transfer_contact" ALTER COLUMN "num_clabe" TYPE varchar`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "transfer_contact" DROP COLUMN "num_clabe"`,
    );
    await queryRunner.query(
      `ALTER TABLE "transfer_contact" ADD "num_clabe" bigint`,
    );
  }
}
