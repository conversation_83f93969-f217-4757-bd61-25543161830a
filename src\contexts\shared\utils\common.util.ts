
export class CommonUtil {


    static compareObjects(obj: any, obj2: any): boolean {
        const obj1WithoutId = { ...obj };
        const obj2WithoutId = { ...obj2 };

        if(obj1WithoutId.id)
            delete obj1WithoutId.id;
        if(obj2WithoutId.id)
            delete obj2WithoutId.id;

        return JSON.stringify(obj1WithoutId) !== JSON.stringify(obj2WithoutId);
    }

    static generateRandomCode(length: number): number {
        return parseInt(Math.random().toString().substring(2, length + 2), 10);
    }

    static generateRFCIdentifier(rfc: string, sequence: number, prefix: string = "S"): string {
        return `${rfc.concat("-")}${prefix}${sequence.toString().padStart(3, '0')}`;
    }

    static generateRFCIdentifierAppUser(rfc: string, sequence: number, prefix: string = "U"): string {
        return `${rfc.concat("-")}${prefix}${sequence.toString().padStart(4, '0')}`;
    }

}
