import { Injectable } from "@nestjs/common";
import { AdminRepositoryImpl } from "src/contexts/users/infrastructure/repositories/adminrepository.impl";

@Injectable()
export class ReadTotalCardsUseCase {
    
    constructor(
        private readonly adminRepository: AdminRepositoryImpl
    ){}

    async execute(adminId:string) {
       const total = await this.adminRepository.getTotalCards(adminId);
       return {total};
    }

}