import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';

/* ** Controllers ** */
import { AuthController } from '../infraestructure/http/auth.controller';

/* ** Use Cases ** */
import { SigninUseCase } from '../application/signin/signin.usecase';
import { RenewTokenUseCase } from '../application/renew-token/renew-token.usecase';
import { BiometricAuthUseCase } from '../application/biometric/biometric-auth.usecase';

/* ** Providers ** */
import { JwtStrategy } from '../strategies/jwt.strategy';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Users } from 'src/contexts/users/domain/entities/users.entity';
import { CustomRedisStoreModule } from 'src/contexts/shared/modules/redis.module';

/* ** Module ** */

@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([Users]),
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return {
          secret: configService.get('JWT_SECRET_AUTH'),
          signOptions: {
            expiresIn: configService.get('JWT_EXPIRATION_AUTH') || 900000,
          },
        };
      },
    }),
    CustomRedisStoreModule,
  ],
  controllers: [AuthController],
  providers: [
    JwtStrategy,
    SigninUseCase,
    RenewTokenUseCase,
    BiometricAuthUseCase, // 👈 Agrega este
  ],
  exports: [JwtStrategy, PassportModule, JwtModule, SigninUseCase],
})
export class AuthModule {}
