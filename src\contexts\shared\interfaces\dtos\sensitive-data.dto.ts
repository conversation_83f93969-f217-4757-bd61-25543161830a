import { IsString } from 'class-validator';

/* ** DTOs for encrypt data ** */
export class ResponseEncryptDto {
  @IsString()
  iv: string;

  @IsString()
  aes: string;

  @IsString()
  encrypted_data: string;

  @IsString()
  mode: string;
}

export class ParamsEncryptDto {
  @IsString()
  encrypted_data: string;
}

export class HeaderPublicKetDto {
  @IsString()
  Authorization: string;

  @IsString()
  Accept: string;

  @IsString()
  'Content-Type': string;
}

export class EncryptDataAESDto {
  @IsString()
  body_text: string;
}

export class ResponseEncryptDataAESDto {
  @IsString()
  encrypt_data: string;

  @IsString()
  iv: string;

  @IsString()
  aes: Buffer;
}

/* ** DTOs for decrypt data ** */
export class ParamsDecryptDto {
  @IsString()
  encrypt_data: string;

  @IsString()
  iv: string;

  @IsString()
  aes: string;
}

export class ParamsDecryptAESDto {
  @IsString()
  encrypt_data: string;

  @IsString()
  iv: Buffer;

  @IsString()
  aes: Buffer;
}
