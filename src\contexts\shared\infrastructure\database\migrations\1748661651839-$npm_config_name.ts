import { MigrationInterface, QueryRunner } from "typeorm";

export class  $npmConfigName1748661651839 implements MigrationInterface {
    name = ' $npmConfigName1748661651839'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "isSpeiInEnabled" SET DEFAULT true`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "isSpeiOutEnabled" SET DEFAULT true`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "isSpeiOutEnabled" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "isSpeiInEnabled" DROP DEFAULT`);
    }

}
