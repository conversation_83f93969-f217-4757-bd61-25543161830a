import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
} from 'typeorm';
import { Users } from 'src/contexts/users/domain/entities/users.entity';

@Entity('user_notifications')
export class UserNotifications {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @ManyToOne(() => Users, (user) => user.userNotifications, { onDelete: 'CASCADE' })
  user: Users;

  @Column({ type: 'text' })
  type: 'transaction' | 'reminder';

  @Column()
  title: string;

  @Column()
  message: string;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  @Column({ default: false })
  read: boolean;
}
