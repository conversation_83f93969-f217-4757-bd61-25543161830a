// src/contexts/clarification/infra/repositories/clarification.repository.impl.ts

import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { ClarificationRepository } from '../../domain/repository/clarification.repository';
import { Clarification } from '../../domain/entities/clarification.entity';
import { CreateClarificationDto } from '../../application/usecases-clarification/create-clarification/create-clarification.dto';
import { UpdateClarificationDto } from '../../application/usecases-clarification/update-clarification/update-clarification.dto';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';
import { Injectable } from '@nestjs/common';

@Injectable()
export class ClarificationRepositoryImpl implements ClarificationRepository {
  constructor(
    @InjectRepository(Clarification)
    private readonly clarificationRepository: Repository<Clarification>,
  ) {}

  async save(dto: CreateClarificationDto): Promise<ApiResponseDto> {
    try {
      const clarification = this.clarificationRepository.create({
        ...dto,
        createdBy: { id: dto.createdBy }, // Properly cast to DeepPartial<Users>
      });
      const result = await this.clarificationRepository.save(clarification);
      return ResponseUtil.success(
        'Aclaración creada exitosamente',
        result,
        201,
      );
    } catch (error) {
      return ResponseUtil.error('No se pudo crear la aclaración', error, 400);
    }
  }

  async findAll(): Promise<ApiResponseDto> {
    try {
      const clarifications = await this.clarificationRepository.find({
        relations: ['createdBy', 'assignedAdmin'],
        order: { createdAt: 'DESC' },
      });
      return ResponseUtil.success(
        'Aclaraciones obtenidas exitosamente',
        clarifications,
        200,
      );
    } catch (error) {
      return ResponseUtil.error(
        'No se pudieron obtener las aclaraciones',
        error,
        400,
      );
    }
  }

  async findByTrackingNumber(trackingNumber: string): Promise<ApiResponseDto> {
    try {
      const clarification = await this.clarificationRepository.findOne({
        where: { trackingNumber },
        relations: ['createdBy', 'assignedAdmin'],
      });

      if (!clarification) {
        return ResponseUtil.error(
          'Aclaración no encontrada',
          `No se encontró ninguna aclaración con el número ${trackingNumber}`,
          404,
        );
      }

      return ResponseUtil.success(
        'Aclaración obtenida exitosamente',
        clarification,
        200,
      );
    } catch (error) {
      return ResponseUtil.error('Error al buscar aclaración', error, 400);
    }
  }

  async update(
    trackingNumber: string,
    data: UpdateClarificationDto,
  ): Promise<ApiResponseDto> {
    try {
      const clarification = await this.clarificationRepository.findOne({
        where: { trackingNumber },
      });

      if (!clarification) {
        return ResponseUtil.error(
          'No se encontró la aclaración',
          'ID inválido',
          404,
        );
      }

      await this.clarificationRepository.update(trackingNumber, {
        ...data,
        assignedAdmin: data.assignedAdmin
          ? { id: data.assignedAdmin }
          : undefined,
      });
      const updated = await this.clarificationRepository.findOne({
        where: { trackingNumber },
      });

      return ResponseUtil.success(
        'Aclaración actualizada exitosamente',
        updated,
        200,
      );
    } catch (error) {
      return ResponseUtil.error(
        'Error al actualizar la aclaración',
        error,
        400,
      );
    }
  }

  async remove(trackingNumber: string): Promise<ApiResponseDto> {
    try {
      const result = await this.clarificationRepository.delete({
        trackingNumber,
      });
      return ResponseUtil.success(
        'Aclaración eliminada exitosamente',
        result,
        200,
      );
    } catch (error) {
      return ResponseUtil.error(
        'No se pudo eliminar la aclaración',
        error,
        400,
      );
    }
  }
}
