import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import { AdminDocuments } from "../../domain/entities/admin-documents.entity";
import { AdminDocumentsRepository } from "../../domain/repository/admin-documents.repository";

export class AdminDocumentsRepositoryImpl implements AdminDocumentsRepository {

    constructor(
        @InjectRepository(AdminDocuments)
        private readonly repository: Repository<AdminDocuments>,
    ) { }

    async getById(id: number){
        return this.repository.createQueryBuilder('adminDocuments')
            .where('adminDocuments.id = :id', { id })
            .leftJoinAndSelect('adminDocuments.file', 'file')
            .getOne();
    }

    async deleteById(id: number): Promise<void> {
        await this.repository.delete({ id });
    }
}