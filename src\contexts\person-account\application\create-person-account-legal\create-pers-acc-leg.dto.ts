import {
  IsString,
  <PERSON><PERSON><PERSON>,
  IsNotEmpty,
  IsOptional,
  IsArray,
  ValidateNested,
  IsInt,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateLegPersAccDto {
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  legal_name: string;

  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  password: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  rfc?: string;

  @IsString()
  @IsNotEmpty()
  dialing_code: string;

  @IsString()
  @IsNotEmpty()
  area_code: string;

  @IsString()
  @IsNotEmpty()
  number: string;

  @IsString()
  @IsNotEmpty()
  postal_code: string;

  @IsString()
  @IsNotEmpty()
  suffix: string;

  @IsString()
  @IsNotEmpty()
  street: string;

  @IsString()
  @IsNotEmpty()
  addresses_number: string;

  @IsString()
  @IsNotEmpty()
  city: string;

  @IsString()
  @IsNotEmpty()
  country_code: string;
}

class DocumentDto {
  @IsInt()
  type_id: number;

  @IsString()
  @IsNotEmpty()
  number: string;

  @IsNotEmpty()
  is_main: boolean;

  @IsString()
  @IsNotEmpty()
  country_code: string;
}

class PhoneDto {
  @IsInt()
  type_id: number;

  @IsNotEmpty()
  is_main: boolean;

  @IsString()
  @IsNotEmpty()
  dialing_code: string;

  @IsString()
  @IsNotEmpty()
  area_code: string;

  @IsString()
  @IsNotEmpty()
  number: string;

  @IsString()
  @IsNotEmpty()
  country_code: string;
}

class AddressDto {
  @IsInt()
  type_id: number;

  @IsNotEmpty()
  is_main: boolean;

  @IsString()
  @IsNotEmpty()
  postal_code: string;

  @IsString()
  @IsNotEmpty()
  suffix: string;

  @IsString()
  @IsNotEmpty()
  street: string;

  @IsString()
  @IsNotEmpty()
  number: string;

  @IsString()
  @IsNotEmpty()
  city: string;

  @IsString()
  @IsNotEmpty()
  country_code: string;
}

class EmailDto {
  @IsInt()
  type_id: number;

  @IsNotEmpty()
  is_main: boolean;

  @IsEmail()
  @IsNotEmpty()
  email: string;
}

export class LegalPersonDataDto {
  @IsInt()
  status_id: number;

  @IsString()
  @IsNotEmpty()
  legal_name: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DocumentDto)
  documents: DocumentDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PhoneDto)
  phones: PhoneDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AddressDto)
  addresses: AddressDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EmailDto)
  emails: EmailDto[];
}

export class AccountLegDataDto {
  @IsString()
  @IsNotEmpty()
  product_id: string;

  @IsString()
  @IsNotEmpty()
  person_id: string;
}

export class ParamsSpendingGroupDto {
  @IsString()
  @IsNotEmpty()
  group_id: string;

  @IsString()
  @IsNotEmpty()
  person_id: string;
}
