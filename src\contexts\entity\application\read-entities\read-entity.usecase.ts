import { Injectable } from '@nestjs/common';
import { EntityRepositoryImpl } from 'src/contexts/entity/infrastructure/repositories/entity.repository.impl';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';

@Injectable()
export class ReadEntityUseCase {
  constructor(private readonly entityRepositoryImpl: EntityRepositoryImpl) {}

  async execute(id: string): Promise<ApiResponseDto> {
    return this.entityRepositoryImpl.findById(id);
  }
}
