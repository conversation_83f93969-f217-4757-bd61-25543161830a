{"name": "finberry-back", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js -d src/contexts/shared/infrastructure/database/data-source.ts", "migrations:generate": "npm run typeorm -- migration:generate src/contexts/shared/infrastructure/database/migrations/$npm_config_name", "migrations:run": "npm run typeorm -- migration:run", "migrations:show": "npm run typeorm -- migration:show", "migrations:revert": "npm run typeorm -- migration:revert"}, "dependencies": {"@aws-sdk/client-s3": "^3.750.0", "@aws-sdk/s3-request-presigner": "^3.808.0", "@nestjs-modules/ioredis": "^2.0.2", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^3.1.3", "@nestjs/bull": "^11.0.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^8.1.1", "@nestjs/typeorm": "^10.0.2", "argon2": "^0.41.1", "axios": "^1.8.3", "bull": "^4.16.5", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "decimal.js-light": "^2.5.1", "finberry-back": "file:", "handlebars": "^4.7.8", "ioredis": "^5.6.0", "luxon": "^3.6.1", "multer": "^1.4.5-lts.2", "nodemailer": "^6.10.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.13.1", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "typeorm": "^0.3.20"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bull": "^3.15.9", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/luxon": "^3.6.1", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}