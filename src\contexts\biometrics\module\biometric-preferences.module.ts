import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BiometricPreference } from '../domain/entities/biometric-preference.entity';
import { BiometricPreferencesRepository } from '../infrastructure/repository/biometric-preferences.repository';
import { UpdateBiometricPreferencesUseCase } from '../application/update-biometric-preferences/update-biometric-preferences.usecase';
import { GetBiometricPreferencesUseCase } from '../application/get-biometric-preferences/get-biometric-preferences.usecase';
import { BiometricPreferencesController } from '../infrastructure/http/biometric-preferences.controller';

@Module({
  imports: [TypeOrmModule.forFeature([BiometricPreference])],
  controllers: [BiometricPreferencesController],
  providers: [
    BiometricPreferencesRepository,
    UpdateBiometricPreferencesUseCase,
    GetBiometricPreferencesUseCase,
  ],
})
export class BiometricPreferencesModule {}