import { Injectable, NotFoundException } from '@nestjs/common';
import { StorageS3Utils } from 'src/contexts/shared/utils/storageUtils/storageS3.utils';
import { ClarificationFile } from '../../../domain/entities/clarification-file.entity';
import { ClarificationFileRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/clarification-file.repository.impl';
import { ClarificationRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/clarification.repository.impl';

interface UploadClarificationFileInput {
  trackingNumber: string;
  files: Express.Multer.File[];
}

@Injectable()
export class UploadClarificationFileUseCase {
  constructor(
    private readonly fileRepo: ClarificationFileRepositoryImpl,
    private readonly clarificationRepo: ClarificationRepositoryImpl,
    private readonly s3: StorageS3Utils,
  ) {}

  async executeMultiple({
    trackingNumber,
    files,
  }: UploadClarificationFileInput): Promise<ClarificationFile[]> {
    const clarification =
      await this.clarificationRepo.findByTrackingNumber(trackingNumber);

    if (!clarification) {
      throw new NotFoundException('Clarification not found');
    }

    const uploaded = await Promise.all(
      files.map(async (file) => {
        const path = `documentos/clarifications/${trackingNumber}`;
        const { key } = await this.s3.uploadFileToPath(file, path);

        const entity = this.fileRepo.create({
          clarification: { trackingNumber } as any,
          file_name: file.originalname,
          file_url: key,
        });

        return this.fileRepo.save(entity);
      }),
    );

    return uploaded;
  }
}
