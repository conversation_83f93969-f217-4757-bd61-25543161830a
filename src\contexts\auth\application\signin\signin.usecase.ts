import {
  ConflictException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { SigninDto } from './signin.dto';
import { JwtService } from '@nestjs/jwt';
import * as argon2 from 'argon2';
import { InjectRepository } from '@nestjs/typeorm';
import { Users } from 'src/contexts/users/domain/entities/users.entity';
import { Repository } from 'typeorm';
import { RedisService } from 'src/contexts/shared/utils/Redis/redis';
import { ExpiresIn } from 'src/contexts/shared/interfaces/dtos/redis.dto';
import { SiginVO } from './signin.vo';
import { RoleEnum } from 'src/contexts/shared/enums/roles.enum';

@Injectable()
export class SigninUseCase {
  constructor(
    @InjectRepository(Users)
    private readonly userRepository: Repository<Users>,
    private readonly jwtService: JwtService,
    private readonly redis: RedisService,
  ) {}

  async signin({ email, password, origin }: SigninDto): Promise<SiginVO> {
    email = email.toLowerCase();

    const user = await this.getUser(email);
    if (!user) throw new NotFoundException('Usuario no encontrado.');

    if (user.isDeleted) throw new ConflictException('Usuario eliminado.');

    if (
      origin === 'mobile' &&
      user.relUserRoleAdmins[0]?.role?.name === RoleEnum.CLIENTE_ADMIN
    ) {
      throw new UnauthorizedException(
        'No autorizado para acceder desde la app móvil',
      );
    }

    await this.validateLoginAttempts(user);

    const isPasswordValid = await argon2.verify(user.password, password);
    if (!isPasswordValid) {
      const attemptsKey = 'failed_login:'.concat(email);
      const failedAttempts = (await this.redis.getCache(attemptsKey)) || 0;

      await this.redis.setCache({
        key: attemptsKey,
        value: (Number(failedAttempts) + 1).toString(),
        expiresIn: ExpiresIn.FIFTY_MINUTES,
      });

      throw new ConflictException('Contraseña inválida.');
    }

    return this.buildToken(user);
  }

  async buildToken(user: Users): Promise<SiginVO> {
    const token = this.jwtService.sign({ id: user.id });
    delete user.password;
    return { token, user };
  }

  private async getUser(email: string): Promise<Users | null> {
    return await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.relUserRoleAdmins', 'relUserRoleAdmins')
      .leftJoinAndSelect('relUserRoleAdmins.role', 'role')
      .leftJoinAndSelect('relUserRoleAdmins.admin', 'admin')
      .where('user.email = :email', { email })
      .getOne();
  }

  async validateLoginAttempts(user: Users): Promise<void> {
    const attemptsKey = 'failed_login:'.concat(user.email.toLowerCase());
    const failedAttempts = (await this.redis.getCache(attemptsKey)) || 0;

    if (+failedAttempts >= 3) {
      throw new ConflictException(
        'Cuenta bloqueada temporalmente por intentos fallidos.',
      );
    }
  }
}
