import { MigrationInterface, QueryRunner } from 'typeorm';

export class $npmConfigName1750695769618 implements MigrationInterface {
  name = ' $npmConfigName1750695769618';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_transfer" ADD "sign_transfer" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_transfer" ADD "error_transfer" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_transfer" DROP COLUMN "error_transfer"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_transfer" DROP COLUMN "sign_transfer"`,
    );
  }
}
