import { MigrationInterface, QueryRunner } from "typeorm";

export class  $npmConfigName1747673001100 implements MigrationInterface {
    name = ' $npmConfigName1747673001100'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "transfer_contact" ADD "email" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "transfer_contact" DROP COLUMN "email"`);
    }

}
