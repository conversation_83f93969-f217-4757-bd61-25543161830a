import { ApiResponseDto } from "src/contexts/shared/interfaces/dtos/api-response.dto";
import { CreateAddressDto } from "../../application/usecases-address/create-address/create-address.dto";
import { UpdateAddressDto } from "../../application/usecases-address/update-address/update-address.dto";

export interface AddressRepository {
  save(entity: CreateAddressDto): Promise<ApiResponseDto>;
  findAll(): Promise<ApiResponseDto>;
  findById(id: string): Promise<ApiResponseDto>;
  update(id: string, entity: UpdateAddressDto): Promise<ApiResponseDto>;
  remove(id: string): Promise<ApiResponseDto>;
}