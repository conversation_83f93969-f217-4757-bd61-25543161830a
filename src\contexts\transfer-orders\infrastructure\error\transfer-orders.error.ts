import { CustomError } from 'src/contexts/shared/errors/custom-error';

export class ERROR_CHECK_ORDERS extends CustomError {
  constructor(message: string) {
    super(message);
  }
}

export class ERROR_CREATE_ORDERS extends CustomError {
  constructor(message: string) {
    super(message);
  }
}

export class ERROR_GET_BANKS extends CustomError {
  constructor(message: string) {
    super(message);
  }
}

export class ERROR_FILTER_BANK extends CustomError {
  constructor(message: string) {
    super(message);
  }
}
