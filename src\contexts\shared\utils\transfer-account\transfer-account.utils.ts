import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/* ** DTOs ** */
import {
  ParamsCreateClabeDto,
  ParamsTransferDigitVerifierDto,
  ResponseClabeDto,
  ResponseCreateClabeDto,
} from '../../interfaces/dtos/transfer-account.dto';

/* ** Repository ** */
import { AccountTransferRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/account.transfer.repository.impl';

@Injectable()
export class TransferAccount {
  /* ** Constructor ** */
  constructor(
    private readonly config: ConfigService,
    private readonly account: AccountTransferRepositoryImpl,
  ) {}

  /* ** Create a new account ** */
  async createAccount(
    params: ParamsCreateClabeDto,
  ): Promise<ResponseCreateClabeDto> {
    for (let attempt = 0; attempt < 60; attempt++) {
      const clabe = this.generateClabe();

      const exists = await this.account.findClabe(clabe.account_clabe);

      // Verify if the CLABE already exists in the database
      if (!exists) {
        const save_clabe = await this.account.saveAccount({
          clabe: clabe.account_clabe,
          person_id: params.person_id,
          limit_id: params.limit_id,
        });

        return save_clabe;
      }
    }

    throw new Error(
      'No se pudo generar una CLABE única después de 20 intentos.',
    );
  }

  /* ** Generate Cuenta Clabe ** */
  private generateClabe(): ResponseClabeDto {
    /* ** Get default params CLABE ** */
    const banck_code = this.config.get<string>('TRANSFER_BANK');
    const branch_location = this.config.get<string>('TRANSFER_BRANCH_LOCATION');
    const product_type = this.config.get<string>('TRANSFER_ACCOUNT_TYPE');

    /* ** Generate the CLABE ** */
    const numero = Array(8)
      .fill(0)
      .map(() => Math.floor(Math.random() * 10)) // Genera un dígito aleatorio entre 0 y 9
      .join(''); // Une los dígitos en una cadena

    const clabe = `${banck_code}${branch_location}${product_type}${numero}`;

    /* ** Calculate the digit verifier ** */
    const digit_verifier = this.verifyTransferDigit({ clabe });

    /* ** Add the digit verifier to the CLABE ** */
    const clabe_with_digit_verifier = `${clabe}${digit_verifier}`;

    return { account_clabe: clabe_with_digit_verifier };
  }

  /* ** Generate the digit verifier for the clabe ** */
  private verifyTransferDigit(params: ParamsTransferDigitVerifierDto): number {
    const { clabe } = params;

    /* ** Get Modulo 10 ** */
    const value_modulo10 = this.config.get<number>('MODULO_10') || 10;

    /* ** Generate an array of numbers from the clabe ** */
    const clabeArray = clabe.split('').map(Number);

    /* ** Generate the array of weights ** */
    const weigth = this.config.get<string>('TRANSFER_WEIGHT') || '';
    const weightArray = weigth.split('').map(Number);

    /* ** STEP 1 - Multiply each digit of the account number by the respective weighting factor ** */
    const multiplied = clabeArray.map((num, index) => num * weightArray[index]);

    /* ** STEP 2 - Take modulo 10 of each result obtained in step 1 ** */
    const modul10 = multiplied.map((num) => num % value_modulo10);

    /* ** STEP 3 - Add the results of each of the modulo operations performed in step 2. ** */
    const sum = modul10.reduce((acc, num) => acc + num, 0);

    /* ** STEP 4 - Take the modulo 10 of the sum calculated in step 3 ** */
    const sum_modulo10 = sum % value_modulo10;

    /* ** STEP 5 - Take the value A obtained in step 4 and subtract it from 10 ** */
    const digit = value_modulo10 - sum_modulo10;

    /* ** STEP 6 -  The Check Digit is the result of obtaining the modulo 10 of the number B calculated in step 5 ** */
    const digit_verifier = digit % value_modulo10;

    /* ** Return the digit verifier ** */
    return digit_verifier;
  }
}
