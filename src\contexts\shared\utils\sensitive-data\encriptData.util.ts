import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import axios from 'axios';
import * as https from 'https';

/* ** Utils ** */
import { authTokenDock } from '../authDock/authDock';

/* ** DTOs ** */
import {
  ResponseEncryptDto,
  ParamsEncryptDto,
  HeaderPublicKetDto,
  EncryptDataAESDto,
  ResponseEncryptDataAESDto,
} from '../../interfaces/dtos/sensitive-data.dto';

@Injectable()
export class EncryptData {
  constructor(
    private readonly configService: ConfigService,
    private readonly authTokenDock: authTokenDock,
  ) {}

  async encryptData(data: ParamsEncryptDto): Promise<ResponseEncryptDto> {
    /* ** Get Public Key ** */
    const public_key = await this.getPublicKey();

    /* ** Encrypt Data ** */
    const { aes, encrypt_data, iv } = this.encryptDataWithAES({
      body_text: data.encrypted_data,
    });

    /* ** Encrypt AES Key with RSA ** */
    const encryptedAESKeyBase64 = crypto
      .publicEncrypt(
        {
          key: Buffer.from(public_key, 'base64'),
          padding: crypto.constants.RSA_PKCS1_OAEP_PADDING, // Usar padding OAEP
          oaepHash: 'sha256', // Usar SHA-256 como función hash
        },
        aes, // Cifrar la clave AES
      )
      .toString('base64');

    return {
      iv,
      aes: encryptedAESKeyBase64,
      encrypted_data: encrypt_data,
      mode: this.configService.get('ENCRYPT_MODE'),
    };
  }

  private async getPublicKey(): Promise<string> {
    /* ** Get Certificate ** */
    const auth = await this.authTokenDock.getAuthDock();

    /* ** Agent ** */
    const httpsAgent = new https.Agent({
      cert: auth.certificate,
      key: auth.key,
      rejectUnauthorized: false,
    });

    /* ** Get Public Key ** */
    const headers = this.getHeaders(auth.bearer_token);
    const { data } = await axios.get(
      `${this.configService.get('DOCK_URL_GLOBAL')}/cards/v1/keys/rsa/public`,
      {
        headers: { ...headers },
        httpsAgent,
      },
    );

    return data.public_key;
  }

  private getHeaders(token: string): HeaderPublicKetDto {
    return {
      Authorization: `Bearer ${token}`,
      Accept: 'application/json',
      'Content-Type': 'application/json',
    };
  }

  private encryptDataWithAES(
    params: EncryptDataAESDto,
  ): ResponseEncryptDataAESDto {
    /* ** Generar una clave AES de 256 bits (32 bytes) ** */
    const aes = crypto.randomBytes(32);
    /* ** Generar un vector de inicialización de 12 bits ** */
    const iv = crypto.randomBytes(12);
    /* ** Cipher data AES-GCM ** */
    const cipher = crypto.createCipheriv('aes-256-gcm', aes, iv);

    let encrypted = cipher.update(params.body_text, 'utf-8', 'hex');
    encrypted += cipher.final('hex');

    // Obtener el tag de autenticación
    const tag = cipher.getAuthTag();

    // Combinar los datos cifrados y el tag de autenticación en un solo resultado y pasarlo a base64
    const result = Buffer.from(encrypted + tag.toString('hex'), 'hex').toString(
      'base64',
    );

    return {
      encrypt_data: result,
      aes,
      iv: Buffer.from(iv).toString('base64'),
    };
  }
}
