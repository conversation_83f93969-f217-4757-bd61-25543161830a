import { ApiResponseDto } from "src/contexts/shared/interfaces/dtos/api-response.dto";
import { CreateAccountDto } from "../../application/usecases-account/create-account/create-account.dto";
import { UpdateAccountDto } from "../../application/usecases-account/update-account/update-account.dto";

export interface AccountRepository {
  save(accounts: CreateAccountDto): Promise<ApiResponseDto>;
  findAll(): Promise<ApiResponseDto>;
  findById(id: string): Promise<ApiResponseDto>;
  update(id: string, account: UpdateAccountDto): Promise<ApiResponseDto>;
  remove(id: string): Promise<ApiResponseDto>;
}