import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { join } from 'path';
import { EmailSubscriber } from './events/user.events';

export const DatabaseProviders = [
  TypeOrmModule.forRootAsync({
    imports: [ConfigModule],
    useFactory: (configService: ConfigService) => ({
      type: 'postgres',
      host: configService.get<string>('DB_HOST'),
      port: configService.get<number>('DB_PORT'),
      username: configService.get<string>('DB_USERNAME'),
      password: configService.get<string>('DB_PASSWORD'),
      database: configService.get<string>('DB_NAME'),
      entities: [join(__dirname, '../../../**/**/**/**/*.entity.{ts,js}')],
      subscribers: [EmailSubscriber],
      synchronize: false,
      ssl: {
        rejectUnauthorized: false,
      },
    }),
    inject: [ConfigService],
  }),
];
