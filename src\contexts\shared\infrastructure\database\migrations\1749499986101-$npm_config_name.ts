import { MigrationInterface, QueryRunner } from "typeorm";

export class  $npmConfigName1749499986101 implements MigrationInterface {
    name = ' $npmConfigName1749499986101'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "bulk_transfers_error" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "errorMessage" character varying NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "beneficiatyAccount" character varying, "bulkTransferId" uuid, CONSTRAINT "PK_d3c9b3a7e5181c03745079e2975" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bulk_transfers" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "total" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "status" character varying NOT NULL DEFAULT 'PENDING', "successCount" integer NOT NULL DEFAULT '0', "failureCount" integer NOT NULL DEFAULT '0', "creationUserId" uuid, "fileId" integer, CONSTRAINT "REL_6d06d8e63c917aa8054817be2e" UNIQUE ("fileId"), CONSTRAINT "PK_aa7da335c36fddc0ddad1fde6a0" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "bulk_transfers_error" ADD CONSTRAINT "FK_cdfc082a41b1b4df8dcf0bbef7c" FOREIGN KEY ("bulkTransferId") REFERENCES "bulk_transfers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bulk_transfers" ADD CONSTRAINT "FK_600091cfa34a923f1c3e5969b27" FOREIGN KEY ("creationUserId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "bulk_transfers" ADD CONSTRAINT "FK_6d06d8e63c917aa8054817be2e6" FOREIGN KEY ("fileId") REFERENCES "s3_metadata"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "bulk_transfers" DROP CONSTRAINT "FK_6d06d8e63c917aa8054817be2e6"`);
        await queryRunner.query(`ALTER TABLE "bulk_transfers" DROP CONSTRAINT "FK_600091cfa34a923f1c3e5969b27"`);
        await queryRunner.query(`ALTER TABLE "bulk_transfers_error" DROP CONSTRAINT "FK_cdfc082a41b1b4df8dcf0bbef7c"`);
        await queryRunner.query(`DROP TABLE "bulk_transfers"`);
        await queryRunner.query(`DROP TABLE "bulk_transfers_error"`);
    }

}
