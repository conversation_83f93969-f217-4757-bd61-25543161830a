import { Column, <PERSON>tity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';

/** Entitys */
import { AccountTransfer } from './account-transfer.entity';
import { Account } from './account.entity';
import { Users } from './users.entity';

@Entity()
export class Person {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  personExtID: string;

  @OneToMany(() => Users, user => user.personIDDock)
  users: Users[];

  @Column({ default: true })
  enabled: boolean;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @OneToMany(() => AccountTransfer, (transfer) => transfer.person)
  transfers?: AccountTransfer[];

  @OneToMany(() => Account, account => account.personIDDock)
  accounts?: Account[];
}
