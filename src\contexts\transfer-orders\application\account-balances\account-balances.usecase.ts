import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

/* ** DTOs ** */
import { ResponseAccountBalancesDto } from './account-balances.dto';

@Injectable()
export class AccountBalances {
  constructor(private readonly config: ConfigService) {}

  async getAccountBalances(): Promise<ResponseAccountBalancesDto> {
    try {
      const url = `${this.config.get('TRANSFER_URL')}/api/1.0/balances/`;
      const response = await axios.post(
        url,
        { account: this.config.get('DOCK_OPERATING_CLABE_ID') },
        {
          headers: {
            'X-Custom-Auth': this.config.get('TRANSFER_TOKEN'),
            Accept: 'application/json',
          },
        },
      );
      return response.data;
    } catch (error) {
      throw new BadRequestException(
        error.response?.data || 'Error fetching account balances',
      );
    }
  }
}
