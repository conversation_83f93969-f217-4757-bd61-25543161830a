import { Process, Processor } from "@nestjs/bull";
import { InjectRepository } from "@nestjs/typeorm";
import { BulkTransfers, BulkTransfersStatus } from "src/contexts/users/domain/entities/bulk-transfers.entity";
import { Repository } from "typeorm";
import { ParamsDockPayTransfertDto } from "../../application/create-transaction/dockpay-transfer.dto";
import { BulkTransfersError } from "src/contexts/users/domain/entities/bulk-transfers-errors.entity";
import { Job } from "bull";
import { Logger } from "@nestjs/common";
import { DockpayTransferUseCase } from "../../application/create-transaction/dockpay-transfer.usecase";
import { CheckOrders } from "src/contexts/transfer-orders/application/check-orders/check-orders.usecase";

@Processor('bulk-transfers')
export class BulkTransferProcessor {
  private readonly logger = new Logger(BulkTransferProcessor.name);

  constructor(
    @InjectRepository(BulkTransfers)
    private bulkTransfersRepository: Repository<BulkTransfers>,
    @InjectRepository(BulkTransfersError)
    private bulkTransfersErrorRepository: Repository<BulkTransfersError>,
    private readonly dockpayTransferUseCase: DockpayTransferUseCase,
    private readonly checkBanks: CheckOrders
  ) {
    this.logger.log('🎯 BulkTransferProcessor initialized');
  }

  @Process('process')
  async handleTransfer(job: Job<{bulkId: string, transfers: ParamsDockPayTransfertDto[]}>) {
    this.logger.log(`🚀 Starting job processing: ${job.id}`);
    
    const { bulkId, transfers } = job.data;
    
    this.logger.log(`Processing bulk transfer ID: ${bulkId} with ${transfers.length} transfers`);
    
    try {
      const bulk = await this.bulkTransfersRepository.findOne({
        where: { id: bulkId }
      });

      if (!bulk) {
        throw new Error(`Bulk transfer with ID ${bulkId} not found`);
      }

      this.logger.log(`Found bulk transfer: ${bulk.id}`);

      bulk.status = BulkTransfersStatus.PROCESSING;
      await this.bulkTransfersRepository.save(bulk);
      this.logger.log(`Updated status to PROCESSING`);

      const validSatusCode = [200, 201, 202, 204];
      let processedCount = 0;

      const banks = await this.checkBanks.getAllBanks();
      
      for (const transfer of transfers) {
        try {
          this.logger.log(`Processing transfer ${processedCount + 1}/${transfers.length} for CLABE: ${transfer.num_clabe}`);

          if(transfer.num_clabe.length === 18){
            const code = transfer.num_clabe.slice(0, 3);
            const bank = banks.banks.find(bank => bank.legalCode === code);

            if (!bank) {
              throw new Error('Banco no encontrado para la CLABE proporcionada');
            }
            
            transfer.NameBank = bank.name;
            transfer.legalBank = bank.code;
          }

          const response = await this.dockpayTransferUseCase.executePay(transfer);

          if( !validSatusCode.includes(response.statusCode)) {
            throw new Error(response.message || 'Error al procesar la transferencia');
          }
        
          bulk.successCount++;
          this.logger.log(`✅ Transfer successful for CLABE: ${transfer.num_clabe}`);

        } catch (error) {
          bulk.failureCount++;
          this.logger.error(`❌ Transfer failed for CLABE: ${transfer.num_clabe}`, error.message);
          
          const bulkError = this.bulkTransfersErrorRepository.create({
            errorMessage: error.message,
            bulkTransfer: bulk,
            beneficiatyAccount: transfer.num_clabe
          });
          await this.bulkTransfersErrorRepository.save(bulkError);
        }
        
        processedCount++;
      }

      bulk.status = BulkTransfersStatus.COMPLETED;
      await this.bulkTransfersRepository.save(bulk);
      
      this.logger.log(`🎉 Bulk transfer completed. Success: ${bulk.successCount}, Failures: ${bulk.failureCount}`);
      
    } catch (error) {
      this.logger.error(`💥 Bulk transfer processing failed for ID: ${bulkId}`, error.message);
      throw error;
    }
  }
}