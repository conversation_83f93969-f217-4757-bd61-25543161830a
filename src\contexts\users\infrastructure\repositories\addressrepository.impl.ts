import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';
import { AddressRepository } from '../../domain/repository/address.repository';
import { CreateAddressDto } from '../../application/usecases-address/create-address/create-address.dto';
import { Address } from '../../domain/entities/address.entity';
import { UpdateAddressDto } from '../../application/usecases-address/update-address/update-address.dto';

export class AddressRepositoryImpl implements AddressRepository {
  constructor(
    @InjectRepository(Address)
    private readonly addressRepository: Repository<Address>,
  ) {}

  async save(address: CreateAddressDto): Promise<ApiResponseDto> {
    try {
      const addressEntity = this.addressRepository.create(address);
      const res = await this.addressRepository.save(addressEntity);
      return ResponseUtil.success('Usuario creado exitosamente', res, 201);
    } catch (error) {
      return ResponseUtil.error('No se pudo crear el usuario', error, 400);
    }
  }

  async findAll(): Promise<ApiResponseDto> {
    try {
      const res = await this.addressRepository.find();
      return ResponseUtil.success('Usuarios obtenidos exitosamente', res, 200);
    } catch (error) {
      return ResponseUtil.error(
        'No se pudieron obtener los usuarios',
        error,
        400,
      );
    }
  }

  async findById(id: string): Promise<ApiResponseDto> {
    try {
      const res = await this.addressRepository.findOne({ where: { id } });
      if (!res) {
        return ResponseUtil.error(
          'Dirección no encontrada',
          'Dirección con el ID proporcionado no existe',
          404,
        );
      }
      return ResponseUtil.success('Usuario obtenido exitosamente', res, 200);
    } catch (error) {
      return ResponseUtil.error('No se pudo obtener el usuario', error, 400);
    }
  }

  async update(id: string, address: UpdateAddressDto): Promise<ApiResponseDto> {
    try {
      const res = await this.addressRepository.update(id, address);
      return ResponseUtil.success('Usuario actualizado exitosamente', res, 200);
    } catch (error) {
      return ResponseUtil.error('No se pudo actualizar el usuario', error, 400);
    }
  }

  async remove(id: string): Promise<ApiResponseDto> {
    try {
      const res = await this.addressRepository.delete(id);
      return ResponseUtil.success('Usuario eliminado exitosamente', res, 200);
    } catch (error) {
      return ResponseUtil.error('No se pudo eliminar el usuario', error, 400);
    }
  }
}
