import { Injectable } from '@nestjs/common';
import { AdminParser } from 'src/contexts/users/infrastructure/parsers/admin.parser';
import { AdminRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/adminrepository.impl';
import {
  AdminListVO,
  BasicAdminVO,
} from 'src/contexts/users/infrastructure/vo/admin.vo';

import { UpdateLegPersAccUseCase } from 'src/contexts/person-account/application/update-person-account-legal/update-person-account-legal.usecase';
import { AdminFilterDto } from './read-admins-filter.dto';
import { DockLegalPersonService } from 'src/contexts/dock/infraestructure/services/dock-legal-person.service';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { RoleEnum } from 'src/contexts/shared/enums/roles.enum';
import { ReadAminTotalAmountUseCase } from '../read-total-amount/read-total-amount.usecase';
import { AccountCardRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/account-card.repository.impl';

@Injectable()
export class ReadAdminsUseCase {
  constructor(
    private readonly adminRepositoryImpl: AdminRepositoryImpl,
    readonly updateLegalPersonUsecase: UpdateLegPersAccUseCase,
    readonly dockLegalPersonService: DockLegalPersonService,
    private readonly readAminTotalAmountUseCase: ReadAminTotalAmountUseCase,
    private readonly ccountCardRepositoryImpl: AccountCardRepositoryImpl
  ) {}

  async readAll(filter: AdminFilterDto): Promise<AdminListVO> {
    const [admin, count] = await this.adminRepositoryImpl.findAll(filter);

    const admins = [];

    for(const a of admin){

      const amount = await this.readAminTotalAmountUseCase.execute({adminId: a.id})
      const totalCards = await this.ccountCardRepositoryImpl.countCardsByAdminId(a.id);

      const adminParsed = AdminParser.parseAdminToList(a, amount.totalAmount);
      adminParsed.numAsignedCards = totalCards;
      admins.push(adminParsed);
    }

    return { admins, count };
  }

  async readAllBasic(): Promise<BasicAdminVO[]> {
    const admin = await this.adminRepositoryImpl.findAllBasic();
    
    let data: BasicAdminVO[] = [];

    for (const a of admin) {
      const adminParsed = AdminParser.parseBasicAdmin(a);
      const totalCards = await this.ccountCardRepositoryImpl.countCardsByAdminId(a.id);
      adminParsed.numAsignedCards = totalCards;
      data.push(adminParsed);
    }

    return data;
  }

  async readAllByUserId(userId: string, role: RoleEnum): Promise<ApiResponseDto> {
    const admin = await this.adminRepositoryImpl.findAllByUserId(userId, role);

    // const admins = admin.map(AdminParser.parseAdminToList);

    return {
      message: 'Admins fetched successfully',
      statusCode: 200,
      data: { admin },
    };
  }
}
