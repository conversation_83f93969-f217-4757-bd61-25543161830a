import { Controller, Body, Post, Get, Query, UseGuards } from '@nestjs/common';

/* ** Import useCases ** */
import { CreateCardEmbossingUseCase } from '../../application/create-embossing/create-embossing.usecase';
import { ReadEmbossingUseCase } from '../../application/read-embossing/read-embossing.usecase';

/* ** DTOs ** */
import { CreateCardEmbossingDto } from '../../application/create-embossing/create-embossing.dto';
import { ReadEmbossingParamsDto } from '../../application/read-embossing/read-embossing.dto';

/* ** Utils ** */
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';
import { JwtAuthGuard } from 'src/contexts/auth/guards/auth.guard';

@UseGuards(JwtAuthGuard)
@Controller('card-embossing')
export class CardEmbossingController {
  constructor(
    private readonly embossingUseCase: CreateCardEmbossingUseCase,
    private readonly readEmbossing: ReadEmbossingUseCase,
  ) {}

  @Post('generate-embossing-file')
  async createCardEmbossing(@Body() cardEmbossing: CreateCardEmbossingDto) {
    try {
      return await this.embossingUseCase.executeEmbossing(cardEmbossing);
    } catch (e) {
      return ResponseUtil.error(
        e.message,
        {
          data: {
            statusCode: e.status,
            message: String(e.response.data.error),
            error: e.name,
          },
        },
        e.status,
      );
    }
  }

  @Get('list-embossing-files')
  async readFileEmbossing(@Query() cardEmbossing: ReadEmbossingParamsDto) {
    try {
      return await this.readEmbossing.readEmbossing(cardEmbossing);
    } catch (e) {
      console.log(e);
      return ResponseUtil.error(
        e.message,
        {
          data: {
            statusCode: e.status,
            message: String(e.response.data.error),
            error: e.name,
          },
        },
        e.status,
      );
    }
  }
}
