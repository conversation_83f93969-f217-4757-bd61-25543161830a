import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { ReadUserUseCase } from '../../usecases-user/read-user/read-user.usecase';
import { Injectable } from '@nestjs/common';
import { TransferContactRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/transfer-contact.repository.impl';

@Injectable()
export class ReadTransferContactUseCase {
  constructor(
    private readonly transferContactRepository: TransferContactRepositoryImpl,
    private readonly readUserUseCase: ReadUserUseCase,
  ) {}

  async executeFind(idUser?: string): Promise<ApiResponseDto> {
    if (!idUser) {
      return {
        statusCode: 400,
        error: 'User idUser or email is required',
        message: 'User idUser or email is required',
      };
    }
    return await this.transferContactRepository.findByUser(idUser);
  }

  async executeFindAll(): Promise<ApiResponseDto> {
    return await this.transferContactRepository.findAll();
  }
}
