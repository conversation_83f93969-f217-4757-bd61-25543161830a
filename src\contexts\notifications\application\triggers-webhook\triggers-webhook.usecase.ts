import { Injectable, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import * as https from 'https';

/* ** DTOs ** */
import { TriggersTestNotificationDto } from './triggers-webhook.dto';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';

/* ** Utils ** */
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';

@Injectable()
export class TriggerTestNotificationUseCase {
  constructor(
    private readonly configService: ConfigService,
    private readonly authDock: authTokenDock,
  ) {}

  async triggerNotification(
    body: TriggersTestNotificationDto,
  ): Promise<ApiResponseDto> {
    /* ** Obtenemos las credenciales de autenticación ** */
    const auth = await this.authDock.getAuthDock();

    // Configurar el agente HTTPS con los certificados
    const httpsAgent = new https.Agent({
      cert: auth.certificate,
      key: auth.key,
      rejectUnauthorized: false,
    });

    /* ** Creamos la persona natural ** */
    const { data: trigger } = await axios.post(
      `${this.configService.get('DOCK_URL_GLOBAL')}/notifications/v1/trigger`,
      body,
      {
        headers: {
          Authorization: `Bearer ${auth.bearer_token}`,
          'Content-Type': 'application/json',
        },
        httpsAgent,
      },
    );

    return ResponseUtil.success(
      'Notification triggered successfully',
      { response: trigger },
      HttpStatus.OK,
    );
  }
}
