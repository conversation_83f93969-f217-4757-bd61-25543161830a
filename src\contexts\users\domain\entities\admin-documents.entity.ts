import { BaseEntity, Column, Entity, <PERSON>in<PERSON><PERSON>umn, ManyToOne, OneToOne, PrimaryColumn, PrimaryGeneratedColumn } from "typeorm";
import { S3Metadata } from "./s3-metadata.entity";
import { Admin } from "./admin.entity";

export enum AdminDocumentTypeEnum {
    OTHER = 'OTHER',
    ARTICLES_OF_INCORPORATION = 'ARTICLES_OF_INCORPORATION',
    CONSTANCY = 'CONSTANCY'
}

@Entity()
export class AdminDocuments extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    documentType: AdminDocumentTypeEnum;

    @OneToOne(type => S3Metadata, s3Metadata => s3Metadata.document)
    @JoinColumn()
    file: S3Metadata;

    @ManyToOne(type => Admin, admin => admin.documents)
    @JoinColumn()
    admin: Admin;
}