import { Injectable } from '@nestjs/common';
import * as crypto from 'crypto';

/* ** DTOs ** */
import { ParamsDecryptDto } from '../../interfaces/dtos/sensitive-data.dto';

/* ** Utils ** */
import { authTokenDock } from '../authDock/authDock';

@Injectable()
export class DecryptData {
  constructor(private readonly auth: authTokenDock) {}

  async decryptData(params: ParamsDecryptDto): Promise<string> {
    const { key_cards } = await this.auth.getAuthDock();

    // Decodificar la clave privada de base64
    const privateKeyPem = key_cards.toString('utf8');

    // Decodificar la clave AES de base64
    const aesKeyBuffer = Buffer.from(params.aes, 'base64');

    // Decodificar el IV de base64
    const ivBuffer = Buffer.from(params.iv, 'base64');

    // Decodificar el mensaje cifrado de base64
    const encryptedDataBuffer = Buffer.from(params.encrypt_data, 'base64');

    // Descifrar la clave AES usando la clave privada
    const privateKey = {
      key: privateKeyPem,
      padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
      oaepHash: 'sha256',
    };

    const aesKey = crypto.privateDecrypt(privateKey, aesKeyBuffer);

    // Crear el descifrador AES
    const decipher = crypto.createDecipheriv('aes-256-gcm', aesKey, ivBuffer);

    // Separar los datos encriptados del tag de autenticación
    const authTag = encryptedDataBuffer.subarray(
      encryptedDataBuffer.length - 16,
    );
    const encryptedWithoutTag = encryptedDataBuffer.subarray(
      0,
      encryptedDataBuffer.length - 16,
    );

    decipher.setAuthTag(authTag);

    // Descifrar el mensaje
    const decryptedMessageBuffer = Buffer.concat([
      decipher.update(encryptedWithoutTag),
      decipher.final(),
    ]);

    const decryptedMessage = decryptedMessageBuffer.toString('utf8');

    return decryptedMessage;
  }
}
