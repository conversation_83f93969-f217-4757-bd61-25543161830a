import {
  IsDateString,
  <PERSON>N<PERSON>ber,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { PaginationDto } from 'src/contexts/shared/dto/pagination.dto';

export class ReadUserAccountsFilterDto extends PaginationDto {
  @IsString()
  @IsOptional()
  q?: string;

  @IsDateString()
  @IsOptional()
  initDate?: string;

  @IsDateString()
  @IsOptional()
  finalDate?: string;

  @IsUUID()
  @IsOptional()
  adminId?: string;
}

export class ParamsReadUserTransactionsDto {
  @IsString()
  userId: string;

  @IsNumber()
  page: number;

  @IsNumber()
  limit: number;
}
