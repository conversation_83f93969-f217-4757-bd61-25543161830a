import {
  <PERSON>Enum,
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  IsN<PERSON>ber,
  IsBoolean,
  IsOptional,
  Length,
} from 'class-validator';

import { CardStatus } from 'src/contexts/shared/interfaces/dtos/cards.enum.dto';

export class ParamsChangeCardStatus {
  @IsString()
  @IsUUID()
  card_dock_id: string;

  @IsEnum(CardStatus)
  card_status: CardStatus;
}

export class ParamsDBChangeCardStatus {
  @IsBoolean()
  status: boolean;

  @IsString()
  card_dock_id: string;
}

export class ParamsChangeCardPin {
  @IsString()
  @IsUUID()
  card_dock_id: string;

  @IsString()
  @Length(4, 4)
  pin: string;
}

export class ResChangeCardStatus {
  @IsNumber()
  statusCode: number;

  @IsString()
  message: string;

  @IsString()
  error: string;

  @IsOptional()
  @IsString()
  card_status?: string;
}

export class PayloadChangeCardStatus {
  status: string;
  status_reason: string;
}

export class PayloadChangeCardStatusNormal {
  status: string;
}

export class ResChangeCardPin {
  @IsNumber()
  statusCode: number;

  @IsString()
  message: string;

  @IsOptional()
  @IsString()
  error?: string;
}

export class PayloadChangeCardPin {
  pin: string;
  aes: string;
  iv: string;
  mode: string;
}
