import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsEmail,
  IsString,
  IsIn,
  IsOptional,
} from 'class-validator';

export class SigninDto {
  @ApiProperty()
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  password: string;

  @IsOptional()
  @IsString()
  @IsIn(['web', 'mobile'], {
    message: 'Origin must be either "web" or "mobile"',
  })
  origin?: 'web' | 'mobile';
}
