import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { CreateLogCardsMovementsDto } from '../../application/create-log-cards-movements/create-log-cards-movements.dto';
import { ReadLogCardsMovementsDto } from '../../application/read-log-cards-movements/read-log-cards-movements.dto';

export interface LogsCardsMovementsRepository {
  save(logCardsMovements: CreateLogCardsMovementsDto): Promise<ApiResponseDto>;
  findAll(filters?: ReadLogCardsMovementsDto): Promise<ApiResponseDto>;
  findById(id: string): Promise<ApiResponseDto>;
}