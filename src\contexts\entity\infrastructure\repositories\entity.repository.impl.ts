import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Entity } from 'src/contexts/entity/domain/entities/entity.entity';
import { EntityRepository } from 'src/contexts/entity/domain/repository/entity.repository';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { CreateEntityDto } from 'src/contexts/entity/application/create-entity/create-entity.dto';
import { UpdateEntityDto } from 'src/contexts/entity/application/update-entity/update-entity.dto';
import { Repository } from 'typeorm';
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';

@Injectable()
export class EntityRepositoryImpl implements EntityRepository {
  constructor(
    @InjectRepository(Entity)
    private readonly entityRepository: Repository<Entity>,
  ) {}
  async save(entity: CreateEntityDto): Promise<ApiResponseDto> {
    try {
      const res = await this.entityRepository.save(entity);
      return ResponseUtil.success('Entity created successfully', res, 201);
    } catch (error) {
      return ResponseUtil.error('Entity could not be created', error, 400);
    }
  }

  async findAll(): Promise<ApiResponseDto> {
    try {
      const res = await this.entityRepository.find();
      return ResponseUtil.success('Entities fetched successfully', res, 200);
    } catch (error) {
      return ResponseUtil.error('Entities could not be fetched', error, 400);
    }
  }

  async findById(id: string): Promise<ApiResponseDto> {
    try {
      const res = await this.entityRepository.findOne({ where: { id } });
      if (!res) {
        return ResponseUtil.error(
          'Entity not found',
          'Entity with the given ID does not exist',
          404,
        );
      }
      return ResponseUtil.success('Entity fetched successfully', res, 200);
    } catch (error) {
      return ResponseUtil.error('Entity could not be fetched', error, 400);
    }
  }

  async update(id: string, entity: UpdateEntityDto): Promise<ApiResponseDto> {
    try {
      const res = await this.entityRepository.update(id, entity);
      if (!res) {
        return ResponseUtil.error(
          'Entity not found',
          'Entity with the given ID does not exist',
          404,
        );
      }
      return ResponseUtil.success('Entity updated successfully', res, 200);
    } catch (error) {
      return ResponseUtil.error('Entity could not be updated', error, 400);
    }
  }

  async delete(id: string): Promise<ApiResponseDto> {
    try {
      const res = await this.entityRepository.delete(id);
      return ResponseUtil.success('Entity deleted successfully', res, 200);
    } catch (error) {
      return ResponseUtil.error('Entity could not be deleted', error, 400);
    }
  }
}
