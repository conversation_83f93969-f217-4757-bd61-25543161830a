name: FinberryBack
on:
  push:
    branches: [develop]
  pull_request:
    branches: [develop]

  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'

    strategy:
      matrix:
        node-version: [18.x]

    steps:
      - uses: actions/checkout@v4
      - name: Clean folder
        uses: appleboy/ssh-action@master
        with:                     
            host: ************
            username: ubuntu
            key: "${{ secrets.SSH_PRIVATE_KEY_DEV }}"
            script: |                      
              sudo rm -Rf /var/www/finberryback
              sudo mkdir /var/www/finberryback
              sudo chmod -Rf 777 /var/www/finberryback
      - name: rsync deployments
        uses: burnett01/rsync-deployments@5.1
        with:
          switches: -avzr --delete
          path: ./*
          remote_path: /var/www/finberryback/
          remote_host: ************
          remote_user: ubuntu
          remote_key: '${{ secrets.SSH_PRIVATE_KEY_DEV }}'

      - name: deploy to server and application start
        uses: appleboy/ssh-action@master
        with:                    
            host: ************
            username: ubuntu
            key: "${{ secrets.SSH_PRIVATE_KEY_DEV }}"
            script: |                      
              cd /var/www/finberryback/
              cd /var/www/finberryback/ && npm install
              echo ${{ vars.APP_DEV_ENV }} | base64 --decode --ignore-garbage > .env              
              npx prisma generate
              cd /var/www/finberryback && pm2 stop finberryback && pm2 delete finberryback && pm2 start npm --name "finberryback" -- start
              
              