import apiClient from '../client'
import { CommissionTransfersResponse } from '@/types/commissions/types';
import { UserData, UserConveniaData, UserResponse, VerifyOTPCodePayload, FindUserWithAdminResponse } from '@/types/user/types'
import { StatementApiResponse, StatementRequestParams } from '@/types/statement/types'

export const createAppUser = async (data: UserData) => {
  const res = await apiClient.post('/user/app/create', data)
  return res.data
}

export const createAdminUser = async (data: UserData) => {
  const res = await apiClient.post('/user/admin/create', data)
  return res.data
}

export const createConveniaUser = async (data: UserConveniaData) => {
  const res = await apiClient.post('/user/convenia', data)
  return res.data
}

export const assignRoleToConveniaUser = async (
  userId: string,
  data: { roleId: string; adminId: string | null },
) => {
  const res = await apiClient.post(`/user/${userId}/role`, data);
  return res.data;
};

export const getConveniaUsers = async (params: {
  page: number
  limit: number
  q: string
  admin?: string | null
  _t?: number
}) => {
  const config = params.q ? {
    params,
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  } : { params }

  const res = await apiClient.get('/user/convenia', config)
  return {
    users: res.data.users,
    count: res.data.count,
  }
}

export const getConveniaUserById = async (id: string) => {
  const res = await apiClient.get(`/user/convenia/${id}`)
  return res.data as UserResponse
}

export const getConveniaUserByEmail = async (
  email: string,
): Promise<FindUserWithAdminResponse> => {
  const { data } = await apiClient.get(`/user/findUserWithAdmin/${email}`);
  return data;
};

export const updateConveniaUser = async (id: string, data: Partial<UserConveniaData>) => {
  const res = await apiClient.patch(`/user/convenia/${id}`, data)
  return res.data
}

export const getUserById = async (id: string) => {
  const res = await apiClient.get(`/user/find/${id}`)
  return res.data as UserResponse
}

export const getUserByEmail = async (email: string) => {
  const res = await apiClient.get(`/user/findByEmail/${email}`)
  return res.data as UserResponse
}

export const getAllUsers = async () => {
  const res = await apiClient.get('/user/all')
  return res.data as UserResponse[]
}

export const updateUser = async (id: string, data: Partial<UserData>) => {
  const res = await apiClient.patch(`/user/update/${id}`, data)
  return res.data
}

export const getTotalAccountsDock = async (adminId?: string | null) => {
  const res = await apiClient.get('/user/total-accounts', {
    params: adminId ? { adminId } : {}
  })
  return res.data as { count: number }
}

export const getTotalAccountsTransfer = async (adminId?: string | null) => {
  const res = await apiClient.get('/user/total-trasnfer-accounts', {
    params: adminId ? { adminId } : {}
  })
  return res.data as { count: number }
}

export const getCommissionBalance = async (): Promise<{
  balance: string
  statusCode: number
}> => {
  const response = await apiClient.get('/commission/balance/account')
  const status = response.status
  if (status !== 200) {
    throw new Error('Error al obtener el saldo de la cuenta de comisiones')
  }
  return response.data
}

export const getCommissionTransfers = async (params: { sort: string, page: number, transfer_way: string }): Promise<CommissionTransfersResponse> => {
  const response = await apiClient.get('/commission/transfers/datatable', {
    params,
  })
  const status = response.status
  if (status !== 200) {
    throw new Error('Error al obtener el listado de transferencias de comisión')
  }
  return response.data
}

export const resetPassword = async (data: { email: string; password: string }) => {
  const res = await apiClient.patch('/user/resetPass', data)
  return res.data
}

export const changePassword = async (data: { userId: string; newPassword: string }) => {
  const res = await apiClient.patch('/user/changePass', data)
  return res.data
}

export const deleteUser = async (id: string, adminId: string | null) => {
  const res = await apiClient.delete(`/user/${id}`, {
    params: adminId ? { adminId } : {}
  })
  return res.data
}

export const verifyEmailRegistered = async (email: string) => {
  const res = await apiClient.get(`/user/email-registered?email=${email}`)
  return res.data
}

export const generateOTPCode = async (email: string) => {
  const response = await apiClient.post('/reset-password/generate-otp', { email })
  return response.data
}

export const generateTransferOTPCode = async (email: string) => {
  const response = await apiClient.post('/transfer-otp/generate-otp', { email })
  return response.data
}

export const generateRegisterOTPCode = async (email: string) => {
  const response = await apiClient.post('/users-otp/generate-otp-create-user', { email })
  return response.data
}

export const sendWelcomeEmail = async (data: {
  email: string
  name_user: string
  company_name: string
  role_name: string
  signup_date: string
}) => {
  const response = await apiClient.post('/welcome-email/send-welcome', data)
  return response.data
}

export const verifyOTPCode = async (data: VerifyOTPCodePayload) => {
  const response = await apiClient.post('/otp/verify', data)
  return response.data
}

export const getAccountNumbers = async (email: string) => {
  const response = await apiClient.get(`/account/details/${email}`)
  return response.data
}

export const getUserCredencialsBank = async (id: string | null) => {
  const response = await apiClient.get(`user/account-convenia/${id}`)
  return response.data
}

export const getListTransactions = async (
  email: string,
  options: {
    initialDate: string
    endDate: string
    page: number
    limit: number
  }
) => {
  console.log('🌐 API Call - getListTransactions:', {
    email,
    options,
    url: `/account/list-movements/${email}`
  })

  // Intentar primero con initial_date/end_date
  try {
    const response = await apiClient.get(`/account/list-movements/${email}`, {
      params: {
        initial_date: options.initialDate,
        end_date: options.endDate,
        page: options.page,
        limit: options.limit,
      },
    })

    console.log('🌐 API Response - getListTransactions (initial_date/end_date):', {
      status: response.status,
      dataType: typeof response.data,
      isArray: Array.isArray(response.data),
      dataLength: response.data?.length || 0,
      firstItem: response.data?.[0]
    })

    console.log('✅ API Response exitosa con initial_date/end_date')
    return response.data
  } catch (error) {
    console.log('⚠️ Error con initial_date/end_date, intentando con start_date/end_date:', error)

    // Si falla, intentar con start_date/end_date
    const response = await apiClient.get(`/account/list-movements/${email}`, {
      params: {
        start_date: options.initialDate,
        end_date: options.endDate,
        page: options.page,
        limit: options.limit,
      },
    })

    console.log('🌐 API Response - getListTransactions (start_date/end_date):', {
      status: response.status,
      dataType: typeof response.data,
      isArray: Array.isArray(response.data),
      dataLength: response.data?.length || 0,
      firstItem: response.data?.[0]
    })

    console.log('✅ API Response exitosa con start_date/end_date')
    return response.data
  }
}

export const getAccountStatement = async (
  params: StatementRequestParams
): Promise<StatementApiResponse> => {
  const response = await apiClient.get(`/account/statement/${params.email}`, {
    params: {
      start_date: params.startDate,
      end_date: params.endDate,
    },
  })

  return response.data
}
