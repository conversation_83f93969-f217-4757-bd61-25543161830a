import { IsString, IsNotEmpty } from 'class-validator';

export class ParamsCommissionUpdateDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsString()
  @IsNotEmpty()
  status: string;
}

export class ParamsSaveCommissionDto {
  @IsString()
  @IsNotEmpty()
  type_commission: string;

  @IsString()
  @IsNotEmpty()
  player_account: string;

  @IsString()
  @IsNotEmpty()
  embajador_account: string;

  @IsString()
  @IsNotEmpty()
  amount: string;

  @IsString()
  @IsNotEmpty()
  status: string;
}
