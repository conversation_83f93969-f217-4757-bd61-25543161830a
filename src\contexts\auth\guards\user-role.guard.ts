import { CanActivate, ExecutionContext, ForbiddenException, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { META_ROLES } from '../decorators/role-protected.decorator';
import { Users } from 'src/contexts/users/domain/entities/users.entity';
import { RoleTypeEnum } from 'src/contexts/users/domain/entities/rol.entity';

@Injectable()
export class UserRoleGuard implements CanActivate {

  constructor(
    private readonly reflector: Reflector,
  ){}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {

    const validRoles = this.reflector.get<string[]>(META_ROLES, context.getHandler());

    if(!validRoles || !validRoles.length )
      return true;

    const request = context.switchToHttp().getRequest();
    const user : Users = request.user;

    const adminId = request.params.adminId;

    const relUserRoleAdmins = user.relUserRoleAdmins || null;

    if(!relUserRoleAdmins) {
      throw new ForbiddenException(`user ${user.id} does not have any roles assigned`);
    }

    let hasValidRole = false;

    if (!adminId || relUserRoleAdmins.some( rel => rel.role.type ===  RoleTypeEnum.CONVENIA)) {
      hasValidRole = validRoles.some(role => 
        relUserRoleAdmins.find(rel => 
          rel.role.name === role
        )
      );
    }else{
      hasValidRole = validRoles.some(role => 
        relUserRoleAdmins.find(rel => 
          rel.role.name === role && 
          rel.admin.id === adminId
        )
      );
    }

    if(hasValidRole)
      return true;

    if(adminId)
      throw new ForbiddenException(`user ${user.id} does not have permission to access this resource for admin ${adminId}`);
    else
      throw new ForbiddenException(`user ${user.id} does not have permission to access this resource`);
  }
}
