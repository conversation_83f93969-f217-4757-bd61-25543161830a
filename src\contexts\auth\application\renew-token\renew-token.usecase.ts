import {
  Injectable,
  Logger,
} from '@nestjs/common';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { JwtService } from '@nestjs/jwt';
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';

@Injectable()
export class RenewTokenUseCase {

  constructor(private readonly jwtService: JwtService) {}

  renewToken(id: string): ApiResponseDto {
    return ResponseUtil.success(
      'Token renovado exitosamente.',
      { token: this.jwtService.sign({ id }) },
      200,
    );
  }
}
