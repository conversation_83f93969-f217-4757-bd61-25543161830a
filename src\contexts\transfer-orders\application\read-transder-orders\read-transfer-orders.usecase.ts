import { Injectable } from '@nestjs/common';
import { isUUID } from 'class-validator';
import { UserTransferRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/user-transfer.repository.impl';

@Injectable()
export class ReadTransferOrdersUseCase {
  constructor(private readonly userTransfer: UserTransferRepositoryImpl) {}

  async execute(idTransfer: string): Promise<any> {
    const response = {
      statusCode: 500,
      message: 'Error al obtener la transferencia',
      data: null,
    };
    if (!isUUID(idTransfer)) {
      response.statusCode = 400;
      response.message = 'El ID de la transferencia no es válido';
      return response;
    }

    const order = await this.userTransfer.findPaymentState(idTransfer);

    if (!order) {
      response.statusCode = 404;
      response.message = 'Transferencia no encontrada';
      return response;
    }

    response.statusCode = 200;
    response.message = 'Transferencia obtenida correctamente';
    response.data = order;
    return response;
  }
}
