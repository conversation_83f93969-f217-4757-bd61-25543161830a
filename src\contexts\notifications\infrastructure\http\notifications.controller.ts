import {
  Controller,
  Post,
  Body,
  HttpStatus,
  Req,
  Request,
  Res,
  Response,
} from '@nestjs/common';

/* ** DTOs ** */
import { TriggersTestNotificationDto } from '../../application/triggers-webhook/triggers-webhook.dto';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';

/* ** Use Cases ** */
import { NotificationWebhookUseCase } from '../../application/notification-webhook/notification-webhook.usecase';
import { TriggerTestNotificationUseCase } from '../../application/triggers-webhook/triggers-webhook.usecase';
import { NotificationAuthorizationUseCase } from '../../application/notification-authorization/notification-authorization.usecase';

/* ** Utils ** */
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';

@Controller('webhooks')
export class NotificationsController {
  constructor(
    private readonly notificationUseCase: NotificationWebhookUseCase,
    private readonly testTriggerUseCase: TriggerTestNotificationUseCase,
    private readonly notificationAuthorization: NotificationAuthorizationUseCase,
  ) {}

  @Post('push-notifications')
  async pushNotification(@Req() req: Request, @Res() res: Response) {
    return await this.notificationUseCase.NotificationWebhook(req, res);
  }

  @Post('push-notifications/authorization')
  async pushNotificationAuthorization(
    @Req() req: Request,
    @Res() res: Response,
  ) {
    return await this.notificationAuthorization.execute(req, res);
  }

  @Post('test/trigger-push-notifications')
  async triggerNotification(
    @Body() params: TriggersTestNotificationDto,
  ): Promise<ApiResponseDto> {
    try {
      return await this.testTriggerUseCase.triggerNotification(params);
    } catch (e) {
      return ResponseUtil.error(
        e.message,
        {
          data: {
            statusCode: e.status,
            message: String(e.message),
            error: e.name,
          },
        },
        e.status,
      );
    }
  }

  @Post('test/local-tunnel')
  async testLocalTunnel(): Promise<ApiResponseDto> {
    try {
      return ResponseUtil.success(
        'Local tunnel test success',
        {
          data: 'OK',
        },
        HttpStatus.OK,
      );
    } catch (e) {
      return ResponseUtil.error(
        e.message,
        {
          data: {
            statusCode: e.status,
            message: String(e.message),
            error: e.name,
          },
        },
        e.status,
      );
    }
  }
}
