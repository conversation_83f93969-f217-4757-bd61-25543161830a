import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { AuthTokenDto } from "src/contexts/shared/interfaces/dtos/authDock.dto";
import { authTokenDock } from "src/contexts/shared/utils/authDock/authDock";

import axios, { AxiosInstance } from "axios";
import * as https from 'https';

@Injectable()
export class DockCoreService {

    private axiosInstace: AxiosInstance;
    constructor(
        private readonly configService: ConfigService,
        private readonly authTokenDock: authTokenDock,
    ) { }


    async getHttpClient() : Promise<AxiosInstance> {
        const auth = await this.authTokenDock.getAuthDock()
        const httpsAgent = this.getHttpsAgent(auth);
        const headers = this.getHeaders(auth);
    
        this.axiosInstace = axios.create({
            baseURL: this.configService.get('DOCK_URL_GLOBAL'),
            httpsAgent,
            headers,
        });

        return this.axiosInstace;
    }

    private getHeaders(auth: AuthTokenDto) {
        return {
            Authorization: `Bearer ${auth.bearer_token}`,
            'Content-Type': 'application/json',
        }
    }

    private getHttpsAgent(auth: AuthTokenDto) {
        return new https.Agent({
            cert: auth.certificate,
            key: auth.key,
            rejectUnauthorized: false,
        });
    }

}