import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { GetAccountDetailsUseCase } from '../../application/get-account-details/get-account-details';
import { GetAccountTransfersUseCase } from '../../application/get-account-transfers/get-account-transfers';
import { JwtAuthGuard } from 'src/contexts/auth/guards/auth.guard';
import { GetListTransfersUseCase } from '../../application/get-account-transfers/get-account-list-transfers';
// import { RoleProtected } from 'src/contexts/auth/decorators/role-protected.decorator';
// import { UserRoleGuard } from 'src/contexts/auth/guards/user-role.guard';
// import { RoleEnum } from 'src/contexts/shared/enums/roles.enum';

@UseGuards(JwtAuthGuard)
@Controller('account')
export class AccountController {
  constructor(
    private readonly getAccountDetailsUseCase: GetAccountDetailsUseCase,
    private readonly getAccountTransfersUseCase: GetAccountTransfersUseCase,
    private readonly getListTransfersUseCase: GetListTransfersUseCase,
  ) {}

  @Get('details/:email')
  async getDetails(@Param('email') email: string): Promise<any> {
    email = email.toLowerCase();
    return await this.getAccountDetailsUseCase.execute(email);
  }

  @Get('list-transfers/:email')
  // @RoleProtected(RoleEnum.CONVENIA_ADMIN, RoleEnum.CLIENTE_ADMIN, RoleEnum.CLIENTE_LECTOR, RoleEnum.CLIENTE_GESTOR_TARJETAHABIENTES, RoleEnum.CLIENTE_TESORERO)
  // @UseGuards(UserRoleGuard)
  async getTransfers(@Param('email') email: string): Promise<any[]> {
    email = email.toLowerCase();
    return await this.getAccountTransfersUseCase.execute(email);
  }

  @Get('list-movements/:email')
  async getListTransfers(
    @Param('email') email: string,
    @Query('initial_date') initialDate: string,
    @Query('end_date') endDate: string,
    @Query('page') page: string,
    @Query('limit') limit: string,
  ): Promise<any[]> {
    email = email.toLowerCase();
    return await this.getListTransfersUseCase.execute(email, {
      initialDate,
      endDate,
      page: parseInt(page, 10),
      limit: parseInt(limit, 10),
    });
  }
}
