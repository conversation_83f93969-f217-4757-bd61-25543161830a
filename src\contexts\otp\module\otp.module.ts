import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';

/* ** Modules ** */
import { CustomMailerModule } from 'src/contexts/shared/modules/mailer.module';

/* ** Entities ** */
import { Otp } from '../domain/entities/otp.entity';

/* ** Controllers ** */
import { OtpController } from '../infrastructure/http/otp.controller';

/* ** Use Cases ** */
import { GenerateOtpUseCase } from './../app/generate-otp/generate-otp.usecase';
import { VerifyOtpUseCase } from '../app/verify-otp/verify-otp-usecase';

/* ** Repositories ** */
import { OtpRepositoryImpl } from '../infrastructure/repositories/otp.repository.impl';

/* ** Utils ** */
import { StorageS3Utils } from 'src/contexts/shared/utils/storageUtils/storageS3.utils';

@Module({
  imports: [TypeOrmModule.forFeature([Otp]), CustomMailerModule],
  controllers: [OtpController],
  providers: [
    GenerateOtpUseCase,
    VerifyOtpUseCase,
    OtpRepositoryImpl,
    JwtService,
    StorageS3Utils,
  ],
})
export class OtpModule {}
