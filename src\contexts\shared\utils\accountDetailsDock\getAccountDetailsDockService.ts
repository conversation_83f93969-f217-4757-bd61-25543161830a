import { Injectable, BadRequestException } from '@nestjs/common';
import axios from 'axios';
import * as https from 'https';
import { authTokenDock } from '../authDock/authDock';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class DockAccountDetailsService {
  constructor(
    private readonly authTokenDock: authTokenDock,
    private readonly configService: ConfigService,
  ) {}

  async getAccountDetails(
    accountExtId: string,
    personExtId: string,
  ): Promise<any> {
    // Obtener el token de autenticación y los certificados
    const { bearer_token, key, certificate } =
      await this.authTokenDock.getAuthDock();

    // Configurar el agente HTTPS con los certificados
    const httpsAgent = new https.Agent({
      cert: certificate,
      key: key,
      rejectUnauthorized: false,
    });

    // Construir el cuerpo de la solicitud
    const requestBody = {
      person_id: personExtId,
      id: accountExtId,
      external_account_id: accountExtId,
      metadata: {
        pagination: {
          page: 0,
          limit: 10,
        },
        sort: {
          field: 'id',
          order: 'asc',
        },
      },
    };

    try {
      // Realizar la solicitud al API de Dock
      const response = await axios.post(
        `${this.configService.get('DOCK_URL_GLOBAL')}/account-services/management/v1/account-details`,
        requestBody,
        {
          headers: {
            Authorization: `Bearer ${bearer_token}`,
            'Content-Type': 'application/json',
          },
          httpsAgent,
        },
      );
      
      return response.data;
    } catch (error) {
      console.error(
        'Error obteniendo los detalles de la cuenta desde Dock:',
        error.response?.data || error.message,
      );
      throw new BadRequestException(
        error.response?.data ||
          'Error al obtener los detalles de la cuenta desde Dock',
      );
    }
  }
}
