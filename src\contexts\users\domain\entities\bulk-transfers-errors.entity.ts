import { <PERSON><PERSON><PERSON>ty, <PERSON>umn, CreateDate<PERSON><PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn } from "typeorm";
import { BulkTransfers } from "./bulk-transfers.entity";
import { ApiProperty } from "@nestjs/swagger";
import { IsDateString, IsNumberString, IsString, IsUUID } from "class-validator";


@Entity()
export class BulkTransfersError extends BaseEntity{
    
    @ApiProperty()
    @IsUUID()
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @ApiProperty()
    @IsString()
    @Column()
    errorMessage: string;

    @ManyToOne(type => BulkTransfers, bulkTransfer => bulkTransfer.errors)
    @JoinColumn()
    bulkTransfer: BulkTransfers;

    @ApiProperty()
    @IsDateString()
    @CreateDateColumn()
    createdAt: Date;

    @ApiProperty()
    @IsString()
    @Column({ nullable: true })
    beneficiatyAccount: string;
}