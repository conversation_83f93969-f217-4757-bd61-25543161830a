import { IsString, <PERSON>NotEmpty, IsInt } from 'class-validator';

export class VerifyEntityDto {
  @IsNotEmpty({ message: 'code should not be empty' })
  @IsString({ message: 'code should be a number' })
  code: string;

  @IsNotEmpty({ message: 'access should not be empty' })
  @IsString({ message: 'access should be a string' })
  access: string;
}

export class findEntityOtpDto {
  @IsString()
  id: string;

  @IsString()
  email: string;

  @IsString()
  code_otp: string;

  @IsString()
  acces_token: string;

  @IsInt()
  attempts: number;
}
