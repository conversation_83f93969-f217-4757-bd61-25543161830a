import { Module, MiddlewareConsumer } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

/* ** Entities ** */
import { NotificationWebhook } from '../domain/entities/notification.entity';

/* ** Controllers ** */
import { NotificationsController } from '../infrastructure/http/notifications.controller';

/* ** Use Cases ** */
import { NotificationWebhookUseCase } from '../application/notification-webhook/notification-webhook.usecase';
import { TriggerTestNotificationUseCase } from '../application/triggers-webhook/triggers-webhook.usecase';
import { NotificationAuthorizationUseCase } from '../application/notification-authorization/notification-authorization.usecase';

/* ** Repositories ** */
import { NotificationsRepositoryImpl } from '../infrastructure/repository/notifications.repository.impl';
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';

/* ** Modules ** */
import { CustomRedisStoreModule } from 'src/contexts/shared/modules/redis.module';
import { UsersModule } from 'src/contexts/users/module/users.module';
import { TransferOrdersModule } from 'src/contexts/transfer-orders/module/transfer-orders.module';
import { CommissionModule } from 'src/contexts/commission/module/commission.module';

/* ** Middleware ** */
import { TextPlainMiddleware } from '../infrastructure/http/middleware/text-plain.middleware';

/* ** Utils ** */
import { CommissionsUtils } from 'src/contexts/shared/utils/commissions/commissions.utils';

@Module({
  imports: [
    TypeOrmModule.forFeature([NotificationWebhook]),
    CustomRedisStoreModule,
    UsersModule,
    TransferOrdersModule,
    CommissionModule,
  ],
  controllers: [NotificationsController],
  providers: [
    authTokenDock,
    NotificationWebhookUseCase,
    TriggerTestNotificationUseCase,
    NotificationAuthorizationUseCase,
    NotificationsRepositoryImpl,
    CommissionsUtils,
  ],
})
export class NotificationModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(TextPlainMiddleware)
      .forRoutes(
        'webhooks/push-notifications',
        'webhooks/push-notifications/authorization',
      );
  }
}
