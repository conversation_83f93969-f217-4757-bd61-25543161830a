import { Controller, Body, Post, HttpCode } from '@nestjs/common';

/* ** Import useCases ** */
import { TransferIn } from '../../application/transfer-orders-in/transfer-orders-in.usecase';
import { TransferOut } from '../../application/transfer-order-out/transfer-orders-out.usecase';

/* ** DTOs ** */
import { ParamsTransferOrderInDto } from '../../application/transfer-orders-in/transfer-orders-in.dto';
import { ParamsTransferOrderOutDto } from '../../application/transfer-order-out/transfer-orders-out.dto';

@Controller()
export class TransferWebhookController {
  constructor(
    private readonly transferIn: TransferIn,
    private readonly transferOut: TransferOut,
  ) {}

  @Post('in')
  @HttpCode(200)
  async webhookTransferIn(@Body() body: ParamsTransferOrderInDto) {
    if (body.type?.startsWith('commission_')) {
      return { returnCode: 0 };
    }
    return await this.transferIn.webhookTransferIn(body);
  }

  @Post('out')
  @HttpCode(200)
  async webhookTransferOut(@Body() body: ParamsTransferOrderOutDto) {
    if (body.type?.startsWith('commission_')) {
      return { statusCode: 200, message: 'OK' };
    }
    return await this.transferOut.webhookTransferOut(body);
  }
}
