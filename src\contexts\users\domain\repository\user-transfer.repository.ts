import {
  createPaymentDto,
  ResponseCreatePaymentDto,
  ResponseFindPaymentDto,
  ResponseFindPaymentStateDto,
} from 'src/contexts/transfers/application/create-transaction/dockpay-transfer.dto';

import {
  ParamsUpdateChangeStatusDto,
  ParamsUpdateSignDto,
  ParamsUpdateOrderDto,
  ParamsUpdateCepDto,
  ParamsUpdateDataTransferDto,
  ParamsCommissionDto,
} from 'src/contexts/notifications/application/notification-webhook/notification-webhook.dto';

export interface UserTransferRepository {
  save(entity: createPaymentDto): Promise<ResponseCreatePaymentDto>;
  findByIDPayment(payment_id: string): Promise<ResponseFindPaymentDto>;
  changeStatusPaymentDock(body: ParamsUpdateChangeStatusDto): Promise<boolean>;
  changeStatusOrderTransfer(
    body: ParamsUpdateChangeStatusDto,
  ): Promise<boolean>;
  updateOrderID(body: ParamsUpdateOrderDto): Promise<boolean>;
  updateOrderSign(body: ParamsUpdateSignDto): Promise<boolean>;
  findByOrderID(order_id: string): Promise<ResponseFindPaymentDto>;
  findPaymentState(id: string): Promise<ResponseFindPaymentStateDto>;
  changeCepOrderTransfer(body: ParamsUpdateCepDto): Promise<boolean>;
  changeDataPaymentDock(body: ParamsUpdateDataTransferDto): Promise<boolean>;
  changeCommissionsSpeiOut(body: ParamsCommissionDto): Promise<boolean>;
}
