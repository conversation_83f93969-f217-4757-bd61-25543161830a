import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';

export enum ClarificationStatus {
  ABIERTO = 'Abierto',
  PENDIENTE = 'Pendiente',
  RESUELTO = 'Resuelto',
}

export class CreateClarificationDto {
  @ApiProperty({ description: 'Tipo de aclaración' })
  @IsString()
  type: string;

  @ApiProperty({ description: 'Descripción detallada del caso' })
  @IsString()
  description: string;

  @IsEnum(ClarificationStatus)
  @IsOptional()
  status?: ClarificationStatus;

  @ApiProperty({
    description: 'ID del usuario que crea la aclaración',
    type: String,
  })
  @IsString()
  @IsOptional()
  createdBy?: string;

  @ApiProperty({
    description: 'Correo del usuario que crea la aclaración',
    type: String,
  })
  @IsOptional()
  @IsString()
  email?: string;
}
