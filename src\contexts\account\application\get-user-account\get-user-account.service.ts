import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Users } from '../../../users/domain/entities/users.entity';
import { Account } from '../../../users/domain/entities/account.entity';
import { Person } from '../../../users/domain/entities/person.entity';

@Injectable()
export class GetUserAccountService {
  constructor(
    @InjectRepository(Users)
    private readonly userRepository: Repository<Users>,
  ) {}

  async getAccountAndPersonByEmail(
    email: string,
  ): Promise<{ account_id: string; person_id: string; clabe: string }> {
    const result = await this.userRepository
      .createQueryBuilder('u')
      .innerJoin(Person, 'p', 'u.personIDDockId = p.id')
      .innerJoin(Account, 'a', 'p.id = a.personIDDockId')
      .innerJoin('account_transfer', 'at', 'at.person_id = p.id')
      .select([
        'a.accountExtID AS account_id',
        'p.personExtID AS person_id',
        'at.clabe AS clabe',
      ])
      .where('u.email = :email', { email })
      .getRawOne();

    if (!result) {
      return null;
    }

    return {
      account_id: result.account_id,
      person_id: result.person_id,
      clabe: result.clabe,
    };
  }
}
