import { Injectable } from '@nestjs/common';
import { LogsCardsMovementsRepositoryImpl } from '../../infrastructure/repositories/logs-cards-movements.repository.impl';
import { ReadLogCardsMovementsDto } from './read-log-cards-movements.dto';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';

@Injectable()
export class ReadLogCardsMovementsUseCase {
  constructor(
    private readonly logsCardsMovementsRepositoryImpl: LogsCardsMovementsRepositoryImpl,
  ) {}

  async execute(filters?: ReadLogCardsMovementsDto): Promise<ApiResponseDto> {
    return await this.logsCardsMovementsRepositoryImpl.findAll(filters);
  }

  async executeById(id: string): Promise<ApiResponseDto> {
    return await this.logsCardsMovementsRepositoryImpl.findById(id);
  }
}