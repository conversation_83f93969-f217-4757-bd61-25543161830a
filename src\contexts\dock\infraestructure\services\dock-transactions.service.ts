import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { DockCoreService } from './dock-core.service';
import { ConfigService } from '@nestjs/config';
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';
import { DockTransactions } from '../../interfaces/dock-transactions.interfaces';

@Injectable()
export class DockTransactionsService extends DockCoreService {
  private readonly logger = new Logger(DockTransactionsService.name);

  constructor(configService: ConfigService, authTokenDock: authTokenDock) {
    super(configService, authTokenDock);
  }

  async getTransactions(
    accountId: string,
    page: number,
    transferWay: string,
    sort: string = 'DESC',
  ): Promise<DockTransactions> {
    const httpClient = await this.getHttpClient();
    try {
      const transactions = await httpClient.get<DockTransactions>(
        '/dockpay/v1/transfers',
        {
          params: {
            debtor_key: accountId,
            page,
            transfer_way: transferWay,
            sort,
          },
        },
      );
      return transactions.data;
    } catch (error) {
      this.logger.error(
        `Error getTransactions: ${error.message}`,
        error.response?.data ? JSON.stringify(error.response.data) : '',
      );
      throw new InternalServerErrorException('Internal Server Error');
    }
  }

  async getInTransactions(
    accountId: string,
    page: number,
    transferWay: string,
    sort: string = 'DESC',
  ): Promise<DockTransactions> {
    const httpClient = await this.getHttpClient();
    try {
      const transactions = await httpClient.get<DockTransactions>(
        '/dockpay/v1/transfers',
        {
          params: {
            creditor_key: accountId,
            page,
            transfer_way: transferWay,
            sort,
          },
        },
      );
      return transactions.data;
    } catch (error) {
      this.logger.error(
        `Error getTransactions: ${error.message}`,
        error.response?.data ? JSON.stringify(error.response.data) : '',
      );
      throw new InternalServerErrorException('Internal Server Error');
    }
  }
}
