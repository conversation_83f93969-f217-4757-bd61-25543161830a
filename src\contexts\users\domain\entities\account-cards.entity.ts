import {
  Entity as <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Account } from './account.entity';

import { CardType } from 'src/contexts/shared/interfaces/dtos/cards.enum.dto';

@ORMCards()
export class AccountCards {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ default: true })
  enabled: boolean;

  @Column({
    type: 'enum',
    enum: CardType,
  })
  card_type: CardType;

  @Column({ unique: true })
  card_dock_id: string;

  // Relaciones
  @ManyToOne(() => Account, (account) => account.cards)
  @JoinColumn({ name: 'account_id' })
  account: Account;

  @Column({ nullable: false })
  account_id: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
