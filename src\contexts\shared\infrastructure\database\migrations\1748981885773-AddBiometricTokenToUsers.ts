import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddBiometricTokenToUsers1748981885773
  implements MigrationInterface
{
  name = 'AddBiometricTokenToUsers1748981885773';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "users" ADD "biometricToken" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "biometricToken"`);
  }
}
