import { Injectable, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

/* ** Repositories ** */
import { NotificationRepository } from '../../domain/repository/notifications.repository';

/* ** Entities ** */
import { NotificationWebhook } from '../../domain/entities/notification.entity';

/* ** DTOs ** */
import { CreateNotificationWebhookDto } from '../../application/notification-webhook/notification-webhook.dto';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';

/* ** Utils ** */
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';

@Injectable()
export class NotificationsRepositoryImpl implements NotificationRepository {
  constructor(
    @InjectRepository(NotificationWebhook)
    private readonly notificationRepository: Repository<NotificationWebhook>,
  ) {}

  async save(entity: CreateNotificationWebhookDto): Promise<ApiResponseDto> {
    await this.notificationRepository.save(entity);
    return ResponseUtil.success('Successfully', { data: 'Ok' }, HttpStatus.OK);
  }
}
