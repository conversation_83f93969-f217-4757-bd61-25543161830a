import {
  <PERSON>umn,
  Entity as Orm<PERSON>imits,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';

import { AccountTransfer } from './account-transfer.entity';

@OrmLimits()
export class DepositLimits {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column()
  description: string;

  @Column()
  amount: number;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @OneToMany(() => AccountTransfer, (transfer) => transfer.limit_id)
  limits: AccountTransfer[];
}
