import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as https from 'https';
import axios from 'axios';

/* ** Utils ** */
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';
import { EncryptData } from 'src/contexts/shared/utils/sensitive-data/encriptData.util';
import { RedisService } from 'src/contexts/shared/utils/Redis/redis';

/* ** Repositories ** */
import { AccountCardRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/account-card.repository.impl';

/* ** DTOs ** */
import {
  ParamsChangeCardStatus,
  PayloadChangeCardPin,
  ResChangeCardStatus,
  ParamsChangeCardPin,
  ResChangeCardPin,
} from './control-dock-card-dto';

/* ** Enums ** */
import {
  ReasonStatus,
  CardStatus,
} from 'src/contexts/shared/interfaces/dtos/cards.enum.dto';

/* ** Errors ** */
import {
  ERROR_CHANGE_STATUS_DOCK,
  ERROR_CHANGE_STATUS_DB,
  ERROR_CHANGE_PIN_DOCK,
} from '../../infrastructure/errors/errors-dock-cards';

@Injectable()
export class ControlDockCardUseCase {
  constructor(
    private readonly config: ConfigService,
    private readonly auth: authTokenDock,
    private readonly accountCardImpl: AccountCardRepositoryImpl,
    private readonly encryptData: EncryptData,
    private readonly log: RedisService,
  ) {}

  async changeCardStatus(
    card: ParamsChangeCardStatus,
  ): Promise<ResChangeCardStatus> {
    const card_status = await this.changeDockCardStatus(card);

    if (!card_status) throw new ERROR_CHANGE_STATUS_DOCK();

    const response = await this.changeDBCardStatus(card);

    if (!response) throw new ERROR_CHANGE_STATUS_DB();

    return {
      statusCode: 200,
      message: 'Success',
      error: '',
      card_status,
    };
  }

  async changeCardPin(card: ParamsChangeCardPin): Promise<ResChangeCardPin> {
    const card_status = await this.changeDockCardPin(card);

    if (card_status !== 200) throw new ERROR_CHANGE_PIN_DOCK();

    return {
      statusCode: card_status,
      message: 'Success',
    };
  }

  private async changeDockCardStatus(
    card: ParamsChangeCardStatus,
  ): Promise<string> {
    const { bearer_token, certificate, key } = await this.auth.getAuthDock();

    /* ** Agent ** */
    const httpsAgent = new https.Agent({
      cert: certificate,
      key,
      rejectUnauthorized: false,
    });

    const statusPayloadMap = {
      [CardStatus.BLOCKED]: {
        status: card.card_status,
        status_reason: ReasonStatus.OWNER_REQUEST,
      },
      [CardStatus.CANCELED]: {
        status: card.card_status,
        status_reason: ReasonStatus.OWNER_REQUEST,
      },
      [CardStatus.NORMAL]: {
        status: card.card_status,
      },
      DEFAULT: {
        status: card.card_status,
      },
    };

    const payload =
      statusPayloadMap[card.card_status] || statusPayloadMap.DEFAULT;

    const execute = async (): Promise<string> => {
      try {
        const data_card = await axios.put(
          `${this.config.get('DOCK_URL_GLOBAL')}/cards/v1/cards/${card.card_dock_id}/status`,
          payload,
          {
            headers: {
              Authorization: `Bearer ${bearer_token}`,
            },
            httpsAgent,
          },
        );

        return data_card?.data?.status;
      } catch (error) {
        const code = error.response.status;
        this.log.logError({
          key: 'api:log',
          name: 'ERROR_CHANGE_STATUS_DOCK',
          error: error?.response?.data || error?.message,
          nameController: 'ControlDockCardUseCase',
          statusCode: code,
        });

        return null;
      }
    };
    const response = await execute();

    return response;
  }

  private async changeDBCardStatus(
    card: ParamsChangeCardStatus,
  ): Promise<boolean> {
    const status = card.card_status === CardStatus.BLOCKED ? false : true;
    const response = await this.accountCardImpl.updateCardStatus({
      card_dock_id: card.card_dock_id,
      status,
    });

    return response;
  }

  private async changeDockCardPin(card: ParamsChangeCardPin): Promise<number> {
    const { bearer_token, certificate, key } = await this.auth.getAuthDock();

    /* ** Agent ** */
    const httpsAgent = new https.Agent({
      cert: certificate,
      key,
      rejectUnauthorized: false,
    });

    const encryptedPin = await this.encryptData.encryptData({
      encrypted_data: card.pin,
    });

    const payload: PayloadChangeCardPin = {
      pin: encryptedPin.encrypted_data,
      aes: encryptedPin.aes,
      iv: encryptedPin.iv,
      mode: encryptedPin.mode,
    };

    const execute = async (): Promise<number> => {
      try {
        const data_card = await axios.put(
          `${this.config.get('DOCK_URL_GLOBAL')}/cards/v1/cards/${card.card_dock_id}/pin`,
          payload,
          {
            headers: {
              Authorization: `Bearer ${bearer_token}`,
            },
            httpsAgent,
          },
        );

        return data_card?.status;
      } catch (error) {
        const code = error.response.status;
        this.log.logError({
          key: 'api:log',
          name: 'ERROR_CHANGE_PIN_DOCK',
          error: error?.response?.data || error?.message,
          nameController: 'ControlDockCardUseCase',
          statusCode: code,
        });

        return error.response.status;
      }
    };
    const response = await execute();

    return response;
  }
}
