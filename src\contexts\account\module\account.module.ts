import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Users } from '../../users/domain/entities/users.entity';
import { Account } from '../../users/domain/entities/account.entity';
import { Person } from '../../users/domain/entities/person.entity';
/* ** Controllers ** */
import { AccountController } from '../infrastructure/http/account.controller';

/* ** Use Cases ** */
import { GetAccountDetailsUseCase } from '../application/get-account-details/get-account-details';
import { GetAccountTransfersUseCase } from '../application/get-account-transfers/get-account-transfers';
import { GetListTransfersUseCase } from '../application/get-account-transfers/get-account-list-transfers';

/* ** Services ** */
import { GetUserAccountService } from '../application/get-user-account/get-user-account.service';
import { DockAccountDetailsService } from '../../shared/utils/accountDetailsDock/getAccountDetailsDockService';
import { authTokenDock } from '../../shared/utils/authDock/authDock';
/* ** Modules ** */
import { CustomRedisStoreModule } from '../../shared/modules/redis.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Users, Account, Person]),
    CustomRedisStoreModule,
  ],
  controllers: [AccountController],
  providers: [
    GetUserAccountService,
    DockAccountDetailsService,
    GetAccountDetailsUseCase,
    authTokenDock,
    GetAccountTransfersUseCase,
    GetListTransfersUseCase,
  ],
  exports: [GetAccountDetailsUseCase, GetUserAccountService],
})
export class AccountModule {}
