import {
  Column,
  En<PERSON>ty,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  JoinColumn,
} from 'typeorm';
import { RelUserRoleAdmin } from './rel-user-role-admin.entity';
import { Users } from './users.entity';
import { AdminDocuments } from './admin-documents.entity';

@Entity()
export class Admin {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  company_name: string;

  @Column()
  alias: string;

  @Column()
  rfc: string;

  @Column()
  num_asigned_cards: number;

  @Column()
  is_sucursal: boolean;

  @Column()
  membership_number: number;

  @Column({ nullable: true, type: 'float' })
  spei_in: number;

  @Column({ nullable: true, type: 'float' })
  spei_out: number;

  @Column({ nullable: true })
  managerId: string;

  @Column({ nullable: true, type: 'float' })
  target_refound: number;

  @Column({ nullable: true })
  ambassador: string;

  @Column({ nullable: true })
  group_id: number;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @ManyToOne(() => Users, (user) => user.admins_managed)
  @JoinColumn({ name: 'managerId' })
  manager: Users;

  @OneToMany(() => RelUserRoleAdmin, (rel) => rel.admin)
  relUserRoleAdmins?: RelUserRoleAdmin[];

  @OneToMany(() => AdminDocuments, (document) => document.admin)
  documents?: AdminDocuments[];

  @OneToMany(() => Users, (user) => user.admin_data)
  users: Users[];

  @Column({ default: false })
  isDeleted: boolean;

  @Column({ type: 'timestamp', nullable: true })
  deletedAt?: Date;
}
