import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';

/* ** Modules ** */
import { CustomMailerModule } from 'src/contexts/shared/modules/mailer.module';

/* ** Entities ** */
import { Otp } from '../../otp/domain/entities/otp.entity';

/* ** Controllers ** */
import { OtpController } from '../infrastructure/http/otp-reset-password.controller';

/* ** Use Cases ** */
import { GenerateOtpUseCase } from '../app/generate-otp/generate-otp.usecase';

/* ** Repositories ** */
import { OtpRepositoryImpl } from '../../otp/infrastructure/repositories/otp.repository.impl';

/* ** Utils ** */
import { StorageS3Utils } from 'src/contexts/shared/utils/storageUtils/storageS3.utils';

@Module({
  imports: [TypeOrmModule.forFeature([Otp]), CustomMailerModule],
  controllers: [OtpController],
  providers: [
    GenerateOtpUseCase,
    OtpRepositoryImpl,
    JwtService,
    StorageS3Utils,
  ],
})
export class OtpResetPasswordModule {}
