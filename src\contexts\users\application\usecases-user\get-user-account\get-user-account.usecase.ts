import { Injectable } from '@nestjs/common';

/* ** DTOs */
import {
  ParamsGetUserAccountDto,
  ResponseGetUserAccountDto,
} from './get-user-account.dto';

/* ** Repositories */
import { UsersRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/users.repository.impl';

@Injectable()
export class GetUserAccountUseCase {
  constructor(private readonly user: UsersRepositoryImpl) {}

  async execute(
    params: ParamsGetUserAccountDto,
  ): Promise<ResponseGetUserAccountDto> {
    const account = await this.user.findOnlyUserAccount(params.user_id);

    if (!account) {
      return {
        statusCode: 404,
        message: 'User account not found',
        error: 'Not Found',
      };
    }

    return {
      statusCode: 200,
      message: 'User account found',
      data: account,
    };
  }
}
