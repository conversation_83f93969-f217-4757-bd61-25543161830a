import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn } from "typeorm";
import { State } from "./state.entity";

@Entity("cities")
export class City {

  @PrimaryGeneratedColumn("identity")
  id: number;

  @Column({nullable: true})
  name: string;

  @ManyToOne(() => State, (state) => state.cities)
  @JoinColumn({name: "state_id"})
  state: State;

}