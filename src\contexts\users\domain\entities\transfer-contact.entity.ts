import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  Join<PERSON><PERSON>um<PERSON>,
} from 'typeorm';
import { Users } from './users.entity';

@Entity()
export class TransferContact {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ name: 'num_clabe', type: 'varchar', nullable: true })
  num_clabe: string;

  @Column()
  bank_institution: string;

  @Column({ nullable: true })
  rfc: string;

  @Column({ nullable: true })
  alias: string;

  @Column({ nullable: true })
  company_name: string;

  @Column({ nullable: true })
  email: string;

  // 👇 Este campo es necesario para queries simples sin JOIN
  @Column()
  userId: string;

  @ManyToOne(() => Users, (user) => user.transferContacts, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'userId' })
  user: Users;
}
