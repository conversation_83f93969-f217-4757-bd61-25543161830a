import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

/* ** Modules ** */
import { TransferAccountClabeModule } from 'src/contexts/shared/modules/transfer-account.module';
import { UsersModule } from 'src/contexts/users/module/users.module';
import { RedisService } from 'src/contexts/shared/utils/Redis/redis';
import { CommissionModule } from 'src/contexts/commission/module/commission.module';

/* ** Entities ** */
import { TransferOrders } from '../domain/entities/transfer-orders.entity';

/* ** Controllers ** */
import { TransferOrdersController } from '../infrastructure/http/transfer-orders.controller';
import { TransferWebhookController } from '../infrastructure/http/transfer-webhook.controller';

/* ** Use Cases ** */
import { TransferIn } from '../application/transfer-orders-in/transfer-orders-in.usecase';
import { TransferOut } from '../application/transfer-order-out/transfer-orders-out.usecase';
import { CheckOrders } from '../application/check-orders/check-orders.usecase';
import { CreateOrdersTransfer } from '../application/create-transfer-orders/create-transfer-orders.usecase';
import { AccountBalances } from '../application/account-balances/account-balances.usecase';

/* ** Repositories ** */
import { TransferOrdersRepositoryImpl } from '../infrastructure/repository/transfer-orders.repository.impl';

/* ** Utils ** */
import { TransferEncryptSign } from 'src/contexts/shared/utils/transfer-encrypt-sign/transfer-encrypt-sign.utils';
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';
import { CommissionsUtils } from 'src/contexts/shared/utils/commissions/commissions.utils';
import { ReadTransferOrdersUseCase } from '../application/read-transder-orders/read-transfer-orders.usecase';

@Module({
  imports: [
    TypeOrmModule.forFeature([TransferOrders]),
    TransferAccountClabeModule,
    UsersModule,
    CommissionModule,
  ],
  controllers: [TransferOrdersController, TransferWebhookController],
  providers: [
    TransferIn,
    TransferOut,
    CheckOrders,
    AccountBalances,
    TransferOrdersRepositoryImpl,
    TransferEncryptSign,
    CreateOrdersTransfer,
    authTokenDock,
    RedisService,
    CommissionsUtils,
    ReadTransferOrdersUseCase,
  ],
  exports: [CreateOrdersTransfer],
})
export class TransferOrdersModule {}
