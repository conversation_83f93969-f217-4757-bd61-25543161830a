import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  UseGuards,
  HttpCode,
} from '@nestjs/common';
import { ApiTags, ApiResponse, ApiOperation } from '@nestjs/swagger';
import { CreateLogCardsMovementsUseCase } from '../../application/create-log-cards-movements/create-log-cards-movements.usecase';
import { ReadLogCardsMovementsUseCase } from '../../application/read-log-cards-movements/read-log-cards-movements.usecase';
import { CreateLogCardsMovementsDto } from '../../application/create-log-cards-movements/create-log-cards-movements.dto';
import { ReadLogCardsMovementsDto } from '../../application/read-log-cards-movements/read-log-cards-movements.dto';
import { JwtAuthGuard } from 'src/contexts/auth/guards/auth.guard';
import { RoleProtected } from 'src/contexts/auth/decorators/role-protected.decorator';
import { UserRoleGuard } from 'src/contexts/auth/guards/user-role.guard';
import { RoleEnum } from 'src/contexts/shared/enums/roles.enum';

@ApiTags('Logs Cards Movements')
@UseGuards(JwtAuthGuard, UserRoleGuard)
@Controller('logs-cards-movements')
export class LogsCardsMovementsController {
  constructor(
    private readonly createLogCardsMovementsUseCase: CreateLogCardsMovementsUseCase,
    private readonly readLogCardsMovementsUseCase: ReadLogCardsMovementsUseCase,
  ) {}

  @Post()
  @HttpCode(201)
  @RoleProtected(RoleEnum.CONVENIA_ADMIN)
  @ApiOperation({ summary: 'Crear un nuevo log de movimiento de tarjeta' })
  @ApiResponse({
    status: 201,
    description: 'Log de movimiento de tarjeta creado exitosamente',
  })
  @ApiResponse({
    status: 400,
    description: 'Datos inválidos',
  })
  create(@Body() createLogDto: CreateLogCardsMovementsDto) {
    return this.createLogCardsMovementsUseCase.execute(createLogDto);
  }

  @Get()
  @RoleProtected(RoleEnum.CONVENIA_ADMIN)
  @ApiOperation({ summary: 'Obtener todos los logs de movimientos de tarjetas' })
  @ApiResponse({
    status: 200,
    description: 'Logs obtenidos exitosamente',
  })
  findAll(@Query() filters: ReadLogCardsMovementsDto) {
    return this.readLogCardsMovementsUseCase.execute(filters);
  }

  @Get(':id')
  @RoleProtected(RoleEnum.CONVENIA_ADMIN)
  @ApiOperation({ summary: 'Obtener un log de movimiento de tarjeta por ID' })
  @ApiResponse({
    status: 200,
    description: 'Log obtenido exitosamente',
  })
  @ApiResponse({
    status: 404,
    description: 'Log no encontrado',
  })
  findOne(@Param('id') id: string) {
    return this.readLogCardsMovementsUseCase.executeById(id);
  }
}