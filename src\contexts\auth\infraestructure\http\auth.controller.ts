import {
  Body,
  Controller,
  HttpCode,
  Post,
  UnauthorizedException,
} from '@nestjs/common';
import { SigninDto } from '../../application/signin/signin.dto';
import { SigninUseCase } from '../../application/signin/signin.usecase';
import { RenewTokenUseCase } from '../../application/renew-token/renew-token.usecase';
import { Auth } from '../../decorators/auth.decorator';
import { GetUserDecorator } from '../../decorators/get-user.decorator';
import { Users } from 'src/contexts/users/domain/entities/users.entity';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { SiginVO } from '../../application/signin/signin.vo';
import { BiometricAuthDto } from '../../application/biometric/biometric-auth.dto';
import { BiometricAuthUseCase } from '../../application/biometric/biometric-auth.usecase';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly signinUseCase: SigninUseCase,
    private readonly renewTokenUseCase: RenewTokenUseCase,
    private readonly biometricAuthUseCase: BiometricAuthUseCase,
  ) {}

  @Post('signin')
  @HttpCode(200)
  @ApiResponse({
    status: 200,
    description: 'Successful sign-in',
    type: SiginVO,
  })
  @ApiResponse({
    status: 409,
    description: 'Conflic exception',
  })
  signin(@Body() dto: SigninDto): Promise<SiginVO> {
    return this.signinUseCase.signin(dto);
  }

  @Auth()
  @Post('renew-token')
  @ApiResponse({
    status: 200,
    description: 'Successful sign-in',
    type: SiginVO,
  })
  @ApiBearerAuth()
  renewToken(@GetUserDecorator() user: Users) {
    return this.renewTokenUseCase.renewToken(user.id);
  }

  @Auth()
  @Post('biometric')
  async setBiometricAuth(@Body() dto: BiometricAuthDto) {
    return this.biometricAuthUseCase.execute(dto);
  }

  @Post('biometric-login')
  @HttpCode(200)
  async biometricLogin(@Body() body: { biometricToken: string }) {
    const user = await this.biometricAuthUseCase.findByBiometricToken(
      body.biometricToken,
    );
    if (!user) throw new UnauthorizedException('Token biométrico inválido');

    await this.signinUseCase.validateLoginAttempts(user);

    return await this.signinUseCase.buildToken(user);
  }
}
