import {
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export class ParamsGetUserAccountDto {
  @IsUUID()
  user_id: string;
}

class DataAccountUser {
  @IsString()
  convenia_account: string;

  @IsNumber()
  member_ship: number;

  @IsString()
  clabe: string;
}

export class ResponseGetUserAccountDto {
  @IsNumber()
  statusCode: number;

  @IsString()
  message: string;

  @IsString()
  @IsOptional()
  error?: string;

  @ValidateNested()
  @IsOptional()
  @Type(() => DataAccountUser)
  data?: DataAccountUser;
}

export class ResponseFindOnlyUserAccountDto {
  @IsString()
  clabe: string;

  @IsString()
  convenia_account: string;

  @IsNumber()
  member_ship: number;
}

export class ResponseFindAccountCreditorDto {
  @IsString()
  beneficiary_account: string;

  @IsString()
  convenia_account: string;

  @IsString()
  @IsOptional()
  cep?: string;

  @IsString()
  @IsOptional()
  payment_type?: string;
}
