import { IsS<PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';

export class UpdateAddressDto {
  @IsOptional()
  @IsString()
  state?: string;

  @IsOptional()
  @IsString()
  city?: string;

  @IsOptional()
  @IsString()
  colonia?: string;

  @IsOptional()
  @IsString()
  street?: string;

  @IsOptional()
  @IsString()
  num_ext?: string;

  @IsOptional()
  @IsString()
  zip_code?: string;
}
