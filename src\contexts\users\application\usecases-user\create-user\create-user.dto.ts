import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsEmail,
  IsOptional,
  IsBoolean,
  IsUUID,
  IsN<PERSON>ber,
  MinLength,
  Matches,
} from 'class-validator';

class BasicUserDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsNumber()
  phone: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  dialing_code?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  country_code?: string;

  @ApiProperty()
  @IsString()
  @MinLength(8)
  @Matches(/(?:(?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
    message:
      'La contrasña debe contener minimo 8 caracteres, incluyendo numeros y letras.',
  })
  password: string;

  @ApiProperty()
  @IsString()
  created_by: string;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  enabled?: boolean;

  @ApiProperty()
  @IsEmail()
  @IsOptional()
  rfc?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  convenia_account?: string;
}
export class CreateUserDto extends BasicUserDto {
  @ApiProperty()
  @IsNumber()
  @IsOptional()
  membership_number?: number;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  personIDDock?: string;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  admin_data?: string;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  address?: string;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  rol?: string;
}

export class CreateConveniaUserDto extends BasicUserDto {
  @ApiProperty()
  @IsUUID()
  rol: string;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  admin_data?: string;
}
