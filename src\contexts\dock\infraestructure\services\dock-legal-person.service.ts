import { Injectable, InternalServerErrorException, Logger } from "@nestjs/common";
import { DockCoreService } from "./dock-core.service";
import { ConfigService } from "@nestjs/config";
import { authTokenDock } from "src/contexts/shared/utils/authDock/authDock";
import { DockAccountDetail } from "src/contexts/users/application/usecases-admin/read-admin/interfaces/account-detail.interface";
import { LegalPersonDetail } from "src/contexts/person-account/application/update-person-account-legal/interfaces/legal-person-detail.interface";
import { UpdateLegalPersonDto } from "src/contexts/person-account/application/update-person-account-legal/update-legal-person.dto";
import { UpdatePersonEmailLegalDto } from "src/contexts/person-account/application/update-person-account-legal/update-person-email-legal.dto";
import { UpdatePersonAddressLegalDto } from "src/contexts/person-account/application/update-person-account-legal/update-person-address-legal.dto";

@Injectable()
export class DockLegalPersonService extends DockCoreService {
    
    private readonly logger = new Logger(DockLegalPersonService.name);

    constructor(
        configService: ConfigService,
        authTokenDock: authTokenDock
    ) {
        super(configService, authTokenDock);
    }

    async getAccountDetail(externalId: string, personId: string) : Promise<DockAccountDetail> {
        
        const httpClient = this.getHttpClient();

        try {
            
            const accountDetail = await (await httpClient).post<DockAccountDetail>('/account-services/management/v1/account-details',
                {
                  "person_id": personId,
                  "id": externalId,
                  "external_account_id": externalId,
                    "metadata": {
                    "pagination": {
                      "page": 0,
                      "limit": 1
                    },
                    "sort": {
                      "field": "id",
                      "order": "asc"
                    }
                  }
            });
            
            return accountDetail.data;

        } catch (error) {
            this.logger.error('Error getAccountDetail', error);
            throw new InternalServerErrorException('Internal Server Error');
        }
        
    }

    async getLegalPerson(legalPersonId: string) : Promise<LegalPersonDetail>{
        const httpClient = this.getHttpClient();
        try {
          const regalPerson = await (await httpClient).get<LegalPersonDetail>(`/person/v1/legal-persons/${legalPersonId}`);
    
          return regalPerson.data;
        } catch (error) {
            this.logger.error('Error getLegalPerson', error.message);
          throw new InternalServerErrorException('Internal Server Error');
        }   
    }

    async updateLegalPerson({legalPersonId, ...dto}: UpdateLegalPersonDto): Promise<void> {
      
      const http = await this.getHttpClient();
  
      try {
        await http.patch(`/person/v1/legal-persons/${legalPersonId}`,dto);
  
      } catch (error) {
        console.error(error);
        throw new InternalServerErrorException('INTERNAL SERVER ERROR')
      }
  
    }

    async updateEmail(dto: UpdatePersonEmailLegalDto) : Promise<void> {  
      const http = await this.getHttpClient();
      try {
        await http.patch(`/person/v1/legal-persons/${dto.legalPersonId}/emails/${dto.currentEmail}`,{email: dto.email});
      } catch (error) {
        console.error(error);
        throw new InternalServerErrorException('INTERNAL SERVER ERROR')
      }   
    }

    async updateLegalPersonAddress({legalPersonId, addressLegalId, ...dto}: UpdatePersonAddressLegalDto) : Promise<void> {
      
      const http = await this.getHttpClient();
  
      try {
        await http.patch(`/person/v1/legal-persons/${legalPersonId}/addresses/${addressLegalId}`,dto);
  
      } catch (error) {
        console.error(error.data);
        throw new InternalServerErrorException('INTERNAL SERVER ERROR')
      }
      
    }

}