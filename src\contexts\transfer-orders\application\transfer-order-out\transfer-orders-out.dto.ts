import {
  IsString,
  <PERSON>N<PERSON>ber,
  ValidateNested,
  IsOptional,
} from 'class-validator';
import { Type } from 'class-transformer';

class DataDto {
  @IsOptional()
  @IsString()
  id?: string;

  @IsOptional()
  @IsString()
  status?: string;
}

export class ParamsTransferOrderOutDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => DataDto)
  data?: DataDto;

  @IsOptional()
  @IsString()
  sign?: string;

  @IsOptional()
  @IsString()
  type?: string;
}

export class ResTransferOrderOutDto {
  @IsNumber()
  statusCode: number;

  @IsString()
  message: string;
}

export class SaveDataTransferOrderDto {
  @IsString()
  type: string;

  @IsString()
  data: string;
}
