import { IsString, IsNotEmpty, <PERSON>Optional, IsNumber } from 'class-validator';

export class CreateNotificationWebhookDto {
  @IsOptional()
  @IsString()
  eventName?: string;

  @IsOptional()
  @IsString()
  status?: string;

  @IsNotEmpty()
  @IsString()
  log: string;

  @IsOptional()
  @IsString()
  error?: string;
}

export class ParamsUpdateChangeStatusDto {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsNotEmpty()
  @IsString()
  status: string;
}

export class ParamsUpdateOrderDto {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsNotEmpty()
  @IsString()
  order_id: string;
}
export class ParamsUpdateSignDto {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsNotEmpty()
  @IsString()
  sign_transfer: string;

  @IsOptional()
  @IsString()
  error_transfer?: string;
}

export class ResponseCommissionByAccountDto {
  @IsNumber()
  spei_in: number;

  @IsNumber()
  spei_out: number;

  @IsNumber()
  target_refound: number;

  @IsString()
  @IsOptional()
  ambassador?: string;

  @IsString()
  @IsOptional()
  account?: string;

  @IsOptional()
  @IsString()
  role_type?: string;
}

export class ParamsApplyCommissionDto {
  @IsNotEmpty()
  @IsString()
  debtor_dock_id: string;

  @IsNotEmpty()
  @IsString()
  creditor_dock_id: string;

  @IsNumber()
  amount: number;

  @IsNotEmpty()
  @IsString()
  type: 'SPEI_IN' | 'SPEI_OUT';

  @IsNotEmpty()
  @IsString()
  event_status: string;
}

export class ParamsUpdateCepDto {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsNotEmpty()
  @IsString()
  cep: string;
}

export class ParamsUpdateDataTransferDto {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsString()
  @IsNotEmpty()
  reference_dock_id?: string;

  @IsNotEmpty()
  @IsString()
  commission: string;

  @IsNotEmpty()
  @IsString()
  status: string;

  @IsNotEmpty()
  @IsString()
  status_transfer: string;
}

export class ParamsCommissionDto {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsNotEmpty()
  @IsString()
  commission: string;

  @IsNotEmpty()
  @IsString()
  status: string;

  @IsString()
  cep: string | null;
}
