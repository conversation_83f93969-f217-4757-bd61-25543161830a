import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/* ** DTOs ** */
import { ResponseCreateConveniaAccountDto } from '../../interfaces/dtos/convenia-account.dto';

/* ** Repository ** */
import { UsersRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/users.repository.impl';

@Injectable()
export class ConveniaAccount {
  /* ** Constructor ** */
  constructor(
    private readonly config: ConfigService,
    private readonly user: UsersRepositoryImpl,
  ) {}

  /* ** Create a new Convenia account ** */
  async createConveniaAccount(): Promise<ResponseCreateConveniaAccountDto> {
    for (let attempt = 0; attempt < 60; attempt++) {
      /* ** Generate a new Convenia account ** */
      const { convenia_account } = this.generateConveniaAccount();

      const not_exists = await this.user.findConveniaAccount(convenia_account);

      if (not_exists) {
        return { convenia_account };
      }
    }
    throw new Error(
      'No se pudo generar una cuenta Convenia única después de 60 intentos',
    );
  }

  /* ** Generate Cuenta Convenia ** */
  private generateConveniaAccount(): ResponseCreateConveniaAccountDto {
    /* ** Get default params Convenia account ** */
    const product_type = this.config.get<string>('TRANSFER_ACCOUNT_TYPE');

    /* ** Generate the CLABE ** */
    const numero = Array(8)
      .fill(0)
      .map(() => Math.floor(Math.random() * 10)) // Genera un dígito aleatorio entre 0 y 9
      .join(''); // Une los dígitos en una cadena

    const account = `${product_type}${numero}`;

    return { convenia_account: account };
  }
}
