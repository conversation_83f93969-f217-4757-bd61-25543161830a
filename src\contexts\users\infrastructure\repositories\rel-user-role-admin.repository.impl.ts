import { ApiResponseDto } from "src/contexts/shared/interfaces/dtos/api-response.dto";
import { CreateRelUserRoleAdminDto } from "../../application/usecases-user/create-user/create-rel-user-role-admin.dto";
import { RelUserRoleAdminRepository } from "../../domain/repository/rel-user-role-admin.respository";
import { InjectRepository } from "@nestjs/typeorm";
import { RelUserRoleAdmin } from "../../domain/entities/rel-user-role-admin.entity";
import { Repository } from "typeorm";
import { ResponseUtil } from "src/contexts/shared/utils/response.util";

export class RelUserRoleAdminRepositoryImpl implements RelUserRoleAdminRepository {

    constructor(
        @InjectRepository(RelUserRoleAdmin)
        private readonly relUserRoleAdminRepository: Repository<RelUserRoleAdmin>,
    ){}

    async save(dto: CreateRelUserRoleAdminDto): Promise<ApiResponseDto> {
        const entity = this.relUserRoleAdminRepository.create({
            role: {id: dto.rol},
            admin: {id: dto.admin_data},
            user: {id: dto.user_id}
        });

        const res = await this.relUserRoleAdminRepository.save(entity);

        return ResponseUtil.success('Rol asignado correctamente', res, 201);
    }

    async remove(dto: CreateRelUserRoleAdminDto): Promise<void> {
        await this.relUserRoleAdminRepository.delete({
            role: {id: dto.rol},
            admin: {id: dto.admin_data},
            user: {id: dto.user_id}
        });
    }

    async removeByUserId(userId: string) : Promise<void>{
        try {
            await this.relUserRoleAdminRepository
                .createQueryBuilder('rel')
                .delete()
                .from(RelUserRoleAdmin)
                .where('userId = :userId', { userId })
                .execute();
        } catch (error) {
            console.error('Error al eliminar relaciones de usuario:', error);
            throw error; 
        }
    }

    async removeByUserIdAndAdminId(userId: string, adminId: string): Promise<void> {
        try {
            await this.relUserRoleAdminRepository
                .createQueryBuilder('rel')
                .delete()
                .from(RelUserRoleAdmin)
                .where('userId = :userId', { userId })
                .andWhere('adminId = :adminId', { adminId })
                .execute();
        } catch (error) {
            console.error('Error al eliminar relaciones de usuario por admin:', error);
            throw error; 
        }
    }

    async deleteById(id: string): Promise<void> {
        await this.relUserRoleAdminRepository.delete(id);
    }

    async getByAdminId(id: string): Promise<RelUserRoleAdmin[]> {
        return await this.relUserRoleAdminRepository.find({ 
            where: { 
                admin : { id } 
            },
            relations: ['user']
        });
    }
}