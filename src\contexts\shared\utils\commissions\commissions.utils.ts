import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import * as https from 'https';

/* ** DTOs ** */
import {
  ParamsCommissionsDto,
  ResponseCommissionsDto,
} from '../../interfaces/dtos/commissions.dto';

/* ** Utils ** */
import { authTokenDock } from '../authDock/authDock';

/* ** Enums ** */
import { PaymentStatusEnum } from '../../enums/typePayment.enum';

@Injectable()
export class CommissionsUtils {
  constructor(
    private readonly auth: authTokenDock,
    private readonly config: ConfigService,
  ) {}

  async payCommissions(
    body: ParamsCommissionsDto,
  ): Promise<ResponseCommissionsDto> {
    try {
      /* ** Response DTO for the commission payment ** */
      let response_payment: ResponseCommissionsDto = {
        status_code: 500,
        operation_id: '',
      };
      /* ** Excute the request to pay the commission ** */
      const executePaymentCommission =
        async (): Promise<ResponseCommissionsDto> => {
          try {
            const auth = await this.auth.getAuthDock();
            const httpsAgent = new https.Agent({
              cert: auth.certificate,
              key: auth.key,
              rejectUnauthorized: false,
            });

            const { data, status } = await axios.post(
              `${this.config.get('DOCK_URL_GLOBAL')}/dockpay/v1/transfers`,
              {
                debtor: {
                  key: body.debtor_key,
                  key_type: 'ACCOUNT_ID',
                  balance_category: 'GENERAL',
                },
                creditor: {
                  key: body.creditor_key,
                  key_type: 'ACCOUNT_ID',
                  balance_category: 'GENERAL',
                },
                external_transaction_id: body.external_transaction_id,
                amount: body.commission,
                operation_type: 'P2P - P2P_OUT',
                description: `Comisión Convenia`,
                type: 'commission_out',
              },
              {
                headers: {
                  Authorization: `Bearer ${auth.bearer_token}`,
                  'Content-Type': 'application/json',
                },
                httpsAgent,
              },
            );

            return {
              status_code: status,
              operation_id: data.operation_instance_id,
            };
          } catch (error) {
            console.log('Error al pagar comisión:', error.message);
            return {
              status_code: error?.response?.status || 500,
              operation_id: '',
            };
          }
        };

      /* ** Delay for SPEI_IN type to ensure the transaction is processed correctly ** */
      if (body.type === 'SPEI_IN' && body.operation_instance_id) {
        const status_payment = await this.getOperationInstanceID(
          body.operation_instance_id,
        );

        if (status_payment === PaymentStatusEnum.APPROVED) {
          response_payment = await executePaymentCommission();
        } else {
          response_payment = {
            status_code: 500,
            operation_id: '',
          };
        }
      } else if (body.type !== 'SPEI_IN') {
        response_payment = await executePaymentCommission();
      }

      return response_payment;
    } catch (error) {
      console.error('Error al cobrar comisión', error.message);
      return {
        status_code: error?.response?.status || 500,
        operation_id: '',
      };
    }
  }

  private async getOperationInstanceID(
    operation_instance_id: string,
  ): Promise<string> {
    const executeRequest = async (): Promise<string> => {
      try {
        /* ** Get the authentication token and certificates */
        const auth = await this.auth.getAuthDock();

        /* ** Configure the HTTPS agent with the certificates */
        const httpsAgent = new https.Agent({
          cert: auth.certificate,
          key: auth.key,
          rejectUnauthorized: false,
        });

        /* ** Make the request to Dock's API */
        const res = await axios.get(
          `${this.config.get('DOCK_URL_GLOBAL')}/dockpay/v1/transfers/${operation_instance_id}`,
          {
            headers: {
              Authorization: `Bearer ${auth.bearer_token}`,
              Accept: 'application/json',
            },
            httpsAgent,
          },
        );

        /* ** Assign Data Balance */
        const data = res.data;
        const availableResource = data ? data.status : 'PENDING';

        return availableResource;
      } catch (error) {
        console.error(
          'Error obteniendo los detalles de la cuenta desde Dock:',
          error.response?.data || error.message,
        );
        return 'PENDING';
      }
    };

    /* ** Polling to check the status of the operation instance ID ** */
    let status_commission: string = 'PENDING';

    for (let i = 0; i < 10; i++) {
      await this.sleep(5000);

      const status = await executeRequest();

      if (status === PaymentStatusEnum.APPROVED) {
        status_commission = PaymentStatusEnum.APPROVED;
        break;
      }
    }

    return status_commission;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
