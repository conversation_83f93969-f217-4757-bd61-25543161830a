import { Module } from '@nestjs/common';

/* ** Modules ** */
import { UsersModule } from 'src/contexts/users/module/users.module';
import { CustomRedisStoreModule } from 'src/contexts/shared/modules/redis.module';
import { LogsCardsMovementsModule } from 'src/contexts/logs-cards-movements/module/logs_cards_movements.module';

/* ** Controllers ** */
import { CardAssignmentController } from '../infrastructure/http/card_assignment.controller';

/* ** Use Cases ** */
import { SingleCardAssignmentUseCase } from '../application/single-card-assignment/single-card-assignment.usecase';

/* ** Utils ** */
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';
import { EncryptData } from 'src/contexts/shared/utils/sensitive-data/encriptData.util';
import { CardDockModule } from 'src/contexts/dock-cards/module/dock-cards.module';

@Module({
  imports: [
    UsersModule,
    CustomRedisStoreModule,
    CardDockModule,
    LogsCardsMovementsModule,
  ],
  controllers: [CardAssignmentController],
  providers: [SingleCardAssignmentUseCase, authTokenDock, EncryptData],
})
export class CardAssignmentModule {}
