import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CreateTransferContactUseCase } from '../../application/usecases-transfers/create-transfer-contact/create-transfer-contact.usecase';
import { CreateTransferContactDto } from '../../application/usecases-transfers/create-transfer-contact/create-transfer-contact.dto';
import { ReadTransferContactUseCase } from '../../application/usecases-transfers/read-transfer-contact/read-transfer-contact.usecase';
import { DeleteTransferContactUseCase } from '../../application/usecases-transfers/delete-transfer-contact/delete-transfer-contact.usecase';
import { UpdateTransferContactUseCase } from '../../application/usecases-transfers/update-transfer-contact/update-transfer-contact.usecase';
import { UpdateTransferContactDto } from '../../application/usecases-transfers/update-transfer-contact/update-transfer-contact.dto';
import { JwtAuthGuard } from 'src/contexts/auth/guards/auth.guard';
import { RoleProtected } from 'src/contexts/auth/decorators/role-protected.decorator';
import { RoleEnum } from 'src/contexts/shared/enums/roles.enum';
import { UserRoleGuard } from 'src/contexts/auth/guards/user-role.guard';

@UseGuards(JwtAuthGuard)
@Controller('contact')
export class TransferContactController {
  constructor(
    private readonly createTransferContactUseCase: CreateTransferContactUseCase,
    private readonly readTransferContactUseCase: ReadTransferContactUseCase,
    private readonly deleteTransferContactUseCase: DeleteTransferContactUseCase,
    private readonly updateTransferContactUseCase: UpdateTransferContactUseCase,
  ) {}

  @Post('save')
  // @RoleProtected(RoleEnum.CONVENIA_ADMIN, RoleEnum.CLIENTE_ADMIN, RoleEnum.CLIENTE_TESORERO)
  @UseGuards(UserRoleGuard)
  saveTransferContact(@Body() createTransferContact: CreateTransferContactDto) {
    return this.createTransferContactUseCase.execute(createTransferContact);
  }

  @Get('findByUser')
  findByUser(@Query('idUser') idUser: string) {
    return this.readTransferContactUseCase.executeFind(idUser);
  }

  @Delete(':id')
  // @RoleProtected(
  //   RoleEnum.CONVENIA_ADMIN,
  //   RoleEnum.CLIENTE_ADMIN,
  //   RoleEnum.CLIENTE_TESORERO,
  // )
  @UseGuards(UserRoleGuard)
  deleteTransferContactByID(@Param('id') id: string) {
    return this.deleteTransferContactUseCase.execute(id);
  }

  @Patch(':id')
  // @RoleProtected(RoleEnum.CONVENIA_ADMIN, RoleEnum.CLIENTE_ADMIN, RoleEnum.CLIENTE_TESORERO)
  @UseGuards(UserRoleGuard)
  updateTransferContactByID(
    @Param('id') id: string,
    @Body() data: UpdateTransferContactDto,
  ) {
    return this.updateTransferContactUseCase.execute(id, data);
  }
}
