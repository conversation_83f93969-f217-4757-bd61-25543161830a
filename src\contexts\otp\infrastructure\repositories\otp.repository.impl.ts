import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

/* ** Entities ** */
import { Otp } from '../../domain/entities/otp.entity';

/* ** DTO ** */
import { CreateOtpDto } from '../../app/generate-otp/generate-otp.dto';
import { VerifyEntityDto } from '../../app/verify-otp/verify-otp.dto';
import { ResponseUtil } from 'src/contexts/shared/utils/response.util';
import { OtpJwtDto } from 'src/contexts/shared/interfaces/dtos/jwt.dto';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { findEntityOtpDto } from '../../app/verify-otp/verify-otp.dto';

/* ** Repositories ** */
import { OtpRepository } from '../../domain/repository/otp.repository';

/**
 * Injectable service that implements the OtpRepository interface.
 */
@Injectable()
export class OtpRepositoryImpl implements OtpRepository {
  constructor(
    @InjectRepository(Otp)
    private readonly otpRepository: Repository<Otp>,
    private readonly jwt: JwtService,
  ) {}

  async save(entity: CreateOtpDto): Promise<ApiResponseDto> {
    const res = await this.otpRepository.save(entity);
    return ResponseUtil.success('Successfully', { data: res }, 200);
  }

  async updateToken(id: string, token: string): Promise<ApiResponseDto> {
    const res = await this.otpRepository.update(id, { acces_token: token });
    return ResponseUtil.success('Successfully', { data: res }, 200);
  }

  async findOneByID(id_otp: string): Promise<findEntityOtpDto> {
    const res: findEntityOtpDto = await this.otpRepository.findOne({
      where: { id: id_otp },
    });
    return res;
  }

  async deleteOTP(params: VerifyEntityDto): Promise<boolean> {
    const payload: OtpJwtDto = this.jwt.decode(params.access);
    this.otpRepository.delete(payload.id_otp);

    return true;
  }

  async addAttempts(id_otp: string, att: number): Promise<boolean> {
    await this.otpRepository.update(id_otp, { attempts: att + 1 });

    return true;
  }

  async deleteOtp(id_otp: string): Promise<boolean> {
    await this.otpRepository.delete(id_otp);

    return true;
  }
}
