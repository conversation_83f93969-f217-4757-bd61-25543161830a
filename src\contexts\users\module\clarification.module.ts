import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { ClarificationFile } from '../domain/entities/clarification-file.entity';
import { ClarificationFileRepositoryImpl } from '../infrastructure/repositories/clarification-file.repository.impl';
import { ClarificationFileController } from '../infrastructure/http/clarification-file.controller';
import { UploadClarificationFileUseCase } from '../application/usecases-clarification-files/upload-clarification-file/upload-clarification-file.usecase';
import { StorageS3Utils } from 'src/contexts/shared/utils/storageUtils/storageS3.utils';
import { Clarification } from '../domain/entities/clarification.entity';
import { ClarificationRepositoryImpl } from '../infrastructure/repositories/clarification.repository.impl';

@Module({
  imports: [TypeOrmModule.forFeature([Clarification, ClarificationFile])], // <-- AÑADIR Clarification
  controllers: [ClarificationFileController],
  providers: [
    UploadClarificationFileUseCase,
    ClarificationFileRepositoryImpl,
    ClarificationRepositoryImpl, // <-- REGISTRAR
    StorageS3Utils,
  ],
  exports: [UploadClarificationFileUseCase],
})
export class ClarificationModule {}
