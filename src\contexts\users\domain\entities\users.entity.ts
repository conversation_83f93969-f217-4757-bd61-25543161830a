import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  Jo<PERSON><PERSON><PERSON>um<PERSON>,
  OneToMany,
} from 'typeorm';
import { Person } from './person.entity';
import { Address } from './address.entity';
import { Admin } from './admin.entity';
import { RelUserRoleAdmin } from './rel-user-role-admin.entity';
import { TransferContact } from './transfer-contact.entity';
import { UserNotifications } from 'src/contexts/user-notifications/domain/entities/user-notification.entity';
import { Exclude } from 'class-transformer';
import { BulkTransfers } from './bulk-transfers.entity';
import { Clarification } from './clarification.entity';

@Entity()
export class Users {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ unique: true })
  email: string;

  @Column('bigint', { nullable: true })
  phone: number;

  @Column()
  password: string;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @Column()
  created_by: string;

  @Column({ default: false })
  enabled: boolean;

  @Column({ default: true })
  isSpeiInEnabled: boolean;

  @Column({ default: true })
  isSpeiOutEnabled: boolean;

  @Column({ nullable: true })
  rfc: string;

  @Column({ type: 'varchar', nullable: true })
  @Exclude() // 👈 excluyendo de respuestas serializadas
  biometricToken?: string;

  @ManyToOne(() => Person, { nullable: true })
  @JoinColumn()
  personIDDock: Person;

  @ManyToOne(() => Admin, (admin) => admin.users, { nullable: true })
  @JoinColumn()
  admin_data: Admin;

  @ManyToOne(() => Address, { nullable: true })
  @JoinColumn()
  address: Address;

  @OneToMany(() => RelUserRoleAdmin, (rel) => rel.user)
  relUserRoleAdmins?: RelUserRoleAdmin[];

  @OneToMany(() => Admin, (admin) => admin.manager)
  admins_managed: Admin[];

  @OneToMany(() => TransferContact, (transferContact) => transferContact.user)
  transferContacts?: TransferContact[];

  @OneToMany(() => UserNotifications, (notification) => notification.user)
  userNotifications: UserNotifications[];

  @OneToMany(() => BulkTransfers, (bulk) => bulk.creationUser)
  bulkTransfers: BulkTransfers[];

  @OneToMany(() => Clarification, (clarification) => clarification.createdBy)
  clarificationsCreated: Clarification[];

  @OneToMany(
    () => Clarification,
    (clarification) => clarification.assignedAdmin,
  )
  clarificationsAssigned: Clarification[];

  @Column({ type: 'varchar', nullable: true })
  convenia_account?: string;

  @Column({ default: false })
  isDeleted: boolean;

  @Column({ type: 'timestamp', nullable: true })
  deletedAt?: Date;
}
