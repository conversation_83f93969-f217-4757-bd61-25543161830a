import { Injectable } from '@nestjs/common';
import { Admin } from '../../domain/entities/admin.entity';
import { AddressParser } from './address.parser';
import { ManagerParser } from './manager.parser';
import { AdminDetailVO, AdminVO, BasicAdminVO } from '../vo/admin.vo';

@Injectable()
export class AdminParser {
  static parseAdminToVO(admin: Admin): AdminDetailVO {
    return {
      id: admin.id,
      companyName: admin.company_name,
      alias: admin.alias,
      rfc: admin.rfc,
      numAsignedCards: admin.num_asigned_cards,
      speiIn: admin.spei_in,
      speiOut: admin.spei_out,
      targetRefound: admin.target_refound,
      ambassador: admin.ambassador,
      createdAt: admin.createdAt,
      groupId: admin.group_id,
      manager: admin.manager
        ? ManagerParser.parseManagerToVO(admin.manager)
        : null,
      address:
        admin.manager && admin.manager.address
          ? AddressParser.parseAddressToVO(admin.manager.address)
          : null,
      files: [],
      amount: '0',
      isSucursal: admin.is_sucursal
    };
  }

  static parseAdminToList(admin: Admin, amount?: string): AdminVO {
    return {
      id: admin.id,
      companyName: admin.company_name,
      alias: admin.alias,
      rfc: admin.rfc,
      numAsignedCards: admin.num_asigned_cards,
      groupId: admin.group_id,
      createdAt: admin.createdAt,
      membershipNumber: admin.membership_number,
      amount,
      manager: admin.manager
        ? ManagerParser.parseManagerToVO(admin.manager)
        : null,
    };
  }

  static parseBasicAdmin(admin: Admin): BasicAdminVO {
    return {
      id: admin.id,
      companyName: admin.company_name,
      alias: admin.alias,
      rfc: admin.rfc,
      numAsignedCards: admin.num_asigned_cards,
      groupId: admin.group_id,
      createdAt: admin.createdAt,
      membershipNumber: admin.membership_number,
    };
  }
}
