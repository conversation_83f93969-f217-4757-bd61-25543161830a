{"info": {"_postman_id": "7535e27b-31d1-4e2c-a1a0-c86a9595b3b6", "name": "REST Operations for an entity", "description": "# 🚀 Here you'll find the methods to communicate with the Finberry database using its RESTful API.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "24067953"}, "item": [{"name": "Entity", "item": [{"name": "Post an entity", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Successful POST request\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"Entity test one\",\n    \"isActive\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/entities", "host": ["{{base_url}}"], "path": ["entities"]}, "description": "This is a POST request, submitting data to an API via the request body. This request submits JSON data, and the data is reflected in the response.\n\nA successful POST request typically returns a `200 OK` or `201 Created` response code."}, "response": []}, {"name": "Update an entity", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Successful PUT request\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n\t\"isActive\": false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/entities/:id", "host": ["{{base_url}}"], "path": ["entities", ":id"], "variable": [{"key": "id", "value": ""}]}, "description": "This is a PUT request and it is used to overwrite an existing piece of data. For instance, after you create an entity with a POST request, you may want to modify that later. You can do that using a PUT request. You typically identify the entity being updated by including an identifier in the URL (eg. `id=1`).\n\nA successful PUT request typically returns a `200 OK`, `201 Created`, or `204 No Content` response code."}, "response": []}, {"name": "Delete an entity", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Successful DELETE request\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 202, 204]);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/entities/:id", "host": ["{{base_url}}"], "path": ["entities", ":id"], "variable": [{"key": "id", "value": ""}]}, "description": "This is a DELETE request, and it is used to delete data that was previously created via a POST request. You typically identify the entity being updated by including an identifier in the URL (eg. `id=1`).\n\nA successful DELETE request typically returns a `200 OK`, `202 Accepted`, or `204 No Content` response code."}, "response": []}, {"name": "Get all entities", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/entities", "host": ["{{base_url}}"], "path": ["entities"]}, "description": "This is a GET request and it is used to \"get\" data from an endpoint. There is no request body for a GET request.\n\nA successful GET response will have a `200 OK` status, and should include some kind of response body - for example, HTML web content or JSON data."}, "response": []}, {"name": "Get an entity", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/entities/:id", "host": ["{{base_url}}"], "path": ["entities", ":id"], "variable": [{"key": "id", "value": ""}]}, "description": "This is a GET request and it is used to \"get\" data from an endpoint. There is no request body for a GET request, but you can use a param to help specify the resource you want data on (e.g., in this request, we have `:id`).\n\nA successful GET response will have a `200 OK` status, and should include some kind of response body - for example, HTML web content or JSON data."}, "response": []}]}, {"name": "<PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/ping", "host": ["{{base_url}}"], "path": ["ping"]}, "description": "This is just a classic ping request method to test the base connectivity and the application controller.\n\nA successful ping request will return the plain text of 'Pong'"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "id", "value": "1"}, {"key": "base_url", "value": "https://postman-rest-api-learner.glitch.me/"}]}