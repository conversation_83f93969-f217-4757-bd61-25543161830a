import {
  Entity as ORMLogsCardsMovements,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
} from 'typeorm';

@ORMLogsCardsMovements('logs_cards_movements')
export class LogsCardsMovements {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: false })
  old_card: string; // Tarjeta eliminada

  @Column({ nullable: false })
  new_card: string; // Tarjeta nueva

  @Column({ nullable: false })
  reason: string; // Motivo

  @CreateDateColumn()
  created_at: Date; // Fecha y hora de creación del registro
}
