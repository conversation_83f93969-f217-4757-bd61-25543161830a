import {
  <PERSON>,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  HttpCode,
  ParseUUIDPipe,
  Query,
  ParseIntPipe,
  UseGuards,
  Req,
} from '@nestjs/common';
import { CreateAdminUseCase } from '../../application/usecases-admin/create-admin/create-admin.usecase';
import { ReadAdminUseCase } from '../../application/usecases-admin/read-admin/read-admin.usecase';
import { ReadAdminsUseCase } from '../../application/usecases-admin/read-admin/read-admins.usecase';
import { DeleteAdminUseCase } from '../../application/usecases-admin/delete-admin/delete-admin.usecase';
import { UpdateAdminUseCase } from '../../application/usecases-admin/update-admin/update-admin.usecase';
import { UpdateAdminDto } from '../../application/usecases-admin/update-admin/update-admin.dto';
import { CreateAdminDto } from '../../application/usecases-admin/create-admin/create-admin.dto';
import { ReadRFCUseCase } from '../../application/usecases-admin/read-rfc/read-rfc.usecase';
import { ApiOkResponse, ApiResponse } from '@nestjs/swagger';
import { Admin } from '../../domain/entities/admin.entity';
import {
  AdminDetailVO,
  AdminListVO,
  AdminOnlyListVO,
  AdminTotalAmount,
  BasicAdminVO,
} from '../vo/admin.vo';
import {
  AdminFilterByUserIdDto,
  AdminFilterDto,
  ReadUsersByAdminFilterDto,
} from '../../application/usecases-admin/read-admin/read-admins-filter.dto';
import { ReadAminTotalAmountUseCase } from '../../application/usecases-admin/read-total-amount/read-total-amount.usecase';
import { ReadAdminTotalAmountDto } from '../../application/usecases-admin/read-total-amount/read-total-amount.dto';
import { JwtAuthGuard } from 'src/contexts/auth/guards/auth.guard';
import { RoleProtected } from 'src/contexts/auth/decorators/role-protected.decorator';
import { RoleEnum } from 'src/contexts/shared/enums/roles.enum';
import { UserRoleGuard } from 'src/contexts/auth/guards/user-role.guard';
import { SkipAuth } from 'src/contexts/auth/decorators/skip-auth.decorator';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { AdminUsersListVO } from '../vo/user.vo';
import { CreateAdminMassiveUseCase } from '../../application/usecases-admin/create-admin-massive/create-admin-massive.usecase';
import { CreateAdminMassiveDto } from '../../application/usecases-admin/create-admin-massive/create-admin-massive.dto';
import { ReadTotalCardsUseCase } from '../../application/usecases-admin/read-total-cards/read-total-cards.usecase';

@UseGuards(JwtAuthGuard)
@Controller('admin')
export class AdminController {
  constructor(
    private readonly createAdminUseCase: CreateAdminUseCase,
    private readonly readAdminUseCase: ReadAdminUseCase,
    private readonly readAdminsUseCase: ReadAdminsUseCase,
    private readonly deleteAdminUseCase: DeleteAdminUseCase,
    private readonly updateAdminUseCase: UpdateAdminUseCase,
    private readonly readRFCUseCase: ReadRFCUseCase,
    private readonly readAminTotalAmountUseCase: ReadAminTotalAmountUseCase,
    private readonly createAdminMassiveUseCase: CreateAdminMassiveUseCase,
    private readonly readTotalCardsUseCase : ReadTotalCardsUseCase
  ) {}

  @Post('')
  @HttpCode(201)
  @ApiResponse({
    status: 201,
    type: Admin,
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict exception',
  })
  @RoleProtected(RoleEnum.CONVENIA_ADMIN)
  @UseGuards(UserRoleGuard)
  createByApp(@Body() createAdminDto: CreateAdminDto) {
    return this.createAdminUseCase.execute(createAdminDto);
  }

  @Get('')
  @HttpCode(200)
  @RoleProtected(RoleEnum.CONVENIA_ADMIN, RoleEnum.CONVENIA_ADMIN_CONSULTOR)
  @UseGuards(UserRoleGuard)
  @ApiResponse({
    status: 200,
    type: AdminListVO,
  })
  findAll(@Query() filter: AdminFilterDto) {
    return this.readAdminsUseCase.readAll(filter);
  }

  @Get('allByUserId')
  @HttpCode(200)
  @RoleProtected(
    RoleEnum.CONVENIA_ADMIN,
    RoleEnum.CLIENTE_ADMIN,
    RoleEnum.CLIENTE_TESORERO,
    RoleEnum.CLIENTE_GESTOR_TARJETAHABIENTES,
  )
  @UseGuards(UserRoleGuard)
  @ApiOkResponse({ type: ApiResponseDto })
  findAllByUserId(@Query() filter: AdminFilterByUserIdDto, @Req() req: any) {
    const { user } = req;
    return this.readAdminsUseCase.readAllByUserId(
      filter.userId,
      user.relUserRoleAdmins[0].role.name,
    );
  }

  @Get('amount')
  @HttpCode(200)
  @ApiResponse({
    status: 200,
    type: AdminTotalAmount,
  })
  getTotalAmount(@Query() dto: ReadAdminTotalAmountDto) {
    return this.readAminTotalAmountUseCase.execute(dto);
  }
  @Get('basic')
  @HttpCode(200)
  @ApiResponse({
    status: 200,
    type: [BasicAdminVO],
  })
  getBasicAdmins() {
    return this.readAdminsUseCase.readAllBasic();
  }

  @Get(':id')
  @RoleProtected(RoleEnum.CONVENIA_ADMIN, RoleEnum.CONVENIA_ADMIN_CONSULTOR)
  @UseGuards(UserRoleGuard)
  @HttpCode(200)
  @ApiResponse({
    status: 200,
    type: AdminDetailVO,
  })
  @ApiResponse({
    status: 404,
    description: 'Admin not found',
  })
  findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.readAdminUseCase.readById(id);
  }

  @Get('findAdminByMemebershipNumber/:memebership_number')
  @SkipAuth()
  findAdminByMemebershipNumber(
    @Param('memebership_number') memebership_number: number,
  ) {
    return this.readAdminUseCase.executeMemebershipNumber(memebership_number);
  }

  @Patch(':id')
  @RoleProtected(RoleEnum.CONVENIA_ADMIN)
  @UseGuards(UserRoleGuard)
  @HttpCode(204)
  @ApiResponse({
    status: 204,
    description: 'Admin updated successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Not found exception',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict exception',
  })
  async update(
    @Param('id') id: string,
    @Body() updateAdminDto: UpdateAdminDto,
  ) {
    await this.updateAdminUseCase.execute(id, updateAdminDto);
  }

  @Delete(':id')
  @RoleProtected(RoleEnum.CONVENIA_ADMIN)
  @UseGuards(UserRoleGuard)
  remove(@Param('id', ParseUUIDPipe) id: string) {
    return this.deleteAdminUseCase.execute(id);
  }

  @Delete(':id/document/:documentId')
  @RoleProtected(RoleEnum.CONVENIA_ADMIN)
  @UseGuards(UserRoleGuard)
  deleteDocument(@Param('documentId', ParseIntPipe) id: number) {
    return this.deleteAdminUseCase.deleteDocument(id);
  }

  @Get('rfc/:rfc')
  @HttpCode(200)
  @ApiResponse({
    status: 200,
    type: Admin,
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  findByRFC(@Param('rfc') rfc: string) {
    return this.readRFCUseCase.execute(rfc);
  }

  @Get(':id/users')
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved users',
    type: AdminUsersListVO,
  })
  getUserByAdmin(
    @Param('id') id: string,
    @Query() dto: ReadUsersByAdminFilterDto,
  ) {
    return this.readAdminUseCase.getUsers(id, dto);
  }

  @Post('massive')
  @HttpCode(201)
  @ApiResponse({
    status: 201,
    type: [Admin],
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict exception',
  })
  @RoleProtected(RoleEnum.CONVENIA_ADMIN)
  @UseGuards(UserRoleGuard)
  createMassiveByApp(@Body() body: CreateAdminMassiveDto) {
    return this.createAdminMassiveUseCase.execute(
      body.admins,
      body.creatorEmail,
      body.name,
    );
  }

  @Get(':id/total-cards')
  @HttpCode(200)
  getTotalCards(@Param('id') id: string) {
    return this.readTotalCardsUseCase.execute(id);
  }
}
