import { BaseEntity, Column, Entity, OneToOne, PrimaryColumn, PrimaryGeneratedColumn } from "typeorm";
import { AdminDocuments } from "./admin-documents.entity";


@Entity()
export class S3Metadata extends BaseEntity{
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    key: string;

    @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: string;

    @OneToOne(() => AdminDocuments, adminDocuments => adminDocuments.file)
    document: AdminDocuments;

    @OneToOne(() => AdminDocuments, adminDocuments => adminDocuments.file)
    bulkTransfer: AdminDocuments;
}
