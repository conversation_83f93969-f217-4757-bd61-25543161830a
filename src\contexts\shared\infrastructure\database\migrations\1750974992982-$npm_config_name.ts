import { MigrationInterface, QueryRunner } from "typeorm";

export class  $npmConfigName1750974992982 implements MigrationInterface {
    name = ' $npmConfigName1750974992982'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "logs_cards_movements" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "old_card" character varying NOT NULL, "new_card" character varying NOT NULL, "reason" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_8a8d3580f2b07ffc52f28012599" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "logs_cards_movements"`);
    }

}
