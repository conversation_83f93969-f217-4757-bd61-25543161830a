import { IsNotEmpty, IsString } from 'class-validator';

export class ParamsTransferDigitVerifierDto {
  @IsString()
  @IsNotEmpty()
  clabe: string;
}

export class ResponseClabeDto {
  @IsString()
  account_clabe: string;
}

export class CreateClabeDto {
  @IsString()
  @IsNotEmpty()
  clabe: string;

  @IsString()
  @IsNotEmpty()
  person_id: string;

  @IsString()
  @IsNotEmpty()
  limit_id: string;
}

export class ResponseCreateClabeDto {
  @IsNotEmpty()
  @IsString()
  id: string;
}

export class ParamsCreateClabeDto {
  @IsString()
  @IsNotEmpty()
  person_id: string;

  @IsString()
  @IsNotEmpty()
  limit_id: string;
}
