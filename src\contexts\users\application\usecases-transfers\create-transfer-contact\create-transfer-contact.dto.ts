import { IsOptional, IsString, IsUUID } from 'class-validator';

export class CreateTransferContactDto {
  @IsString()
  name: string;

  @IsString()
  num_clabe: string;

  @IsString()
  bank_institution: string;

  @IsString()
  @IsOptional()
  rfc?: string;

  @IsString()
  @IsOptional()
  alias?: string;

  @IsString()
  @IsOptional()
  company_name?: string;

  @IsUUID()
  userId: string;

  @IsString()
  @IsOptional()
  email?: string;
}
