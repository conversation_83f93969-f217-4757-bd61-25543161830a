import { Injectable } from '@nestjs/common';
import { CreateEntityDto } from 'src/contexts/entity/application/create-entity/create-entity.dto';
import { EntityRepositoryImpl } from 'src/contexts/entity/infrastructure/repositories/entity.repository.impl';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';

@Injectable()
export class CreateEntityUseCase {
  constructor(private readonly entityRepositoryImpl: EntityRepositoryImpl) {}

  async execute(entity: CreateEntityDto): Promise<ApiResponseDto> {
    return this.entityRepositoryImpl.save(entity);
  }
}
