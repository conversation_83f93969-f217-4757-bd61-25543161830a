import {
  <PERSON>,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  UseGuards,
  HttpStatus,
  HttpCode,
  Query,
  ParseUUIDPipe,
} from '@nestjs/common';
import { CreateUserUseCase } from '../../application/usecases-user/create-user/create-user.usecase';
import { ReadUserUseCase } from '../../application/usecases-user/read-user/read-user.usecase';
import { ReadUsersUseCase } from '../../application/usecases-user/read-user/read-users.usecase';
import { DeleteUserUseCase } from '../../application/usecases-user/delete-user/delete-user.usecase';
import { UpdateUserUseCase } from '../../application/usecases-user/update-user/update-user.usecase';
import { CreateConveniaAccountUseCase } from '../../application/usecases-user/create-convenia-account/create-convenia-account.usecase';
import { GetUserAccountUseCase } from '../../application/usecases-user/get-user-account/get-user-account.usecase';
import {
  CreateConveniaUserDto,
  CreateUserDto,
} from '../../application/usecases-user/create-user/create-user.dto';
import {
  UpdateConveniUserDto,
  UpdateUserDto,
} from '../../application/usecases-user/update-user/update-user.dto';
import { RoleProtected } from 'src/contexts/auth/decorators/role-protected.decorator';
import { RoleEnum } from 'src/contexts/shared/enums/roles.enum';
import { AuthGuard } from '@nestjs/passport';
import { UserRoleGuard } from 'src/contexts/auth/guards/user-role.guard';
import { UpdateUserPassDto } from '../../application/usecases-user/update-user/update-user-pass.dto';
import { ReadTotalUsersUseCase } from '../../application/usecases-user/read-total-users/read-total-users.usecase';
import { ApiResponse } from '@nestjs/swagger';
import {
  ConveniaUserVO,
  TotalUsersVO,
  ConveniUsersListVO,
} from '../vo/user.vo';
import { FilterConveniaUsersDto } from '../../application/usecases-user/read-user/read-user.dto';
import { JwtAuthGuard } from 'src/contexts/auth/guards/auth.guard';
import { SkipAuth } from 'src/contexts/auth/decorators/skip-auth.decorator';
import { ReadTotalUsersDto } from '../../application/usecases-user/read-total-users/read-total-users.dto';
import { GetUserDecorator } from 'src/contexts/auth/decorators/get-user.decorator';
import { Users } from '../../domain/entities/users.entity';
import { ResponseCreateConveniaAccountDto } from '../../application/usecases-user/create-convenia-account/create-convenia-account.dto';
import { AssignRoleDto } from '../../application/usecases-user/asign-role/asign-role.dto';
import { ResponseGetUserAccountDto } from '../../application/usecases-user/get-user-account/get-user-account.dto';
import { AssignRoleUseCase } from '../../application/usecases-user/asign-role/asign-role.usecase';

@UseGuards(JwtAuthGuard)
@Controller('user')
export class UserController {
  constructor(
    private readonly createUserUseCase: CreateUserUseCase,
    private readonly readUserUseCase: ReadUserUseCase,
    private readonly readUsersUseCase: ReadUsersUseCase,
    private readonly deleteUserUseCase: DeleteUserUseCase,
    private readonly updateUserUseCase: UpdateUserUseCase,
    private readonly readTotalUsersUseCase: ReadTotalUsersUseCase,
    private readonly convenia: CreateConveniaAccountUseCase,
    private readonly assignRoleUseCase: AssignRoleUseCase,
    private readonly account: GetUserAccountUseCase,
  ) {}

  @Post('app/create')
  @SkipAuth()
  createByApp(@Body() createUserDto: CreateUserDto) {
    return this.createUserUseCase.executeApp(createUserDto);
  }

  @Post('convenia')
  @ApiResponse({
    status: 201,
    description: 'Convenia user created',
    type: ConveniaUserVO,
  })
  @ApiResponse({
    status: 409,
    description: 'Conflic exception',
  })
  createByDash(@Body() createUserDto: CreateConveniaUserDto) {
    return this.createUserUseCase.executeConveniaUser(createUserDto);
  }

  @Patch('convenia/:id')
  @ApiResponse({
    status: 200,
    description: 'Convenia user created',
    type: ConveniaUserVO,
  })
  @ApiResponse({
    status: 409,
    description: 'Conflic exception',
  })
  updateConveniaUser(
    @Param('id') userId: string,
    @Body() dto: UpdateConveniUserDto,
  ) {
    return this.updateUserUseCase.excecuteUpdateConveniaUser(userId, dto);
  }

  @Get('convenia')
  @ApiResponse({
    status: 200,
    type: ConveniUsersListVO,
  })
  getConveniUsers(
    @Query() filter: FilterConveniaUsersDto,
    @GetUserDecorator() user: Users,
  ) {
    filter.userId = user.id;
    filter.roleType = user.relUserRoleAdmins?.[0]?.role?.type;
    filter.roleName = user.relUserRoleAdmins?.[0]?.role?.name;
    return this.readUsersUseCase.executeConveniaUsers(filter);
  }

  @Get('convenia/:id')
  @ApiResponse({
    status: 200,
    type: ConveniaUserVO,
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  getConveniaUser(@Param('id') id: string) {
    return this.readUserUseCase.executeConveniaUser(id);
  }
  @Post('admin/create')
  createByAdmin(@Body() createUserDto: CreateUserDto) {
    return this.createUserUseCase.executeAdmin(createUserDto);
  }

  @Get('all')
  findAll() {
    return this.readUsersUseCase.execute();
  }

  @Get('find/:id')
  findOne(@Param('id') id: string) {
    return this.readUserUseCase.execute(id);
  }

  @Get('findByEmail/:email')
  @SkipAuth()
  findByEmail(@Param('email') email: string) {
    const result = this.readUserUseCase.executeEmail(email);
    return result;
  }

  @Get('findUserWithAdmin/:email')
  @SkipAuth()
  async getUserWithAdminSettingsByEmail(@Param('email') email: string) {
    const result =
      await this.readUserUseCase.findUserWithAdminSettingsByEmail(email);

    return result;
  }

  @Patch('update/:id')
  update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.updateUserUseCase.execute(id, updateUserDto);
  }

  @SkipAuth()
  @Patch('resetPass')
  resetPass(@Body() updateUserDto: UpdateUserDto) {
    return this.updateUserUseCase.executeResetPass(updateUserDto);
  }

  @Patch('changePass')
  changePass(@Body() updateUserDto: UpdateUserPassDto) {
    return this.updateUserUseCase.executeChangePass(updateUserDto);
  }

  @Delete(':id')
  @RoleProtected(RoleEnum.CLIENTE_ADMIN, RoleEnum.CONVENIA_ADMIN)
  @UseGuards(AuthGuard(), UserRoleGuard)
  remove(@Param('id') id: string, @Query('adminId') adminId?: string) {
    return this.deleteUserUseCase.execute(id, adminId);
  }

  @Get('total-accounts')
  @HttpCode(HttpStatus.OK)
  @ApiResponse({
    status: 200,
    description: 'Total number of user accounts retrieved successfully.',
    type: TotalUsersVO,
  })
  readTotalUsers(@Query() query: ReadTotalUsersDto): Promise<TotalUsersVO> {
    return this.readTotalUsersUseCase.execute(query);
  }

  @Get('total-trasnfer-accounts')
  @HttpCode(HttpStatus.OK)
  @ApiResponse({
    status: 200,
    description: 'Total number of transfer accounts retrieved successfully.',
    type: TotalUsersVO,
  })
  readTotalTransferAccounts(
    @Query() query: ReadTotalUsersDto,
  ): Promise<TotalUsersVO> {
    return this.readTotalUsersUseCase.executeTransferAccounts(query);
  }

  @Get('email-registered')
  @ApiResponse({
    status: 200,
    description: 'Check if an email is registered successfully.',
    type: Boolean,
  })
  @SkipAuth()
  isEmailRegistered(@Query('email') email: string): Promise<boolean> {
    return this.readUserUseCase.executeIsEmailRegistered(email);
  }

  @Post('create-convenia-account')
  @RoleProtected(RoleEnum.CONVENIA_ADMIN, RoleEnum.CLIENTE_ADMIN)
  @UseGuards(AuthGuard(), UserRoleGuard)
  async createConveniaAccount(): Promise<ResponseCreateConveniaAccountDto[]> {
    try {
      const response = await this.convenia.execute();

      return response;
    } catch (error) {
      throw new Error('Error creating Convenia account: ' + error.message);
    }
  }

  @ApiResponse({
    status: 201,
    description: 'Role assigned successfully',
  })
  @HttpCode(HttpStatus.CREATED)
  @Post(':id/role')
  async asignRole(
    @Body() dto: AssignRoleDto,
    @Param('id', ParseUUIDPipe) userId: string,
  ): Promise<void> {
    dto.userId = userId;
    await this.assignRoleUseCase.execute(dto);
  }

  @Get('account-convenia/:user_id')
  async getUserAccount(
    @Param('user_id') user_id: string,
  ): Promise<ResponseGetUserAccountDto> {
    try {
      return await this.account.execute({
        user_id,
      });
    } catch (error) {
      return {
        statusCode: error.status || HttpStatus.INTERNAL_SERVER_ERROR,
        message: error.message || 'Internal Server Error',
        error: error.name || 'Error',
      };
    }
  }
}
