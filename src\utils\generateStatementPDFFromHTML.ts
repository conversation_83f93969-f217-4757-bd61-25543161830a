/* eslint-disable @typescript-eslint/no-explicit-any */
import { StatementBalance, StatementMovement } from '@/types/statement/types'
import { getAccountStatement } from '@/api/endpoints/user'
import { Transaction } from '@/hooks/useListTransactions'

interface AccountInfo {
  name?: string
  convenia_account?: string
  accountNumberTransfer?: string
  admin?: {
    companyName?: string
    rfc?: string
  }
}

// Usar los tipos existentes de la API
type BalanceInfo = StatementBalance
type MovementInfo = StatementMovement

// Función para formatear moneda
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('es-MX', {
    style: 'currency',
    currency: 'MXN'
  }).format(amount)
}

// Función para cargar el template HTML
const loadTemplate = async (templateName: string = 'account-statement'): Promise<string> => {
  try {
    const response = await fetch(`/templates/${templateName}.html`)
    return await response.text()
  } catch (error) {
    console.error('Error loading template:', error)
    throw new Error(`No se pudo cargar el template ${templateName}.html`)
  }
}

// Función para calcular el período de un mes atrás desde la fecha actual
const calculateLastMonthPeriod = (): { startDate: Date; endDate: Date; periodString: string } => {
  const now = new Date()
  const endDate = new Date(now)
  const startDate = new Date(now)
  startDate.setMonth(now.getMonth() - 1)

  const startMonth = startDate.toLocaleDateString('es-MX', { month: 'long', year: 'numeric' })
  const endMonth = endDate.toLocaleDateString('es-MX', { month: 'long', year: 'numeric' })

  // Si es el mismo mes y año, mostrar solo el mes
  if (startDate.getMonth() === endDate.getMonth() && startDate.getFullYear() === endDate.getFullYear()) {
    return {
      startDate,
      endDate,
      periodString: startMonth
    }
  }

  // Si es diferente mes/año, mostrar rango
  return {
    startDate,
    endDate,
    periodString: `${startMonth} - ${endMonth}`
  }
}

// Función para filtrar movimientos del último mes
const filterLastMonthMovements = (movements?: MovementInfo[]): MovementInfo[] => {
  if (!movements || movements.length === 0) {
    return []
  }

  const { startDate, endDate } = calculateLastMonthPeriod()

  return movements.filter(movement => {
    // Intentar parsear la fecha en diferentes formatos
    const dateStr = movement.fechaOperacion
    let date: Date | null = null

    // Formato DD/MM/YYYY
    if (dateStr.includes('/')) {
      const [day, month, year] = dateStr.split('/')
      date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day))
    }
    // Formato YYYY-MM-DD
    else if (dateStr.includes('-')) {
      date = new Date(dateStr)
    }

    if (!date || isNaN(date.getTime())) {
      return false
    }

    // Filtrar movimientos del último mes
    return date >= startDate && date <= endDate
  })
}

// Función para reemplazar placeholders en el template
const replacePlaceholders = (
  template: string,
  accountInfo: AccountInfo,
  balanceInfo?: BalanceInfo,
  movements?: MovementInfo[]
): string => {
  // Filtrar movimientos del último mes y calcular período
  const filteredMovements = filterLastMonthMovements(movements)
  const { periodString } = calculateLastMonthPeriod()

  // Datos del cliente
  const clientData = {
    fecha: periodString,
    denominacionSocial: accountInfo.admin?.companyName || '-',
    nombreComercial: accountInfo.name || '-',
    numeroCuenta: accountInfo.convenia_account || '-',
    numeroClabe: accountInfo.accountNumberTransfer || '-',
    rfc: accountInfo.admin?.rfc || '-',
    direccionFiscal: 'Dirección fiscal no disponible'
  }
  
  // Datos financieros
  const financialData = balanceInfo ? {
    saldoAnterior: formatCurrency(balanceInfo.saldoAnterior),
    depositos: formatCurrency(balanceInfo.depositos),
    retiros: formatCurrency(balanceInfo.retiros),
    saldoFinal: formatCurrency(balanceInfo.saldoFinal),
    promedioAbonos: formatCurrency(balanceInfo.promedioSaldosDiariosAbonos),
    promedioCargos: formatCurrency(balanceInfo.promedioSaldosDiariosCargos)
  } : {
    saldoAnterior: '$ -',
    depositos: '$ -',
    retiros: '$ -',
    saldoFinal: '$ -',
    promedioAbonos: '$ -',
    promedioCargos: '$ -'
  }
  
  // Generar filas de movimientos usando los movimientos filtrados
  let movimientosHTML = ''
  if (filteredMovements && filteredMovements.length > 0) {
    movimientosHTML = filteredMovements.slice(0, 10).map(movement => `
      <tr>
        <td>${movement.fechaOperacion}</td>
        <td>${movement.fechaLiquidacion}</td>
        <td>${movement.concepto}</td>
        <td>${movement.claveRastreo}</td>
        <td>${movement.cargos > 0 ? formatCurrency(movement.cargos) : '-'}</td>
        <td>${movement.abonos > 0 ? formatCurrency(movement.abonos) : '-'}</td>
        <td>${formatCurrency(movement.saldos)}</td>
      </tr>
    `).join('')
  } else {
    movimientosHTML = `
      <tr class="empty-row">
        <td colspan="7" style="text-align: center;">
          No hay movimientos disponibles
        </td>
      </tr>
    `
  }
  
  // Reemplazar todos los placeholders
  let result = template
  
  // Reemplazar datos del cliente
  Object.entries(clientData).forEach(([key, value]) => {
    result = result.replace(new RegExp(`{{${key}}}`, 'g'), value)
  })
  
  // Reemplazar datos financieros
  Object.entries(financialData).forEach(([key, value]) => {
    result = result.replace(new RegExp(`{{${key}}}`, 'g'), value)
  })
  
  // Reemplazar movimientos
  result = result.replace('{{movimientos}}', movimientosHTML)
  
  return result
}

// Función principal para generar PDF desde HTML
export const generateStatementPDFFromHTML = async (
  accountInfo: AccountInfo,
  cardNumber: string,
  balanceInfo?: BalanceInfo,
  movements?: MovementInfo[]
): Promise<void> => {
  try {
    // Cargar template
    const template = await loadTemplate()
    
    // Reemplazar placeholders
    const htmlContent = replacePlaceholders(template, accountInfo, balanceInfo, movements)
    
    // Crear un elemento temporal para renderizar el HTML
    const printWindow = window.open('', '_blank')
    if (!printWindow) {
      throw new Error('No se pudo abrir la ventana de impresión')
    }
    
    // Escribir el HTML en la nueva ventana
    printWindow.document.write(htmlContent)
    printWindow.document.close()
    
    // Esperar a que se cargue completamente
    printWindow.onload = () => {
      // Configurar para imprimir como PDF
      printWindow.print()
      
      // Cerrar la ventana después de un momento
      setTimeout(() => {
        printWindow.close()
      }, 1000)
    }
    
  } catch (error) {
    console.error('Error generando PDF desde HTML:', error)
    throw error
  }
}

// Función alternativa usando html2canvas + jsPDF (si prefieres descarga automática)
export const generateStatementPDFDownload = async (
  accountInfo: AccountInfo,
  cardNumber: string,
  balanceInfo?: BalanceInfo,
  movements?: MovementInfo[]
): Promise<void> => {
  try {
    const { default: html2canvas } = await import('html2canvas')
    const { default: jsPDF } = await import('jspdf')

    const pdf = new jsPDF('p', 'mm', 'a4')
    const firstPageMovements = 3 // Movimientos en la primera página
    const continuationMovementsPerPage = 15 // Movimientos por página de continuación

    // Filtrar movimientos del último mes
    const filteredMovements = filterLastMonthMovements(movements)

    // Primera página con información completa (solo 3 movimientos)
    const template = await loadTemplate('account-statement')
    const firstPageMovementsList = filteredMovements?.slice(0, firstPageMovements)
    const htmlContent = replacePlaceholders(template, accountInfo, balanceInfo, firstPageMovementsList)

    await addPageToPDF(pdf, htmlContent, html2canvas, true)

    // Páginas adicionales si hay más movimientos filtrados
    if (filteredMovements && filteredMovements.length > firstPageMovements) {
      const remainingMovements = filteredMovements.slice(firstPageMovements)
      const continuationTemplate = await loadTemplate('account-statement-continuation')

      for (let i = 0; i < remainingMovements.length; i += continuationMovementsPerPage) {
        const pageMovements = remainingMovements.slice(i, i + continuationMovementsPerPage)
        const pageNumber = Math.floor(i / continuationMovementsPerPage) + 2

        let continuationHTML = continuationTemplate

        // Reemplazar número de página
        continuationHTML = continuationHTML.replace('{{pageNumber}}', pageNumber.toString())

        // Generar filas de movimientos para esta página
        const movimientosHTML = pageMovements.map(movement => `
          <tr>
            <td>${movement.fechaOperacion}</td>
            <td>${movement.fechaLiquidacion}</td>
            <td>${movement.concepto}</td>
            <td>${movement.claveRastreo}</td>
            <td>${movement.cargos > 0 ? formatCurrency(movement.cargos) : '-'}</td>
            <td>${movement.abonos > 0 ? formatCurrency(movement.abonos) : '-'}</td>
            <td>${formatCurrency(movement.saldos)}</td>
          </tr>
        `).join('')

        continuationHTML = continuationHTML.replace('{{movimientos}}', movimientosHTML)

        await addPageToPDF(pdf, continuationHTML, html2canvas, false)
      }
    }

    // Descargar
    const today = new Date()
    const fileName = `estado-cuenta-${cardNumber}-${today.toISOString().slice(0, 10)}.pdf`
    pdf.save(fileName)

  } catch (error) {
    console.error('Error generando PDF con descarga automática:', error)
    throw error
  }
}

// Función para filtrar transacciones del último mes
const filterLastMonthTransactions = (transactions: Transaction[]): Transaction[] => {
  if (!transactions || transactions.length === 0) {
    return []
  }

  const { startDate, endDate } = calculateLastMonthPeriod()

  return transactions.filter(tx => {
    const txDate = new Date(tx.date)
    if (isNaN(txDate.getTime())) {
      return false
    }

    // Filtrar transacciones del último mes
    return txDate >= startDate && txDate <= endDate
  })
}

// Función para convertir transacciones locales al formato de movimientos
const convertTransactionsToMovements = (transactions: Transaction[]): MovementInfo[] => {
  // Filtrar transacciones del último mes antes de convertir
  const filteredTransactions = filterLastMonthTransactions(transactions)

  return filteredTransactions.map(tx => ({
    fechaOperacion: tx.dateFormatted || tx.date,
    fechaLiquidacion: tx.dateFormatted || tx.date,
    concepto: tx.description || 'Sin concepto',
    claveRastreo: tx.key || tx.id || '-',
    cargos: tx.type === 'debtor' ? Math.abs(parseFloat(tx.amount.replace(/[^0-9.-]/g, ''))) : 0,
    abonos: tx.type === 'creditor' ? Math.abs(parseFloat(tx.amount.replace(/[^0-9.-]/g, ''))) : 0,
    saldos: 0 // No tenemos saldo acumulado en las transacciones locales
  }))
}

// Función para calcular balance desde transacciones
const calculateBalanceFromTransactions = (transactions: Transaction[]): BalanceInfo => {
  // Filtrar transacciones del último mes antes de calcular el balance
  const filteredTransactions = filterLastMonthTransactions(transactions)
  const movements = filteredTransactions.map(tx => ({
    fechaOperacion: tx.dateFormatted || tx.date,
    fechaLiquidacion: tx.dateFormatted || tx.date,
    concepto: tx.description || 'Sin concepto',
    claveRastreo: tx.key || tx.id || '-',
    cargos: tx.type === 'debtor' ? Math.abs(parseFloat(tx.amount.replace(/[^0-9.-]/g, ''))) : 0,
    abonos: tx.type === 'creditor' ? Math.abs(parseFloat(tx.amount.replace(/[^0-9.-]/g, ''))) : 0,
    saldos: 0 // No tenemos saldo acumulado en las transacciones locales
  }))

  const depositos = movements.reduce((sum, mov) => sum + mov.abonos, 0)
  const retiros = movements.reduce((sum, mov) => sum + mov.cargos, 0)

  return {
    saldoAnterior: 0, // No tenemos saldo anterior
    depositos,
    retiros,
    saldoFinal: depositos - retiros,
    promedioSaldosDiariosAbonos: depositos / Math.max(movements.length, 1),
    promedioSaldosDiariosCargos: retiros / Math.max(movements.length, 1)
  }
}

// Función principal que usa datos locales de transacciones
export const generateStatementPDFFromTransactions = async (
  accountInfo: AccountInfo,
  transactions: Transaction[],
  useDownload: boolean = true
): Promise<void> => {
  try {
    // Validar parámetros
    if (!accountInfo) {
      throw new Error('Faltan parámetros requeridos: accountInfo')
    }

    // Asegurar que transactions sea un array (puede estar vacío)
    const safeTransactions = transactions || []

    // Convertir transacciones al formato esperado
    const movements = convertTransactionsToMovements(safeTransactions)
    const balanceInfo = calculateBalanceFromTransactions(safeTransactions)

    // Usar la función apropiada según la preferencia
    if (useDownload) {
      await generateStatementPDFDownload(
        accountInfo,
        accountInfo.convenia_account || 'N/A',
        balanceInfo,
        movements
      )
    } else {
      await generateStatementPDFFromHTML(
        accountInfo,
        accountInfo.convenia_account || 'N/A',
        balanceInfo,
        movements
      )
    }

  } catch (error) {
    console.error('Error generando PDF desde transacciones locales:', error)

    // Re-lanzar el error con un mensaje más descriptivo
    if (error instanceof Error) {
      throw new Error(`Error al generar el estado de cuenta: ${error.message}`)
    } else {
      throw new Error('Error desconocido al generar el estado de cuenta')
    }
  }
}

// Función principal que integra con la API (mantener para compatibilidad)
export const generateStatementPDFFromAPI = async (
  email: string,
  startDate: string,
  endDate: string,
  useDownload: boolean = true
): Promise<void> => {
  try {
    // Validar parámetros
    if (!email || !startDate || !endDate) {
      throw new Error('Faltan parámetros requeridos: email, startDate, endDate')
    }

    // Obtener datos de la API
    const response = await getAccountStatement({
      email,
      startDate,
      endDate
    })

    if (response.statusCode !== 200) {
      throw new Error(response.message || 'Error al obtener el estado de cuenta')
    }

    const statementData = response.data

    // Validar que tenemos los datos necesarios
    if (!statementData || !statementData.clientInfo) {
      throw new Error('Los datos del estado de cuenta están incompletos')
    }

    // Convertir datos de la API al formato esperado por las funciones existentes
    const accountInfo: AccountInfo = {
      name: statementData.clientInfo.nombreComercial || 'N/A',
      convenia_account: statementData.clientInfo.numeroCuenta || 'N/A',
      accountNumberTransfer: statementData.clientInfo.numeroClientePrefijo || 'N/A',
      admin: {
        companyName: statementData.clientInfo.denominacionSocial || 'N/A',
        rfc: statementData.clientInfo.rfc || 'N/A'
      }
    }

    const balanceInfo: BalanceInfo = statementData.balance || {
      saldoAnterior: 0,
      depositos: 0,
      retiros: 0,
      saldoFinal: 0,
      promedioSaldosDiariosAbonos: 0,
      promedioSaldosDiariosCargos: 0
    }

    const movements: MovementInfo[] = statementData.movements || []

    // Usar la función apropiada según la preferencia
    if (useDownload) {
      await generateStatementPDFDownload(
        accountInfo,
        statementData.clientInfo.numeroCuenta || 'N/A',
        balanceInfo,
        movements
      )
    } else {
      await generateStatementPDFFromHTML(
        accountInfo,
        statementData.clientInfo.numeroCuenta || 'N/A',
        balanceInfo,
        movements
      )
    }

  } catch (error) {
    console.error('Error generando PDF desde API:', error)

    // Re-lanzar el error con un mensaje más descriptivo
    if (error instanceof Error) {
      throw new Error(`Error al generar el estado de cuenta: ${error.message}`)
    } else {
      throw new Error('Error desconocido al generar el estado de cuenta')
    }
  }
}

// Función auxiliar para agregar una página al PDF
const addPageToPDF = async (
  pdf: any,
  htmlContent: string,
  html2canvas: any,
  isFirstPage: boolean
): Promise<void> => {
  // Crear elemento temporal
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = htmlContent
  tempDiv.style.position = 'absolute'
  tempDiv.style.left = '-9999px'
  tempDiv.style.width = '210mm'
  tempDiv.style.height = 'auto'
  tempDiv.style.minHeight = '297mm'
  document.body.appendChild(tempDiv)

  // Esperar a que se carguen las imágenes
  await new Promise(resolve => setTimeout(resolve, 500))

  // Convertir a canvas
  const canvas = await html2canvas(tempDiv, {
    height: 1123, // 297mm en pixels (96 DPI)
    width: 794,  // 210mm en pixels (96 DPI)
    scale: 2,
    useCORS: true,
    allowTaint: true,
    backgroundColor: '#ffffff'
  })

  // Agregar nueva página si no es la primera
  if (!isFirstPage) {
    pdf.addPage()
  }

  const imgData = canvas.toDataURL('image/png')
  pdf.addImage(imgData, 'PNG', 0, 0, 210, 297)

  // Limpiar
  document.body.removeChild(tempDiv)
}
