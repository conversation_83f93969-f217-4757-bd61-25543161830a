import {
  IsString,
  <PERSON><PERSON><PERSON>ber,
  IsOptional,
  IsArray,
  ValidateNested,
  IsUUID,
} from 'class-validator';

import { Type } from 'class-transformer';

class MetaDataAliasCore {
  @IsString()
  card_id: string;

  @IsString()
  card_rail: string;
}

class AliasCore {
  @IsString()
  alias_key: string;

  @ValidateNested({ each: true })
  @Type(() => MetaDataAliasCore)
  metadata: MetaDataAliasCore;
}

export class ArrayDataCard {
  @IsString()
  id: string;

  @IsString()
  card_type: string;

  @IsString()
  pan: string;

  @IsString()
  expiration_date: string;

  @IsString()
  card_last_4: string;

  @IsString()
  format_expiration_date: string;

  @IsString()
  status: string;
}

export class ParamsListCards {
  @IsString()
  @IsUUID()
  account_dock_id: string;
}

export class ResListCards {
  @IsNumber()
  statusCode: number;

  @IsString()
  message: string;

  @IsOptional()
  @IsString()
  error?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ArrayDataCard)
  data?: ArrayDataCard[];
}

export class DataAliasCore {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AliasCore)
  content: AliasCore[];
}

export class ParamsSensitiveData {
  @IsString()
  @IsUUID()
  card_dock_id: string;
}

export class ParamsListCard {
  @IsString()
  @IsUUID()
  card_dock_id: string;
}

export class ParamsFindCardDockPin {
  @IsString()
  @IsUUID()
  card_dock_id: string;
}

export class ResFindCardDockPin {
  @IsNumber()
  statusCode: number;

  @IsString()
  message: string;

  @IsOptional()
  @IsString()
  error?: string;

  @IsOptional()
  @IsString()
  pin?: string;
}

export class ResponseListCard {
  @IsString()
  @IsUUID()
  id: string;

  @IsString()
  status: string;

  @IsString()
  type: string;

  @IsString()
  product_type: string;

  @IsString()
  masked_pan: string;
}

export class ResponseSearchCardPanDto {
  @IsString()
  id: string;

  @IsString()
  status: string;

  @IsString()
  type: string;

  @IsString()
  cardholder_name: string;
}

export class ParamsFindCardDockPan {
  @IsString()
  pan: string;
}
