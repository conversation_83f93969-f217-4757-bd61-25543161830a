import { Injectable, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import * as https from 'https';

/* ** DTOs ** */
import {
  ParamsAliasCoreDto,
  ResponseAssignmentCardDto,
  FindPanCardPayloadDto,
  ResponseSearchPanDto,
  ResAssignAliasCoreDto,
  PayloadAliasCoreDto,
  ResponseCreateVirtualCardDto,
  CreateDockCardVirtualDto,
  ParamsDockCardVirtualDto,
  ChangeCardStatusDto,
  ResCardStatusDto,
  ParamsCheckExpireDto,
  ResCheckExpireDto,
  PayloadCheckExpireDto,
  ResponseSearchVirtualCardDto,
  ReasignCardDto,
  virtualStringArrayDto,
} from './single-card-assignment.dto';

/* ** Utils ** */
import { EncryptData } from 'src/contexts/shared/utils/sensitive-data/encriptData.util';
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';

/* ** Enums ** */
import {
  CardType,
  ActiveFunction,
  CardStatus,
} from 'src/contexts/shared/interfaces/dtos/cards.enum.dto';
import { FormatCheckCardExpiration } from 'src/contexts/shared/enums/CardExpirationDateFormat.enum';
import { EncryptedMode } from 'src/contexts/shared/enums/encrypted.enum';

/* ** Repository ** */
import { UsersRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/users.repository.impl';
import { AccountRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/account.repository.impl';
import { AccountCardRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/account-card.repository.impl';

/* ** Errors ** */
import {
  ERROR_USER_NOT_FOUND,
  ERROR_CARD_NOT_FOUND,
  ERROR_ACCOUNT_NOT_FOUND,
  ERROR_ALIAS_CORE_PHYSICAL_NOT_CREATED,
  ERROR_ALIAS_CORE_VIRTUAL_NOT_CREATED,
  ERROR_CARD_ALREADY_ASSIGNED,
  ERROR_CARD_EXPIRED,
  ERROR_CHANGE_STATUS_NORMAL,
} from '../../infrastructure/errors/errors-card-assignment';
import { FindDockCardUseCase } from 'src/contexts/dock-cards/apps/find-dock-card/find-dock-card.usecase';
import { CreateLogCardsMovementsUseCase } from 'src/contexts/logs-cards-movements/application/create-log-cards-movements/create-log-cards-movements.usecase';

@Injectable()
export class SingleCardAssignmentUseCase {
  constructor(
    private readonly config: ConfigService,
    private readonly encrypt: EncryptData,
    private readonly authDock: authTokenDock,
    private readonly userRepo: UsersRepositoryImpl,
    private readonly accountRepo: AccountRepositoryImpl,
    private readonly cardAssignRepo: AccountCardRepositoryImpl,
    private readonly findDockCardUseCase: FindDockCardUseCase,
    private readonly createLogCardsMovementsUseCase: CreateLogCardsMovementsUseCase,
  ) {}

  async createCardAssignment(
    params: ParamsAliasCoreDto,
  ): Promise<ResponseAssignmentCardDto> {
    /* ** search email user ** */
    const person = await this.userRepo.getPersonIdByEmail(params.email);

    /* ** Validate if user exists ** */
    if (!person || !person?.person_dock_id) throw new ERROR_USER_NOT_FOUND();

    /* ** search pan in card ** */
    const card = await this.searchPan(params.card_number);

    /* ** Validate if card exists ** */
    if (!card?.id || card?.type !== CardType.PHYSICAL)
      throw new ERROR_CARD_NOT_FOUND();

    /* ** search register exists card ** */
    const exists_card = await this.cardAssignRepo.findCardByID(card.id);

    /* ** Validate if card exists ** */
    if (exists_card) throw new ERROR_CARD_ALREADY_ASSIGNED();

    /* ** search expiration date ** */
    const check_expire = await this.CheckCardExpirationDate({
      card_expiration: params.card_expiration,
      id: card.id,
    });

    /* ** Validate if expiration date ** */
    if (!check_expire?.status) throw new ERROR_CARD_EXPIRED();

    /* ** search account by person ** */
    const account_id = await this.accountRepo.getAccountIdByPersonId(person.id);

    /* ** Validate if account exists ** */
    if (!account_id?.id) throw new ERROR_ACCOUNT_NOT_FOUND();

    /* ** create alias core  - PHYSICAL CARD ** */
    const alias_physical = await this.createAliasCore({
      account_id: account_id.account_dock_id,
      alias_provider_id: this.config.get('DOCK_ALIAS_PROVIDER_ID'),
      alias: {
        card_id: card.id,
        card_rail: ActiveFunction.CREDIT,
      },
    });

    /* ** Validate if alias */
    if (!alias_physical?.id) throw new ERROR_ALIAS_CORE_PHYSICAL_NOT_CREATED();

    /* ** create Physical Card In DB ** */
    const promise_physical_db = this.cardAssignRepo.saveAccountCard({
      account_id: account_id.id,
      card_type: CardType.PHYSICAL,
      card_dock_id: card.id,
    });

    const promise_exist_virtual_card = this.verifyVirtualCard(account_id.id);

    const [physical_db, exist_virtual_card] = await Promise.all([
      promise_physical_db,
      promise_exist_virtual_card,
    ]);

    const { active_card, id_card_db, id_card_dock, card_status } =
      exist_virtual_card;

    /* ** create Virtual Card ** */
    const virtual_card = active_card
      ? await Promise.resolve({ id: id_card_dock })
      : await this.createVirtualCard({
          cardholder_name: card.cardholder_name,
          address: card.address,
        });

    /* ** Validate if virtual card */
    if (!virtual_card?.id) throw new ERROR_ALIAS_CORE_VIRTUAL_NOT_CREATED();

    /* ** create alias core - VIRTUAL CARD ** */
    const alias_virtual = active_card
      ? await Promise.resolve({ id: id_card_db })
      : await this.createAliasCore({
          account_id: account_id.account_dock_id,
          alias_provider_id: this.config.get('DOCK_ALIAS_PROVIDER_ID'),
          alias: {
            card_id: virtual_card.id,
            card_rail: ActiveFunction.CREDIT,
          },
        });

    /* ** Validate if alias */
    if (!alias_virtual?.id) throw new Error('Alias Core virtual not created');

    /* ** create Virtual Card In DB ** */
    const virtual_db = active_card
      ? await Promise.resolve({ id: id_card_db })
      : await this.cardAssignRepo.saveAccountCard({
          account_id: account_id.id,
          card_type: CardType.VIRTUAL,
          card_dock_id: virtual_card.id,
        });

    /* ** Change status card - PHYSICAL ** */
    const [status_physical, status_virtual] = await Promise.all([
      this.changeCardStatusNormal({
        card_id: card.id,
        status: CardStatus.NORMAL,
      }),
      active_card
        ? Promise.resolve({
            status: card_status ? CardStatus.NORMAL : CardStatus.BLOCKED,
          })
        : this.changeCardStatusNormal({
            card_id: virtual_card.id,
            status: CardStatus.NORMAL,
          }),
    ]);

    if (!status_physical?.status || !status_virtual?.status)
      throw new ERROR_CHANGE_STATUS_NORMAL();

    return {
      code: HttpStatus.OK,
      message:
        'Alias successfully created for both physical and virtual cards.',
      error: '',
      data: {
        account_dock_id: account_id.account_dock_id,
        person_dock_id: person.person_dock_id,
        card_physical: {
          physical_card_dock_id: card.id,
          physical_card_id: physical_db.id,
          physical_status: status_physical.status,
        },
        card_virtual: {
          virtual_card_dock_id: virtual_card.id,
          virtual_card_id: virtual_db.id,
          virtual_status: status_virtual.status,
        },
      },
    };
  }

  private async verifyVirtualCard(
    account_id: string,
  ): Promise<ResponseSearchVirtualCardDto> {
    let active_card: boolean = false;

    const account =
      await this.cardAssignRepo.findCardVirtualByAccountId(account_id);

    /* ** Validate if account exists ** */
    if (account && account.length === 0) {
      return {
        active_card,
        id_card_db: null,
        id_card_dock: null,
        card_status: null,
      };
    }

    const virtualCards = account.map((card) => card.card_dock_id);

    const check_state_canceled = await this.checkCancelCard({
      cards: virtualCards,
    });

    if (check_state_canceled) active_card = true;
    else active_card = false;

    return {
      active_card,
      id_card_db: active_card ? account[0].id : null,
      id_card_dock: active_card ? account[0].card_dock_id : null,
      card_status: active_card ? account[0].enabled : null,
    };
  }

  private async searchPan(pan: string): Promise<ResponseSearchPanDto> {
    /* ** Encrypt data ** */
    const encrypt = await this.encrypt.encryptData({
      encrypted_data: pan,
    });

    const { bearer_token, certificate, key } =
      await this.authDock.getAuthDock();

    const payload: FindPanCardPayloadDto = {
      pan: encrypt.encrypted_data,
      iv: encrypt.iv,
      aes: encrypt.aes,
      mode: encrypt.mode,
    };

    const httpsAgent = new https.Agent({
      cert: certificate,
      key,
      rejectUnauthorized: false,
    });

    const executeSearch = async (): Promise<ResponseSearchPanDto> => {
      try {
        const data = await axios.post(
          `${this.config.get('DOCK_URL_GLOBAL')}/cards/v1/cards/pan`,
          payload,
          {
            headers: {
              Authorization: `Bearer ${bearer_token}`,
            },
            httpsAgent,
          },
        );

        return data.data;
      } catch (error) {
        const code = error.response.status;
        if (code === 400 || code === 401 || code === 403 || code === 500) {
          console.error('Error In Search Pan', error.message);
        }

        return null;
      }
    };

    const response = await executeSearch();
    return response;
  }

  private async createAliasCore(
    body: PayloadAliasCoreDto,
  ): Promise<ResAssignAliasCoreDto> {
    const { bearer_token, certificate, key } =
      await this.authDock.getAuthDock();

    const httpsAgent = new https.Agent({
      cert: certificate,
      key,
      rejectUnauthorized: false,
    });

    const executeAliasCore = async (): Promise<ResAssignAliasCoreDto> => {
      try {
        const data = await axios.post(
          `${this.config.get('DOCK_URL_GLOBAL')}/account-services/alias-core/v1/alias/ecosystems/MASTERCARD/schemas/DOCK_CARDS`,
          body,
          {
            headers: {
              Authorization: `Bearer ${bearer_token}`,
            },
            httpsAgent,
          },
        );

        return data.data;
      } catch (error) {
        const code = error.response.status;
        const array_code = [400, 404, 405, 409, 415, 422, 500];
        if (array_code.includes(code)) {
          console.error('Error In Alias Core', error.message);
        }

        return null;
      }
    };

    const response = await executeAliasCore();

    return response;
  }

  private async createVirtualCard(
    params: ParamsDockCardVirtualDto,
  ): Promise<ResponseCreateVirtualCardDto> {
    const { bearer_token, certificate, key } =
      await this.authDock.getAuthDock();

    /* ** Agent ** */
    const httpsAgent = new https.Agent({
      cert: certificate,
      key,
      rejectUnauthorized: false,
    });

    const bodyCard: CreateDockCardVirtualDto = {
      cardholder_name: params.cardholder_name,
      profile_id: this.config.get('DOCK_PROFILE_ID'),
      type: CardType.VIRTUAL,
      active_function: ActiveFunction.CREDIT,
      expiration_date: '2027-01-01T00:00:01Z',
      pin: '1234',
      settings: {
        security: {
          pin_offline: false,
        },
        transaction: {
          ecommerce: true,
          international: true,
          stripe: false,
          wallet: true,
          withdrawal: true,
          contactless: true,
        },
      },
      address: params.address,
    };

    const executeCreateVirtualCard =
      async (): Promise<ResponseCreateVirtualCardDto> => {
        try {
          const card = await axios.post(
            `${this.config.get('DOCK_URL_GLOBAL')}/cards/v1/cards`,
            bodyCard,
            {
              headers: {
                Authorization: `Bearer ${bearer_token}`,
              },
              httpsAgent,
            },
          );

          return card.data;
        } catch (error) {
          const code = error.response.status;
          const array_code = [400, 401, 403, 404, 422, 500];
          if (array_code.includes(code)) {
            console.error('Error In Create Virtual Card', error.message);
          }

          return null;
        }
      };

    return await executeCreateVirtualCard();
  }

  private async changeCardStatusNormal(
    body: ChangeCardStatusDto,
  ): Promise<ResCardStatusDto> {
    const { bearer_token, certificate, key } =
      await this.authDock.getAuthDock();

    const httpsAgent = new https.Agent({
      cert: certificate,
      key,
      rejectUnauthorized: false,
    });

    const executeChangeStatus = async (): Promise<ResCardStatusDto> => {
      try {
        const data = await axios.put(
          `${this.config.get('DOCK_URL_GLOBAL')}/cards/v1/cards/${body.card_id}/status`,
          { status: body.status, status_reason: body?.reason },
          {
            headers: {
              Authorization: `Bearer ${bearer_token}`,
            },
            httpsAgent,
          },
        );

        return data.data;
      } catch (error) {
        const code = error.response.status;
        const array_code = [400, 401, 403, 404, 422, 500];
        if (array_code.includes(code)) {
          console.error('Error In Change Card Status Normal', error.message);
        }

        return null;
      }
    };

    return await executeChangeStatus();
  }

  private async CheckCardExpirationDate(
    card: ParamsCheckExpireDto,
  ): Promise<ResCheckExpireDto> {
    const { bearer_token, certificate, key } =
      await this.authDock.getAuthDock();

    const httpsAgent = new https.Agent({
      cert: certificate,
      key,
      rejectUnauthorized: false,
    });

    const { aes, encrypted_data, iv } = await this.encrypt.encryptData({
      encrypted_data: card.card_expiration,
    });

    const payload: PayloadCheckExpireDto = {
      expiration_date: encrypted_data,
      format: FormatCheckCardExpiration.MMYY,
      aes,
      iv,
      mode: EncryptedMode.GCM,
    };

    const executeCheckExpiration = async (): Promise<ResCheckExpireDto> => {
      try {
        const data = await axios.post(
          `${this.config.get('DOCK_URL_GLOBAL')}/cards/v1/cards/${card.id}/check-expiration-date`,
          payload,
          {
            headers: {
              Authorization: `Bearer ${bearer_token}`,
            },
            httpsAgent,
          },
        );

        return { status: data.status === 200 && true };
      } catch (error) {
        const code = error.response.status;
        const array_code = [400, 401, 403, 404, 422, 500];
        if (array_code.includes(code)) {
          console.error('Error In Check Card Expiration Date', error.message);
        }

        return null;
      }
    };

    return await executeCheckExpiration();
  }

  async reassignCard(dto: ReasignCardDto) {
    const card = await this.searchPan(dto.pan);

    if (!card?.id || card?.type !== CardType.PHYSICAL) {
      throw new ERROR_CARD_NOT_FOUND();
    }

    const sentitveData = await this.findDockCardUseCase.sensitiveData({
      card_dock_id: card.id,
    });

    const resp = await this.createCardAssignment({
      card_expiration: sentitveData.format_expiration_date.replace('/', ''),
      card_number: dto.pan,
      email: dto.email,
    });

    const cancelStatus = await this.changeCardStatusNormal({
      card_id: dto.oldCardId,
      status: CardStatus.CANCELED,
      reason: 'OWNER_REQUEST',
    });

    if (!cancelStatus?.status) {
      throw new ERROR_CHANGE_STATUS_NORMAL();
    }

    await this.changeDBCardStatus(dto.oldCardId);

    // Log the card reassignment with reason
    await this.createLogCardsMovementsUseCase.execute({
      old_card: dto.oldCardId,
      new_card: card.id,
      reason: dto.reason,
    });

    return resp;
  }

  private async changeDBCardStatus(card_dock_id: string): Promise<boolean> {
    const response = await this.cardAssignRepo.updateCardStatus({
      card_dock_id,
      status: false,
    });

    return response;
  }

  private async checkCancelCard(
    array_cards: virtualStringArrayDto,
  ): Promise<boolean> {
    const specificCard = async (card_id: string): Promise<string> => {
      try {
        const { bearer_token, certificate, key } =
          await this.authDock.getAuthDock();

        const httpsAgent = new https.Agent({
          cert: certificate,
          key,
          rejectUnauthorized: false,
        });
        const response = await axios.get(
          `${this.config.get('DOCK_URL_GLOBAL')}/cards/v1/cards/${card_id}`,
          {
            headers: {
              Authorization: `Bearer ${bearer_token}`,
            },
            httpsAgent,
          },
        );

        return response?.data?.status;
      } catch (error) {
        console.error('Error checking cancel card:', error.message);
        return CardStatus.CANCELED;
      }
    };
    const promises_card = array_cards.cards.map((card) => specificCard(card));

    const cards = await Promise.all(promises_card);

    const filteredCards = cards.filter((card) => card !== CardStatus.CANCELED);

    return filteredCards.length > 0;
  }
}
