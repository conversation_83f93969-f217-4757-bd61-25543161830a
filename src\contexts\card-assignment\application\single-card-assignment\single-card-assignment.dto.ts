import {
  Is<PERSON><PERSON>y,
  IsString,
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsInt,
  Matches,
  Length,
  ValidateNested,
  IsEmail,
} from 'class-validator';
import { Type } from 'class-transformer';

import {
  CardType,
  ActiveFunction,
  CardStatus,
} from 'src/contexts/shared/interfaces/dtos/cards.enum.dto';

import { EncryptedMode } from 'src/contexts/shared/enums/encrypted.enum';

import { FormatCheckCardExpiration } from 'src/contexts/shared/enums/CardExpirationDateFormat.enum';

class TransactionSettings {
  @IsBoolean()
  ecommerce: boolean;

  @IsBoolean()
  international: boolean;

  @IsBoolean()
  stripe: boolean;

  @IsBoolean()
  wallet: boolean;

  @IsBoolean()
  withdrawal: boolean;

  @IsBoolean()
  contactless: boolean;
}

class SecuritySettings {
  @IsBoolean()
  pin_offline: boolean;
}

class Settings {
  @ValidateNested()
  @Type(() => TransactionSettings)
  transaction: TransactionSettings;

  @ValidateNested()
  @Type(() => SecuritySettings)
  security: SecuritySettings;
}

class Address {
  @IsString()
  @IsOptional()
  street: string;

  @IsString()
  @IsOptional()
  number: string;

  @IsString()
  @IsOptional()
  city: string;

  @IsString()
  @IsOptional()
  administrative_area_code: string;

  @IsString()
  @IsOptional()
  country_code: string;

  @IsString()
  @IsOptional()
  postal_code: string;

  @IsString()
  @IsOptional()
  neighborhood: string;

  @IsString()
  complement: string;
}

class Alias {
  @IsString()
  card_id: string;

  @IsEnum(ActiveFunction)
  card_rail: ActiveFunction;
}

class DataPhysical {
  @IsNotEmpty()
  @IsString()
  physical_card_dock_id: string;

  @IsNotEmpty()
  @IsString()
  physical_card_id: string;

  @IsNotEmpty()
  @IsString()
  physical_status: string;
}

class DataVirtual {
  @IsNotEmpty()
  @IsString()
  virtual_card_dock_id: string;

  @IsNotEmpty()
  @IsString()
  virtual_card_id: string;

  @IsNotEmpty()
  @IsString()
  virtual_status: string;
}

class DataAssignmentCardDto {
  @IsNotEmpty()
  @IsString()
  person_dock_id: string;

  @IsNotEmpty()
  @IsString()
  account_dock_id: string;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => DataPhysical)
  card_physical?: DataPhysical;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => DataVirtual)
  card_virtual?: DataVirtual;
}

export class ParamsAliasCoreDto {
  @IsNotEmpty()
  @IsString()
  @IsEmail()
  email: string;

  @IsNotEmpty()
  @IsString()
  @Length(16, 16)
  card_number: string;

  @IsNotEmpty()
  @IsString()
  @Matches(/^(0[1-9]|1[0-2])\d{2}$/, {
    message: 'card_expiration must be in the format MMYY',
  })
  card_expiration: string;
}

export class CreateAccountCardDto {
  @IsNotEmpty()
  @IsEnum(CardType)
  card_type: CardType;

  @IsNotEmpty()
  @IsString()
  card_dock_id: string;

  @IsNotEmpty()
  @IsString()
  account_id: string;
}

export class ResponseCreateAccountCardDto {
  @IsNotEmpty()
  @IsString()
  id: string;
}

export class ResponseAssignmentCardDto {
  @IsNotEmpty()
  @IsInt()
  code: number;

  @IsNotEmpty()
  @IsString()
  message: string;

  @IsNotEmpty()
  @IsString()
  error: string;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => DataAssignmentCardDto)
  data?: DataAssignmentCardDto;
}

export class FindPanCardPayloadDto {
  @IsString()
  pan: string;

  @IsString()
  iv: string;

  @IsString()
  aes: string;

  @IsString()
  mode: string;
}

export class ResponseSearchPanDto {
  @IsString()
  id: string;

  @IsString()
  status: string;

  @IsString()
  type: string;

  @IsString()
  cardholder_name: string;

  @ValidateNested()
  @IsOptional()
  @Type(() => Address)
  address: Address;
}

export class ResAssignAliasCoreDto {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsNotEmpty()
  @IsString()
  status: string;
}

export class PayloadAliasCoreDto {
  @IsString()
  account_id: string;

  @IsString()
  alias_provider_id: string;

  @ValidateNested({ each: true })
  @Type(() => Alias)
  alias: Alias;
}

export class CreateDockCardVirtualDto {
  @IsString()
  @IsOptional()
  profile_id: string;

  @IsEnum(CardType)
  @IsNotEmpty()
  type: CardType;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  cardholder_name: string;

  @IsEnum(ActiveFunction)
  @IsNotEmpty()
  active_function: ActiveFunction;

  @IsDateString()
  @IsNotEmpty()
  @IsOptional()
  expiration_date: string;

  @IsString()
  @IsNotEmpty()
  pin: string;

  @ValidateNested()
  @IsOptional()
  @Type(() => Settings)
  settings: Settings;

  @ValidateNested()
  @IsOptional()
  @Type(() => Address)
  address: Address;
}

export class ParamsDockCardVirtualDto {
  @IsString()
  cardholder_name: string;

  @ValidateNested()
  @IsOptional()
  @Type(() => Address)
  address: Address;
}

export class ResponseCreateVirtualCardDto {
  @IsString()
  id: string;

  @IsString()
  status: string;
}

export class ResPersonDto {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsNotEmpty()
  @IsString()
  person_dock_id: string;
}

export class ResAccountDto {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsNotEmpty()
  @IsString()
  account_dock_id: string;
}

export class ChangeCardStatusDto {
  @IsNotEmpty()
  @IsString()
  card_id: string;

  @IsNotEmpty()
  @IsEnum(CardStatus)
  status: CardStatus;

  @IsOptional()
  @IsString()
  reason?: string;
}

export class ResCardStatusDto {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsNotEmpty()
  @IsString()
  status: string;

  @IsNotEmpty()
  @IsString()
  status_reason: string;
}

export class ParamsCheckExpireDto {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsNotEmpty()
  @IsString()
  @Matches(/^(0[1-9]|1[0-2])\/\d{2}$/, {
    message: 'card_expiration must be in the format MM/YY',
  })
  card_expiration: string;
}

export class PayloadCheckExpireDto {
  @IsNotEmpty()
  @IsString()
  expiration_date: string;

  @IsNotEmpty()
  @IsEnum(FormatCheckCardExpiration)
  format: FormatCheckCardExpiration;

  @IsNotEmpty()
  @IsString()
  aes: string;

  @IsNotEmpty()
  @IsString()
  iv: string;

  @IsNotEmpty()
  @IsEnum(EncryptedMode)
  mode: EncryptedMode;
}

export class ResCheckExpireDto {
  @IsNotEmpty()
  @IsString()
  status: boolean;
}

export class ResponseSearchVirtualCardDto {
  @IsOptional()
  @IsString()
  id_card_dock?: string;

  @IsOptional()
  @IsString()
  id_card_db?: string;

  @IsBoolean()
  @IsBoolean()
  card_status?: boolean;

  @IsBoolean()
  active_card?: boolean;
}

export class ReasignCardDto {
  @IsEmail()
  email: string;

  @IsString()
  oldCardId: string;

  @IsString()
  pan: string;

  @IsString()
  @IsNotEmpty()
  @Length(1, 500)
  reason: string;
}

export class virtualStringArrayDto {
  @IsArray()
  @IsString({ each: true })
  cards: string[];
}
