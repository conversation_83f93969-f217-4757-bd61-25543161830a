import { ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsEmail, IsNumber, IsString, IsUUID } from "class-validator";


export class UpdateUserAccountDto {
    
    @ApiProperty()
    @IsString()
    name: string;

    @ApiProperty()
    @IsNumber()
    phone: number;

    @ApiProperty()
    @IsEmail()
    email: string;

    @ApiProperty()
    @IsString()
    @IsUUID()
    adminId: string;
}

export class UpdateSpeiDto {
    @ApiProperty()
    @IsBoolean()
    speiIn: boolean;
    @ApiProperty()
    @IsBoolean()
    speiOut: boolean;
}