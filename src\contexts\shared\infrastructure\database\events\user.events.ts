import { Users } from "src/contexts/users/domain/entities/users.entity"
import { EntitySubscriberInterface, EventSubscriber, InsertEvent, UpdateEvent } from "typeorm"

@EventSubscriber()
export class EmailSubscriber implements EntitySubscriberInterface<Users> {
    /**
     * Indicates that this subscriber only listen to Post events.
     */
    listenTo() {
        return Users
    }

    /**
     * Called before post insertion.
     */
    beforeInsert(event: InsertEvent<Users>) {
        event.entity.email = event.entity.email.toLowerCase();
    }

    beforeUpdate(event: UpdateEvent<Users>) {
        if (event.entity && event.entity.email) {
            event.entity.email = event.entity.email.toLowerCase();
        }
    }
}