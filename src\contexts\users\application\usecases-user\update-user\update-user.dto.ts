import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEmail, IsOptional, IsBoolean, IsUUID, IsNumber, MinLength, Matches , ValidateIf} from 'class-validator';

export class UpdateUserDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsNumber()
  phone?: number;

  @ApiProperty()
  @ValidateIf((o) => o.password !== null)
  @IsString()
  @MinLength(8)
  password: string | null;

  @IsOptional()
  created_by?: string;

  @IsOptional()
  @IsBoolean()
  enabled?: boolean;

  @IsNumber()
  @IsOptional()
  membership_number?: number;

  @IsOptional()
  @IsUUID()
  personIDDock?: string;

  @IsOptional()
  @IsUUID()
  admin_data?: string;

  @IsOptional()
  @IsUUID()
  address?: string;

  @IsOptional()
  @IsUUID()
  rol?: string;
}

export class UpdateConveniUserDto {
  @ApiProperty()
  @IsString()
  name: string;

  @IsEmail()
  email: string;

  @ApiProperty()
  @IsNumber()
  phone: number;

  @ApiProperty()
  @ValidateIf((o) => o.password !== null)
  @IsString()
  @MinLength(8)
  password: string | null;

  @ApiProperty()
  @IsUUID()
  rol: string;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  admin?: string | null;
}