import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as https from 'https';
import axios from 'axios';

/* ** DTOs ** */
import {
  ParamsTransferOrderOutDto,
  ResTransferOrderOutDto,
} from './transfer-orders-out.dto';

/* ** Repositories ** */
import { UserTransferRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/user-transfer.repository.impl';
import { UsersRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/users.repository.impl';
import { TransferOrdersRepositoryImpl } from '../../infrastructure/repository/transfer-orders.repository.impl';
import { CommissionRepositoryImpl } from 'src/contexts/commission/infrastructure/repositories/commission.repository.impl';

/* ** Utils ** */
import { authTokenDock } from 'src/contexts/shared/utils/authDock/authDock';
import { CommissionsUtils } from 'src/contexts/shared/utils/commissions/commissions.utils';

/* ** Enums ** */
import { TypeCommissionEnum } from 'src/contexts/shared/enums/commission.enum';

@Injectable()
export class TransferOut {
  constructor(
    private readonly transfer: TransferOrdersRepositoryImpl,
    private readonly userTransfer: UserTransferRepositoryImpl,
    private readonly userAdmin: UsersRepositoryImpl,
    private readonly auth: authTokenDock,
    private readonly configService: ConfigService,
    private readonly commissions: CommissionsUtils,
    private readonly commissionImpl: CommissionRepositoryImpl,
  ) {}

  async webhookTransferOut(
    body: ParamsTransferOrderOutDto,
  ): Promise<ResTransferOrderOutDto> {
    /* ** Check status transfer ** */
    this.checkStatusTransfer(body);

    /* ** Save transfer out ** */
    await this.transfer.save({
      type: 'transfer-out',
      data: JSON.stringify(body),
    });

    return {
      statusCode: 200,
      message: 'Success',
    };
  }

  private async checkStatusTransfer(
    body: ParamsTransferOrderOutDto,
  ): Promise<boolean> {
    /* ** Timeout para esperar la asignación de la orden ** */
    await this.sleep(2000);

    const order_id = body.data?.id ?? null;
    const status = body.data?.status ?? null;

    const user_order = await this.userTransfer.findByOrderID(order_id);

    if (!user_order) return false;

    const user_admin = await this.userAdmin.findUserWithAdminSettingsByEmail(
      user_order.email,
    );

    if (status === 'canceled' || status === 'returned') {
      await this.userTransfer.changeStatusOrderTransfer({
        id: user_order.id,
        status,
      });

      await this.canceledTransferOrder(
        user_order.player_account_id,
        user_order.amount,
      );
    } else if (status === 'scattered') {
      /* ** CEP de la orden ** */
      const cep = await this.getCep(user_order.order_id);

      let commissionAmount = 0;

      const concentrator_account = this.configService.get<string>(
        'DOCK_OPERATING_ACCOUNT_ID',
      );

      const account_commission = this.configService.get<string>(
        'DOCK_COMMISSIONS_ACCOUNT_ID',
      );

      if (
        user_order.player_account_id !== concentrator_account &&
        user_order.player_account_id !== account_commission
      ) {
        /* ** Se cobra la comisión por SPEI_OUT ** */
        commissionAmount = user_admin.spei_out ?? 0;

        const comm = await this.commissionImpl.save({
          status: status.toUpperCase(),
          type_commission: TypeCommissionEnum.OUT,
          player_account: user_order?.player_account ?? 'N/A',
          embajador_account: user_admin?.ambassador ?? 'N/A',
          amount: Number(user_order.amount).toFixed(2),
        });

        this.commissions.payCommissions({
          debtor_key: user_order.player_account_id,
          creditor_key: account_commission,
          commission: commissionAmount,
          type: 'SPEI_OUT',
          external_transaction_id: comm.id,
        });
      }

      this.userTransfer.changeCommissionsSpeiOut({
        id: user_order.id,
        commission: commissionAmount.toFixed(2),
        status: status,
        cep: cep ? cep : null,
      });
    } else {
      await this.userTransfer.changeStatusOrderTransfer({
        id: user_order.id,
        status,
      });
    }

    return true;
  }

  private async canceledTransferOrder(
    creditorId: string,
    amount: number,
  ): Promise<boolean> {
    try {
      const auth = await this.auth.getAuthDock();
      const httpsAgent = new https.Agent({
        cert: auth.certificate,
        key: auth.key,
        rejectUnauthorized: false,
      });
      const transferObject = {
        debtor: {
          key: this.configService.get('DOCK_OPERATING_ACCOUNT_ID'),
          key_type: 'ACCOUNT_ID',
          balance_category: 'GENERAL',
        },
        creditor: {
          key: creditorId,
          key_type: 'ACCOUNT_ID',
          balance_category: 'GENERAL',
        },
        amount: Number(amount),
        operation_type: 'P2P - P2P_OUT',
        description: 'Cancelación de transferencia',
      };
      /* ** Realizamos la transferencia ** */
      const response = await axios.post(
        `${this.configService.get('DOCK_URL_GLOBAL')}/dockpay/v1/transfers`,
        transferObject,
        {
          headers: {
            Authorization: `Bearer ${auth.bearer_token}`,
            'Content-Type': 'application/json',
          },
          httpsAgent,
        },
      );

      return response.status === 200 || (response.status === 201 && true);
    } catch (error) {
      console.error('Error al cancelar la transferencia:', error.message);
      return false;
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  private async getCep(id: string): Promise<string> {
    try {
      const url = `${this.configService.get('TRANSFER_URL')}/api/1.0/orders/${id}/cep`;
      const response = await axios.get(url, {
        headers: {
          'X-Custom-Auth': this.configService.get<string>('TRANSFER_TOKEN'),
          Accept: 'application/json',
        },
      });
      return response?.data?.data ? response.data.data : null;
    } catch (error) {
      console.error('Error al obtener el CEP:', error.message);
      return null;
    }
  }
}
