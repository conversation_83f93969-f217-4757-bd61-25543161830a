import { Injectable } from '@nestjs/common';

/* ** DTO ** */
import { ResponseCreateConveniaAccountDto } from './create-convenia-account.dto';

/* ** Repositories ** */
import { UsersRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/users.repository.impl';

/* ** Utils ** */
import { ConveniaAccount } from 'src/contexts/shared/utils/convenia-account/convenia-account.utils';

@Injectable()
export class CreateConveniaAccountUseCase {
  constructor(
    private readonly user: UsersRepositoryImpl,
    private readonly convenia: ConveniaAccount,
  ) {}

  async execute(): Promise<ResponseCreateConveniaAccountDto[]> {
    const response: ResponseCreateConveniaAccountDto[] = [];

    const users = await this.user.findAllUsers();

    if (!users || users.length === 0) {
      return [];
    }

    for (const user of users) {
      if (user.convenia_account === null) {
        const { convenia_account } =
          await this.convenia.createConveniaAccount();

        if (convenia_account) {
          await this.user.updateConveniaAccount(user.id, convenia_account);
          const update_data = {
            id: user.id,
            email: user.email,
            convenia_account,
          };

          response.push(update_data);
        }
      }
    }

    return response;
  }
}
