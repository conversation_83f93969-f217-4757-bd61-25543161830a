import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsNotEmpty, IsString, ValidateNested } from "class-validator";
import { Users } from "src/contexts/users/domain/entities/users.entity";


export class SiginVO {
    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    token: string;

    @ApiProperty()
    @ValidateNested()
    @Type(() => Users)
    user: Users;
}
