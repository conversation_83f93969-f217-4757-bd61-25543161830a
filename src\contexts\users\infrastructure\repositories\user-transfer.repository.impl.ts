import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

/* ** DTOs ** */
import {
  createPaymentDto,
  ResponseCreatePaymentDto,
  ResponseFindPaymentDto,
  ResponseFindPaymentStateDto,
} from 'src/contexts/transfers/application/create-transaction/dockpay-transfer.dto';
import {
  ParamsUpdateChangeStatusDto,
  ParamsUpdateSignDto,
  ParamsUpdateOrderDto,
  ParamsUpdateCepDto,
  ParamsUpdateDataTransferDto,
  ParamsCommissionDto,
} from 'src/contexts/notifications/application/notification-webhook/notification-webhook.dto';

/* ** Enum ** */

/* ** Entities ** */
import { UserTransfer } from '../../domain/entities/user_transfers.entity';

/* ** Repositories ** */
import { UserTransferRepository } from '../../domain/repository/user-transfer.repository';

@Injectable()
export class UserTransferRepositoryImpl implements UserTransferRepository {
  constructor(
    @InjectRepository(UserTransfer)
    private readonly userPayment: Repository<UserTransfer>,
  ) {}

  async save(entity: createPaymentDto): Promise<ResponseCreatePaymentDto> {
    return await this.userPayment.save(entity);
  }

  async findByIDPayment(payment_id: string): Promise<ResponseFindPaymentDto> {
    const transfer = await this.userPayment.findOneBy({
      id: payment_id,
    });
    return transfer ? transfer : null;
  }

  async findPaymentState(id: string): Promise<ResponseFindPaymentStateDto> {
    const transfer = await this.userPayment.findOneBy({ id });
    return transfer ? transfer : null;
  }

  async changeStatusPaymentDock(
    body: ParamsUpdateChangeStatusDto,
  ): Promise<boolean> {
    await this.userPayment.update(body.id, { status_dock: body.status });
    return true;
  }

  async changeStatusOrderTransfer(
    body: ParamsUpdateChangeStatusDto,
  ): Promise<boolean> {
    await this.userPayment.update(body.id, { status_transfer: body.status });
    return true;
  }

  async changeCepOrderTransfer(body: ParamsUpdateCepDto): Promise<boolean> {
    await this.userPayment.update(body.id, { cep: body.cep });
    return true;
  }

  async updateOrderID(body: ParamsUpdateOrderDto): Promise<boolean> {
    await this.userPayment.update(body.id, { order_id: body.order_id });
    return true;
  }

  async findByOrderID(order_id: string): Promise<ResponseFindPaymentDto> {
    const transfer = await this.userPayment.findOneBy({
      order_id: order_id,
    });
    return transfer ? transfer : null;
  }

  async findById(id: string): Promise<UserTransfer> {
    const qb = this.userPayment.createQueryBuilder('user_transfer');
    qb.where('user_transfer.id = :id', { id });

    return await qb.getOne();
  }

  async changeDataPaymentDock(
    body: ParamsUpdateDataTransferDto,
  ): Promise<boolean> {
    await this.userPayment.update(body.id, {
      commission: String(body.commission),
      reference_dock_id: body.reference_dock_id,
      status_dock: body.status,
      status_transfer: body.status_transfer,
    });
    return true;
  }

  async changeCommissionsSpeiOut(body: ParamsCommissionDto): Promise<boolean> {
    await this.userPayment.update(body.id, {
      commission: String(body.commission),
      cep: body.cep,
      status_transfer: body.status,
    });
    return true;
  }

  async updateOrderSign(body: ParamsUpdateSignDto): Promise<boolean> {
    await this.userPayment.update(body.id, {
      sign_transfer: body.sign_transfer,
      error_transfer: body.error_transfer,
    });
    return true;
  }
}
