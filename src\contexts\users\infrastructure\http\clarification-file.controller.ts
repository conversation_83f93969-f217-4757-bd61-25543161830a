import {
  Controller,
  Post,
  Param,
  UploadedFiles,
  UseInterceptors,
  UseGuards,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from 'src/contexts/auth/guards/auth.guard';
import { UploadClarificationFileUseCase } from '../../application/usecases-clarification-files/upload-clarification-file/upload-clarification-file.usecase';

@UseGuards(JwtAuthGuard)
@Controller('clarification/:id/files')
export class ClarificationFileController {
  constructor(
    private readonly uploadClarificationFile: UploadClarificationFileUseCase,
  ) {}

  @Post()
  @UseInterceptors(FilesInterceptor('files', 5)) // 👈 máximo 5 archivos
  async uploadMultiple(
    @Param('id') id: string,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    const savedFiles = await this.uploadClarificationFile.executeMultiple({
      trackingNumber: id,
      files,
    });
    return { files: savedFiles };
  }
}
