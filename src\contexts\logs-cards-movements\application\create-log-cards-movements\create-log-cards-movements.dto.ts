import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, MaxLength, IsOptional, IsDateString } from 'class-validator';

export class CreateLogCardsMovementsDto {
  @ApiProperty({
    description: 'Tarjeta anterior/eliminada',
    example: 'CARD_12345',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  old_card: string;

  @ApiProperty({
    description: 'Tarjeta nueva',
    example: 'CARD_67890',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  new_card: string;

  @ApiProperty({
    description: 'Motivo del cambio de tarjeta',
    example: 'Tarjeta dañada',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(500)
  reason: string;

  @ApiProperty({
    description: 'Fecha y hora cuando se realizó el cambio',
    example: '2024-01-15T10:30:00Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  created_at?: Date;
}