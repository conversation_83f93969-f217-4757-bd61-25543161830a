import { ApiResponseDto } from "src/contexts/shared/interfaces/dtos/api-response.dto";
import { Users } from "../entities/users.entity";
import { CreateTransferContactDto } from "../../application/usecases-transfers/create-transfer-contact/create-transfer-contact.dto";
import { UpdateTransferContactDto } from "../../application/usecases-transfers/update-transfer-contact/update-transfer-contact.dto";

export interface TransferContactRepository {
  save(entity: CreateTransferContactDto): Promise<ApiResponseDto>;
  findAll(): Promise<ApiResponseDto>;
  findById(id: string): Promise<ApiResponseDto>;
  update(id: string, entity: UpdateTransferContactDto): Promise<ApiResponseDto>;
  remove(id: string): Promise<ApiResponseDto>;
  findByUser(idUser: string): Promise<ApiResponseDto>;
}