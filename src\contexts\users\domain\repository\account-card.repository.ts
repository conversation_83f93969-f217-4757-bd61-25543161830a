import {
  CreateAccountCardDto,
  ResponseCreateAccountCardDto,
} from '../../../card-assignment/application/single-card-assignment/single-card-assignment.dto';

import { ParamsDBChangeCardStatus } from 'src/contexts/dock-cards/apps/control-dock-card/control-dock-card-dto';

import { AccountCards } from '../entities/account-cards.entity';

export interface AccountCardRepository {
  saveAccountCard(
    entity: CreateAccountCardDto,
  ): Promise<ResponseCreateAccountCardDto>;
  findCardByID(card_id: string): Promise<boolean>;
  findCardType(card_dock_id: string): Promise<string>;
  updateCardStatus(card: ParamsDBChangeCardStatus): Promise<boolean>;
  findCardVirtualByAccountId(card_dock_id: string): Promise<AccountCards[]>;
}
