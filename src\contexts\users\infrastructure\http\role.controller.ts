import {
  Controller,
  Get,
  Query,
  UseGuards
} from '@nestjs/common';
import { ApiResponse } from '@nestjs/swagger';
import { ReadRolesFilterDtoo } from '../../application/usecases-roles/read-roles/read-roles.dto';
import { ReadRolesUseCase } from '../../application/usecases-roles/read-roles/read-roles.usecase';
import { Rol } from '../../domain/entities/rol.entity';
import { JwtAuthGuard } from 'src/contexts/auth/guards/auth.guard';

@UseGuards(JwtAuthGuard)
@Controller('role')
export class RoleController {
  constructor(
    private readonly readRolesUseCase: ReadRolesUseCase
  ) {}
  @Get()
  @ApiResponse({
    status: 200,
    description: 'Roles retrieved successfully',
    type: Rol
  })
  getRoles(@Query() filter: ReadRolesFilterDtoo){
     return this.readRolesUseCase.execute(filter);
  }

}
