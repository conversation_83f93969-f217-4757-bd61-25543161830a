import { Body, Controller, Post, HttpStatus, UseGuards } from '@nestjs/common';

/* ** DTOs ** */
import {
  ParamsAliasCoreDto,
  ReasignCardDto,
  ResponseAssignmentCardDto,
} from '../../application/single-card-assignment/single-card-assignment.dto';

/* ** Use Cases ** */
import { SingleCardAssignmentUseCase } from '../../application/single-card-assignment/single-card-assignment.usecase';
import { JwtAuthGuard } from 'src/contexts/auth/guards/auth.guard';
import { ApiResponse } from '@nestjs/swagger';

@UseGuards(JwtAuthGuard)
@Controller('card-assignment')
export class CardAssignmentController {
  constructor(private readonly singleAssig: SingleCardAssignmentUseCase) {}

  @ApiResponse({
    status: 200,
    description: 'Card assigned successfully',
    type: ResponseAssignmentCardDto,
  })
  @Post('single')
  async createSingleCardAssignment(
    @Body() params: ParamsAliasCoreDto,
  ): Promise<ResponseAssignmentCardDto> {
    try {
      return await this.singleAssig.createCardAssignment(params);
    } catch (e) {
      return {
        code: HttpStatus.NOT_FOUND,
        message: e.message,
        error: e.name,
      };
    }
  }

  @ApiResponse({
    status: 200,
    description: 'Card reasigned successfully',
    type: ResponseAssignmentCardDto,
  })
  @Post('reassign')
  async reassignCard(@Body() dto: ReasignCardDto) {
    return this.singleAssig.reassignCard(dto);
  }
}
