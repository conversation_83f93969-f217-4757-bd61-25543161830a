import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { City } from "../../domain/entities/city.entity";

export class CityRepositoryImpl  {

    constructor(
        @InjectRepository(City)
        private readonly repository: Repository<City>,
    ) { }

    findByState(stateId: number){
        return this.repository.find({
            where : {
                state: {
                    id: stateId
                }
            }
        });
    }

}