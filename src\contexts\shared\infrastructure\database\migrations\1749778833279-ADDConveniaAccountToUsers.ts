import { MigrationInterface, QueryRunner } from 'typeorm';

export class ADDConveniaAccountToUsers1749778833279
  implements MigrationInterface
{
  name = 'ADDConveniaAccountToUsers1749778833279';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "users" ADD "convenia_account" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "users" DROP COLUMN "convenia_account"`,
    );
  }
}
