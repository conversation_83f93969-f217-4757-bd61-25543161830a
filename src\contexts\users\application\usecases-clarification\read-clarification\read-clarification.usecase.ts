import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Clarification } from '../../../domain/entities/clarification.entity';
import { Repository } from 'typeorm';
import { StorageS3Utils } from 'src/contexts/shared/utils/storageUtils/storageS3.utils';
import { ClarificationVO } from 'src/contexts/users/infrastructure/vo/clarification.vo';

@Injectable()
export class ReadClarificationUseCase {
  constructor(
    @InjectRepository(Clarification)
    private readonly clarificationRepo: Repository<Clarification>,
    private readonly s3: StorageS3Utils,
  ) {}

  async execute(params: {
    limit: number;
    offset: number;
  }): Promise<ClarificationVO[]> {
    const [clarifications] = await this.clarificationRepo.findAndCount({
      relations: ['createdBy', 'files'],
      take: params.limit,
      skip: params.offset,
      order: { createdAt: 'DESC' },
    });

    const results: ClarificationVO[] = [];

    for (const clarification of clarifications) {
      const fileData = await Promise.all(
        clarification.files.map(async (file) => {
          const url = await this.s3.generatePresignedUrl(file.file_url);

          return {
            id: file.id,
            file_name: file.file_name,
            file_url: url,
          };
        }),
      );

      results.push({
        trackingNumber: clarification.trackingNumber,
        type: clarification.type,
        description: clarification.description,
        status: clarification.status,
        createdAt: clarification.createdAt,
        user: {
          name: clarification.createdBy.name,
          email: clarification.createdBy.email,
          avatar: clarification.createdBy.email,
        },
        files: fileData,
      });
    }

    return results;
  }
}
