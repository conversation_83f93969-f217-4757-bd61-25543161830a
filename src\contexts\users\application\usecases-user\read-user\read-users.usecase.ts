import { Injectable } from '@nestjs/common';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { UserParser } from 'src/contexts/users/infrastructure/parsers/user.parser';
import { UsersRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/users.repository.impl';
import { ConveniUsersListVO } from 'src/contexts/users/infrastructure/vo/user.vo';
import { FilterConveniaUsersDto } from './read-user.dto';

@Injectable()
export class ReadUsersUseCase {
  constructor(private readonly usersRepositoryImpl: UsersRepositoryImpl) {}

  async execute(): Promise<ApiResponseDto> {
    return this.usersRepositoryImpl.findAll();
  }

  async executeConveniaUsers(
    filter: FilterConveniaUsersDto,
  ): Promise<ConveniUsersListVO> {
    const [data, count] =
      await this.usersRepositoryImpl.findAllConveniaUsers(filter);

    const users = data.map((user) => {
      const admin = user.relUserRoleAdmins[0]?.admin || null;

      return UserParser.parseToConveniaUser(
        user,
        user.relUserRoleAdmins.map((r) => r.role),
        admin,
      );
    });

    return { users, count };
  }
}
