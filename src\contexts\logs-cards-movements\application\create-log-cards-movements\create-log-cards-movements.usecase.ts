import { Injectable } from '@nestjs/common';
import { LogsCardsMovementsRepositoryImpl } from '../../infrastructure/repositories/logs-cards-movements.repository.impl';
import { CreateLogCardsMovementsDto } from './create-log-cards-movements.dto';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';

@Injectable()
export class CreateLogCardsMovementsUseCase {
  constructor(
    private readonly logsCardsMovementsRepositoryImpl: LogsCardsMovementsRepositoryImpl,
  ) {}

  async execute(createLogDto: CreateLogCardsMovementsDto): Promise<ApiResponseDto> {
    return await this.logsCardsMovementsRepositoryImpl.save(createLogDto);
  }
}