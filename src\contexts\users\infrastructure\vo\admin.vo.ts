import { <PERSON><PERSON><PERSON>y, <PERSON>Date, IsNumber, IsNumberString, IsOptional, IsString, IsUUID, ValidateNested } from "class-validator";
import { <PERSON>ressVO } from "./address.vo";
import { <PERSON><PERSON> } from "./manager.vo";
import { ApiProperty} from "@nestjs/swagger";
import { Type } from "class-transformer";
import { CountVO } from "src/contexts/shared/vo/count.vo";


export class BasicAdminVO {
    @ApiProperty()
    @IsUUID()
    id: string;

    @ApiProperty()
    @IsString()
    companyName: string;

    @ApiProperty()
    @IsString()

    alias: string;
    @ApiProperty()
    @IsString()
    rfc: string;

    @ApiProperty()
    @IsNumber()
    numAsignedCards: number;
    
    @ApiProperty()
    @IsNumberString()
    amount?: string | null;

    @ApiProperty()
    @IsNumber()
    groupId: number;

    @ApiProperty()
    @IsDate()
    createdAt: Date;

    @ApiProperty()
    @IsNumber()
    membershipNumber: number;
}
export class AdminDetailVO {
    @ApiProperty()
    @IsUUID()
    id: string;

    @ApiProperty()
    @IsString()
    companyName: string;

    @ApiProperty()
    @IsString()
    alias: string;

    @ApiProperty()
    @IsString()
    rfc: string;

    @ApiProperty()
    @IsNumber()
    numAsignedCards: number;

    @ApiProperty()
    @IsNumber()
    speiIn: number;

    @ApiProperty()
    @IsNumber()
    speiOut: number;

    @ApiProperty()
    @IsNumber()
    targetRefound: number;
    
    @ApiProperty()
    @IsString()
    ambassador: string;

    @ApiProperty()
    @IsNumber()
    groupId: number;

    @ApiProperty()
    @IsDate()
    createdAt: Date;

    @ApiProperty()
    @ValidateNested()
    @Type(() => ManagerVO)
    manager: ManagerVO;

    @ApiProperty()
    @ValidateNested()
    @Type(() => ManagerVO)
    address: AdressVO;

    @ApiProperty()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => File)
    files: File[];

    @ApiProperty()
    @IsNumberString()
    amount: string;

    @ApiProperty()
    isSucursal: boolean;
}

export class AdminVO extends BasicAdminVO{
    @ApiProperty()
    @ValidateNested()
    @Type(() => ManagerVO)
    manager: ManagerVO;
}

export class AdminListVO extends CountVO{
    @ApiProperty({ type: [AdminVO] })
    admins: AdminVO[];
}

export class AdminOnlyListVO {
    admins: AdminVO[];
}

export class AdminTotalAmount {
    @ApiProperty()
    @IsNumberString()
    totalAmount: string;
}


export class File {
    @ApiProperty()
    @IsString()
    type: string;

    @ApiProperty()
    @IsString()
    url: string;

    @ApiProperty()
    @IsNumber()
    id: number;
}