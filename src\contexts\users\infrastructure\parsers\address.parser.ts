import { Injectable } from "@nestjs/common";
import { Address } from "../../domain/entities/address.entity";
import { AdressVO } from "../vo/address.vo";

@Injectable()
export class AddressParser {

    static parseAddressToVO(address: Address) : AdressVO {
        return {
            id: address.id,
            state: address.state,
            city: address.city,
            colonia: address.colonia,
            street: address.street,
            numExt: address.num_ext,
            zipCode: address.zip_code
        }
    }
}
