import { Injectable } from '@nestjs/common';
import { ApiResponseDto } from 'src/contexts/shared/interfaces/dtos/api-response.dto';
import { AccountRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/account.repository.impl';
import { PersonRepositoryImpl } from 'src/contexts/users/infrastructure/repositories/person.repository.impl';
import { CreateAccountDto } from './create-account.dto';

@Injectable()
export class CreateAccountUseCase {
  constructor(private readonly accountRepositoryImpl: AccountRepositoryImpl) {}

  async execute(account: CreateAccountDto): Promise<ApiResponseDto> {
    return this.accountRepositoryImpl.save(account);
  }
}
